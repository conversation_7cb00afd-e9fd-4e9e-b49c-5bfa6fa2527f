const resolvers = {
    Mutation : {
        updateEducation : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        if (
                            args.Year_Of_Passing &&
                            (!/^\d{4}$/.test(args.Year_Of_Passing) || 
                              args.Year_Of_Passing < new Date().getFullYear() - 100)
                          ) {
                            throw new Error('Invalid year of passing');
                          }
                        return knexconfig('candidate_education')
                        .where('Education_Id',args.Education_Id)
                        .andWhere('Candidate_Id',url[0].Candidate_Id)      
                        .then((details)=>{
                            if(!details[0]){
                                throw new Error("Education Detail not found");
                            } else {
                                return knexconfig('candidate_education')
                                .where('Education_Id',args.Education_Id)
                                .andWhere('Candidate_Id',url[0].Candidate_Id)
                                .update({
                                    Education_Type: args.Education_Type,
                                    Specialisation: (!args.Specialisation) ? (null) : (args.Specialisation),
                                    Institute_Name: (!args.Institute_Name) ? (null) : (args.Institute_Name),
                                    University: (!args.University) ? (null) : (args.University),
                                    Year_Of_Start: (!args.Year_Of_Start) ? '' : (args.Year_Of_Start),
                                    Year_Of_Passing: args.Year_Of_Passing,
                                    Percentage:  (!args.Percentage) ? (null) : (args.Percentage),
                                    Grade:  (!args.Grade) ? (null) : (args.Grade),
                                    Specialization_Id:  (!args.Specialization_Id) ? (null) : (args.Specialization_Id),
                                    Institution_Id: (!args.Institution_Id) ? (null) : (args.Institution_Id),
                                    Start_Date: (!args.Start_Date) ? (null) : (args.Start_Date),
                                    End_Date: (!args.End_Date) ? (null) : (args.End_Date),
                                    City: (!args.City) ? (null) : (args.City),
                                    State: (!args.State) ? (null) : (args.State),
                                    Country: (!args.Country) ? (null) : (args.Country)
                                })
                                .transacting(trc)
                                .then(()=>{
                                    return knexconfig('candidate_education_documents')
                                    .where('Education_Id',args.Education_Id)
                                    .then((document_exist)=>{
                                        if(!document_exist[0]){
                                            if(!args.File_Name){
                                                return {
                                                    message:"Education details updated"
                                                }
                                            } else {
                                                return knexconfig('candidate_education_documents')
                                                .insert({
                                                    Education_Id: args.Education_Id,
                                                    File_Name: args.File_Name,
                                                    Document_Name: args.Document_Name,
                                                    Sub_Type_Id: args.Sub_Type_Id
                                                })
                                                .transacting(trc)
                                                .then(()=>{
                                                    return {
                                                        message:"Education details updated"
                                                    }
                                                })
                                            }
                                        } else {
                                            if(!args.File_Name){
                                                return knexconfig('candidate_education_documents')
                                                .where('Education_Id',args.Education_Id)
                                                .del()
                                                .transacting(trc)
                                                .then(()=>{
                                                    return {
                                                        message:"Education details updated"
                                                    }
                                                })
                                            } else {
                                                return knexconfig('candidate_education_documents')
                                                .where('Education_Id',args.Education_Id)
                                                .update({
                                                    File_Name: args.File_Name,
                                                    Document_Name: args.Document_Name,
                                                    Sub_Type_Id: args.Sub_Type_Id
                                                })
                                                .transacting(trc)
                                                .then(()=>{
                                                    return {
                                                        message:"Education details updated"
                                                    }
                                                })
                                            }
                                        }
                                    })
                                })
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from updateEducation');
                return result;
            }).catch(function(err){
                console.log('Error in updateEducation',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Education Type should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Year Of Passing should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Invalid year of passing"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Education Detail not found"){
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;