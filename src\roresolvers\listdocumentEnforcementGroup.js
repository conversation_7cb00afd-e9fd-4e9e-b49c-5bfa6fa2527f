//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');


module.exports.listdocumentEnforcementGroup = async (parent, args, context, info) => {
    console.log('Inside listdocumentEnforcementGroup function', args);
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

       const groupIdList = await  organizationDbConnection(ehrTables.documentEnforcementGroup)
       .select('Group_Name','Group_Id');

        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return {errorCode: '', groupIds: groupIdList, message: 'Retrieve Group_Id details list of records'}
     
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listdocumentEnforcementGroup function main catch block.', e);
        let errResult = commonLib.func.getError(e, '_UH0001');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
