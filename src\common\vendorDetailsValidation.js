//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

//Function to validate basic vendor data

module.exports.vendorDetailsValidation = async (args, isUpdate) => {
    console.log('Inside vendorDetailsValidation function');
    try {
        let validationError = {};

        if (args.vendorId) {
            //Validate the invited vendor id
            if (!(args.vendorId || commonLib.commonValidation.numberValidation(args.vendorId)) || args.invitedVendorId < 1) {
                validationError['IVE0253'] = commonLib.func.getError('', 'IVE0253').message1;
                throw '_EC0007'
            }
        }

        //Validate the vendor type
        if (args.vendorType) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.vendorType)) {
                validationError['IVE0253'] = commonLib.func.getError('', 'IVE0253').message2;
            } 
            else if (!commonLib.commonValidation.checkLength(args.vendorType, 3, 30)) {
                validationError['IVE0253'] = commonLib.func.getError('', 'IVE0253').message3;
            }
        } else {
            validationError['IVE0253'] = commonLib.func.getError('', 'IVE0253').message4;
        }

        //Validate the vendor name
        if (args.vendorName) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.vendorName)) {
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message2;
            } 
            else if (!commonLib.commonValidation.checkLength(args.vendorName, 3, 50)) {
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message3;
            }
        } else {
            validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message4;
        }

        //validate business number
        if (args.businessNumber && !isUpdate) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.businessNumber)) {
                validationError['IVE0253'] = commonLib.func.getError('', 'IVE0253').message7;
            }
            else if (!commonLib.commonValidation.checkLength(args.businessNumber, 11, 30)) {
                validationError['IVE0253'] = commonLib.func.getError('', 'IVE0253').message6;
            }
        } else{
            if(!isUpdate){
                validationError['IVE0253'] = commonLib.func.getError('', 'IVE0253').message5;
            }
        }

        //Validate the trustee name
        if (args.trusteeName) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.trusteeName)) {
                validationError['IVE0253'] = commonLib.func.getError('', 'IVE0253').message8;
            } 
            else if (!commonLib.commonValidation.checkLength(args.trusteeName, 3, 50)) {
                validationError['IVE0253'] = commonLib.func.getError('', 'IVE0253').message9;
            }
        }

        //validate the vendor email
        if (args.vendorEmail) {
            if (!commonLib.commonValidation.emailValidation(args.vendorEmail)) {
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message5;
            }
            else if (!commonLib.commonValidation.checkLength(args.vendorEmail, 3, 50)) {
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message6;
            }
        }else{
            validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message7;
        }

        //validate the telephone number
        if (args.telephone) {
            if(!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.telephone)){
                validationError['IVE0253'] = commonLib.func.getError('', 'IVE0253').message10;
            }
            else if(!commonLib.commonValidation.checkLength(args.telephone, 1, 30)){
                validationError['IVE0253'] = commonLib.func.getError('', 'IVE0253').message11;
            }
        }

        return validationError;
    }
    catch (err) {
        console.log('Error in the vendorDetailsValidation() function in the main catch block.', err);
        throw err;
    }
}
