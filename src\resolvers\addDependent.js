const resolvers = {
    Mutation : {
        addDependent : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');
            var dateFormat = require('dateformat');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        Candidate_Id = url[0].Candidate_Id;
                        if(!args.Dependent_First_Name) {
                            throw new Error("Dependent First Name should not be empty");
                        } else if(args.Dependent_First_Name.length > 50) {
                            throw new Error("Dependent First Name should be less than 50 characters");
                        } else if(!args.Dependent_Last_Name) {
                            throw new Error("Dependent Last Name should not be empty");
                        } else if(args.Dependent_Last_Name.length > 50) {
                            throw new Error("Dependent Last Name should be less than 50 characters");
                        } else if(!args.Relationship) {
                            throw new Error("Relationship should not be empty");
                        } else if(!args.Dependent_DOB) {
                            throw new Error("Dependent DOB should not be empty");
                        }
                        try {
                            var Dependent_DOB = (!args.Dependent_DOB || isNaN(Date.parse(args.Dependent_DOB))) ? (null) : ((isNaN(Number(args.Dependent_DOB))) ? dateFormat(new Date((args.Dependent_DOB)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Dependent_DOB)), "yyyy-mm-dd"));
                        }
                        catch(err){
                            throw new Error("Invalid Date Format")
                        }

                        return knexconfig('candidate_dependent')
                        .insert({
                            Candidate_Id: url[0].Candidate_Id,
                            Dependent_First_Name: args.Dependent_First_Name,
                            Dependent_Last_Name: args.Dependent_Last_Name,
                            Gender: (!args.Gender) ? (null) : (args.Gender),
                            Gender_Id: args.Gender_Id,
                            Relationship: args.Relationship,
                            Dependent_DOB: Dependent_DOB
                        })
                        .transacting(trc)
                        .then(()=>{
                            return {
                                message:"Dependent added"
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return knexconfig('candidate_dependent')
                .select('candidate_dependent.*')
                .where('Candidate_Id',Candidate_Id)
                .then((dependent)=>{
                    if(!dependent[0]){
                        console.log('return successful response from addDependent');
                        return {
                            dependent: null
                        }
                    } else {
                        var newDependent = dependent.map((arr)=>{ 
                            arr['Dependent_DOB']  = dateFormat(new Date(arr['Dependent_DOB']), "yyyy-mm-dd");
                            return arr;
                        });
                        console.log('return successful response from addDependent');
                        return {
                            dependent: newDependent
                        }
                    }
                })
            }).catch(function(err){
                console.log('Error in addDependent',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Dependent First Name should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Dependent First Name should be less than 50 characters"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Dependent Last Name should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Dependent Last Name should be less than 50 characters"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Relationship should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Dependent DOB should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Invalid Date Format"){
                    throw new UserInputError(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;