const resolvers = {
    Mutation : {
        deleteCertification : async (root,args) =>{

            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');
            var dateFormat = require('dateformat');

            var Candidate_Id;
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        Candidate_Id = url[0].Candidate_Id;
                        return knexconfig('candidate_certifications')
                        .where('Certification_Id',args.Certification_Id)
                        .andWhere('Candidate_Id',Candidate_Id)
                        .then((certificate)=>{
                            if(!certificate[0]){
                                throw new Error("Record not found");
                            } else {
                                return knexconfig('candidate_certifications')
                                .where('Certification_Id',args.Certification_Id)
                                .del()
                                .transacting(trc)
                                .then(()=>{
                                    return knexconfig('candidate_certifications_documents')
                                    .where('Certification_Id',args.Certification_Id)
                                    .then((document_exist)=>{
                                        if(!document_exist[0]){
                                            return {
                                                message:"certificate details deleted"
                                            }
                                        } else {
                                            return knexconfig('candidate_certifications_documents')
                                            .where('Certification_Id',args.Certification_Id)
                                            .del()
                                            .transacting(trc)
                                            .then(()=>{
                                                return {
                                                    message:"certificate details deleted"
                                                }
                                            })
                                        }
                                    })
                                })
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return knexconfig.select('candidate_certifications.*',
                                        'candidate_certifications_documents.File_Name',
                                        'candidate_certifications_documents.Document_Name',
                                        'candidate_certifications_documents.Sub_Type_Id')
                .from('candidate_certifications')
                .leftJoin('candidate_certifications_documents','candidate_certifications.Certification_Id','candidate_certifications_documents.Certification_Id')
                .where('candidate_certifications.Candidate_Id',Candidate_Id)
                .then((certificates)=>{
                    if(!certificates[0]){
                        return {
                            certificates: null
                        }
                    } else {
                        var newCertificates = certificates.map((arr)=>{ 
                            arr['Received_Date']  = dateFormat(new Date(arr['Received_Date']), "yyyy-mm-dd");
                            return arr;
                        });
                        return {
                            certificates: newCertificates
                        }
                    }
                })
            }).catch(function(err){
                console.log('Error in deleteCertification',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Record not found"){
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;