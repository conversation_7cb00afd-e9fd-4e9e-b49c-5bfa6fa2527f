<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>Self Onboard</title>
  <style type="text/css">
  body {
   padding-top: 0 !important;
   padding-bottom: 0 !important;
   padding-top: 0 !important;
   padding-bottom: 0 !important;
   margin:0 !important;
   width: 100% !important;
   -webkit-text-size-adjust: 100% !important;
   -ms-text-size-adjust: 100% !important;
   -webkit-font-smoothing: antialiased !important;
 }
 .tableContent img {
   border: 0 !important;
   display: block !important;
   outline: none !important;
 }
 a{
  color:#382F2E;
}

p, h1{
  color:#382F2E;
  margin:0;
}
p{
  text-align:left;
  color:#999999;
  font-size:14px;
  font-weight:normal;
  line-height:19px;
}


a.link1{
  color:#382F2E;
}
a.link2{
  font-size:16px;
  text-decoration:none;
  color:#ffffff;
}

h2{
  text-align:left;
   color:#222222; 
   font-size:19px;
  font-weight:normal;
}
div,p,ul,h1{
  margin:0;
}

.bgBody{
  background: #ffffff;
}
.bgItem{
  background: #ffffff;
}

.a6S {display: none !important;}
@media only screen and (max-width:480px)
    
{
    
table[class="MainContainer"], td[class="cell"] 
{
    width: 100% !important;
    height:auto !important; 
}
td[class="specbundle"] 
{
    width:100% !important;
    float:left !important;
    font-size:13px !important;
    line-height:17px !important;
    display:block !important;
    padding-bottom:15px !important;
}
    
td[class="spechide"] 
{
    display:none !important;
}
    img[class="banner"] 
{
          width: 100% !important;
          height: auto !important;
}
    td[class="left_pad"] 
{
        padding-left:15px !important;
        padding-right:15px !important;
}
     
}

@media only screen and (max-width:540px) 

{
    
table[class="MainContainer"], td[class="cell"] 
{
    width: 100% !important;
    height:auto !important; 
}
td[class="specbundle"] 
{
    width:100% !important;
    float:left !important;
    font-size:13px !important;
    line-height:17px !important;
    display:block !important;
    padding-bottom:15px !important;
}
    
td[class="spechide"] 
{
    display:none !important;
}
    img[class="banner"] 
{
          width: 100% !important;
          height: auto !important;
}
.font {
    font-size:18px !important;
    line-height:22px !important;
    
    }
    .font1 {
    font-size:18px !important;
    line-height:22px !important;
    
    }
}

</style>
<script type="colorScheme" class="swatch active">
{
"name":"Default",
"bgBody":"ffffff",
"link":"382F2E",
"color":"999999",
"bgItem":"ffffff",
"title":"222222"
}
</script>
</head>
<body paddingwidth="0" paddingheight="0"   style="padding-top: 0; padding-bottom: 0; padding-top: 0; padding-bottom: 0; background-repeat: repeat; width: 100% !important; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; -webkit-font-smoothing: antialiased;" offset="0" toppadding="0" leftpadding="0">
<table bgcolor="#ffffff" width="100%" border="0" cellspacing="0" cellpadding="0" class="tableContent" align="center"  style='font-family:Helvetica, Arial,serif;'>
<tbody>
<tr>
  <td><table width="600" border="0" cellspacing="0" cellpadding="0" align="center" bgcolor="#ffffff" class="MainContainer">
<tbody>
<tr>
  <td><table width="100%" border="0" cellspacing="0" cellpadding="0">
<tbody>
<tr>
  <td valign="top" width="40">&nbsp;</td>
  <td><table width="100%" border="0" cellspacing="0" cellpadding="0">
<tbody>
<!-- =============================== Header ====================================== -->   
<tr>
    <div>
      <tr style="background:#FFFFFF">
        <td align="center" valign="top">          
          <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer">
            <tr>
              <td valign="top" class="preheaderContainer">
                <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnImageBlock" style="min-width:100%;">
                  <tbody class="mcnImageBlockOuter">
                    <tr>
                      <td valign="top" style="padding:9px" class="mcnImageBlockInner">
                        <table align="left" width="100%" border="0" cellpadding="0" cellspacing="0" class="mcnImageContentContainer" style="min-width:100%;">
                          <tbody>
                            <tr>
                              <td class="mcnImageContent" valign="top" style="text-align:center;">
                                <!-- <a href="" style="cursor:default;"> -->
                                  <img align="center" alt="" class = "a6S" src={{orgLogo}} style="max-width:200px;height: auto;margin: 30px; padding-bottom: 0; display: inline !important; vertical-align: bottom; width: 250px;">
                                <!-- </a> -->
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
          </table>          
        </td>
      </tr>
    </div>
  </tr>
    
    <!-- =============================== Body ====================================== -->
<tr>
  <td class='movableContentContainer ' valign='top'>
    <div class="movableContent" style="border: 0px; padding-top: 0px; position: relative;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
                      <tr>
                        <td align='left'>
                          <div class="contentEditableContainer contentTextEditable">
                            <div class="contentEditable" align='center'>
                              <h2 >Authentication pin for your onboarding process has been changed to {{pin}}. Use this pin for authentication and fill your details.</h2>
                            </div>
                          </div>
                        </td>
                      </tr>

                      <tr><td height='15'> </td></tr>

                      <tr>
                        <td align='center'>
                          <table>
                            <tr>
                              <td align='center' bgcolor='#00263E' id="button-backround" style='background: #00263E; padding:15px 18px;-webkit-border-radius: 4px; -moz-border-radius: 4px; border-radius: 30px;width: 113px;'>
                                <div class="contentEditableContainer contentTextEditable">
                                  <div class="contentEditable" align='center'>
                                    <a target='_blank' href='{{url}}' class='link2' style='color:#ffffff;'>Launch</a>
                                  </div>
                                </div>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      <tr><td height='20'></td></tr>
                    </table>
    </div>
    <div class="movableContent" style="border: 0px; padding-top: 0px; position: relative;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
<tbody>
<tr>
  <td height='65'>
</tr>
<tr>
  <td  style='border-bottom:1px solid #DDDDDD;'></td>
</tr>
<tr><td height='25'></td></tr>
<tr>
  <td><table width="100%" border="0" cellspacing="0" cellpadding="0">
<tbody>
<tr>
 
  <td valign="top" width="30" class="specbundle">&nbsp;</td>
  <td valign="top" class="specbundle"><table width="100%" border="0" cellspacing="0" cellpadding="0">

</table>
</td>
</tr>
</tbody>
</table>
</td>
</tr>
<tr><td height='88'></td></tr>
</tbody>
</table>

    </div>
    
    <!-- =============================== footer ====================================== -->
  
  </td>
</tr>
</tbody>
</table>
</td>
  <td valign="top" width="40">&nbsp;</td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
<script>
  function applyStyles(elementId, bgColor, textColor) {
      var element = document.getElementById(elementId);
      if (element) {
        if(bgColor){
          element.style.backgroundColor = bgColor;
        }
        
        if(textColor){
          element.style.color = textColor;
        }
      } else {
          console.error('Element with ID ' + elementId + ' not found.');
      }
  }
  // Define variables for styles
  var backgroundColor = bgColors;
  var fontColor = fontColors;
  // Apply styles to the element
  applyStyles('button-backround', backgroundColor, fontColor);
</script>
  </body>
  </html>


