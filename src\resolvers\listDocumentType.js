const resolvers = {
    Query : {
        listDocumentType : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0] && !args.Vendor_Based){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig("document_type")
                        .where('Category_Id',args.Category_Id)
                        .then((type)=>{
                            if(!type[0]){
                                return {
                                    documentType: null
                                }
                            } else{
                                return {
                                    documentType: type
                                }
                            }
                        })
                    }
                })
                .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listDocumentType')
                return result;
            }).catch(function(err){
                console.log('Error in listDocumentType',err);
                if (err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;