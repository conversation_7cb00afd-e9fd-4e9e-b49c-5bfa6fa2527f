{"securityGroupIds": ["sg-06e06ee52057fb09f"], "subnetIds": ["subnet-023ff1fb8431b273f", "subnet-09dd9cf2a9239643c"], "KmsKeyId": "arn:aws:kms:ap-south-1:378423228887:key/cefd894a-4f8a-436c-b136-4e516dceb0fe", "role": "arn:aws:iam::378423228887:role/lambdaFullAccess", "secretName": "PROD/CANNY/PGACCESS", "imageBucket": "s3.images.cannyhr.com", "documentBucket": "s3.taxdocs.cannyhr.com", "baseUrlUI": "http://onboard.cannyhr.com?companyName=", "emailFrom": "<EMAIL>", "documentsBucket": "s3.taxdocs.cannyhr.com", "emailReplyTo": "<EMAIL>", "sesRegion": "us-east-1", "logoBucket": "s3.logos.cannyhr.com", "domainName": "cannyhr", "fullDomainName": ".cannyhr.com", "customDomainName": "onboardapi.cannyhr.com", "commonAPIDomainName": "api.cannyhr.com", "dbPrefix": "cannyhr_", "baseAUUrlUI": "http://onboard.cannyhr.com?companyName=", "vendorBucketName": "supplier.cannyhr.com", "authorizerARN": "arn:aws:lambda:ap-south-1:378423228887:function:ATS-cannyhr-firebaseauthorizer", "resendInviteVendorStepFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhrResendInviteVendorStepFunction", "resendInviteCandidateStepFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhrResendInviteCandidateStepFunction", "asyncSyntrumAPIStepFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-asyncSyntrumAPIFunction", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:378423228887:function:HRAPPOnboard-cannyhr", "snsRegion": "ap-southeast-1", "snsEndpoint": "sns.ap-southeast-1.amazonaws.com"}