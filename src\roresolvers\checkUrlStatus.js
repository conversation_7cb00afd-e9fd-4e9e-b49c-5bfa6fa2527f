//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const moment = require('moment');

//fuction to check the status of the url
let organizationDbConnection;
module.exports.checkUrlStatus = async (parent, args, context, info) => {
    console.log('Inside checkUrlStatus function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
            organizationDbConnection(ehrTables.invitedVendors)
            .select("Url_Hash as urlHash", "Expire_Time as expireTime", "Status as status", "Vendor_Id as vendorId")
            .from(ehrTables.invitedVendors)
            .where("Url_Hash", args.urlHash)
                .then((data) => {
                    if(!data[0]){
                        throw 'VO0109';
                    }
                    const expireTimeUTC = moment.utc(data[0].expireTime, 'YYYY-MM-DD HH:mm:ss');
                    const currentUTC = moment.utc();
                    if (expireTimeUTC.isBefore(currentUTC)) {
                        throw 'VO0110';
                    }else if(data[0].status == 'Completed'){
                        throw 'VO0111';
                    }
                    else{
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Url status is active", vendorId: data[0].vendorId };
                    }
                    })
                .catch((catchError) => {
                    console.log('Error in checkUrlStatus .catch() block', catchError);
                    errResult = commonLib.func.getError(catchError, 'VO0112');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in checkUrlStatus function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'VO0005');
        throw new ApolloError(errResult.message, errResult.code)
    }
}