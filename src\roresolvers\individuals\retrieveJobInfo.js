// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appConstants');
const { fetchJobRoleDetails } = require('../../common/commonFunctions');

module.exports.retrieveJobInfo = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveJobInfo function.");
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const data = await getJobInfo(organizationDbConnection, args);
        if (data) {
            const { jobInfoDetails, experienceDetails, assetDetails } = data;
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return {
                errorCode: "",
                message: "Candidate job info details retrieved successfully.",
                jobInfoDetails: JSON.stringify(jobInfoDetails),
                experienceDetails: JSON.stringify(experienceDetails),
                assetDetails: JSON.stringify(assetDetails)
            };
        } else {
            throw 'IO0107'
        }
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveJobInfo function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'IO0008');
        throw new ApolloError(errResult.message, errResult.code);
    }
};

async function getJobInfo(organizationDbConnection, args) {
    try {
        let [jobInfoDetails, experienceDetails, assetDetails] = await Promise.all([
            organizationDbConnection(ehrTables.candidateJob + " as EJ")
                .select("EJ.*", "ER.ESIC_Reason as Relieving_Reason", "R.Roles_Name", "EO.Field_Force",
                    organizationDbConnection.raw("CASE WHEN DES.Designation_Code IS NOT NULL THEN CONCAT(DES.Designation_Code,' - ',DES.Designation_Name) ELSE DES.Designation_Name END AS Designation_Name"),
                    organizationDbConnection.raw(`
                    CASE 
                        WHEN DEP.Department_Code IS NOT NULL 
                        THEN CONCAT(DEP.Department_Code, ' - ', DEP.Department_Name) 
                        ELSE DEP.Department_Name 
                    END AS Department_Name
                `),
                    organizationDbConnection.raw(
                        "CASE WHEN BU.Business_Unit_Code IS NOT NULL THEN CONCAT(BU.Business_Unit_Code, ' - ', BU.Business_Unit) ELSE BU.Business_Unit END AS Business_Unit"
                    ),
                    organizationDbConnection.raw(`
                    CASE 
                        WHEN SP.Service_Provider_Code IS NOT NULL 
                        THEN CONCAT(SP.Service_Provider_Code, ' - ', SP.Service_Provider_Name) 
                        ELSE SP.Service_Provider_Name 
                    END AS Service_Provider_Name
                `),
                    organizationDbConnection.raw(`
                    CASE 
                        WHEN OG.Organization_Group_Code IS NOT NULL 
                        THEN CONCAT(OG.Organization_Group_Code, ' - ', OG.Organization_Group) 
                        ELSE OG.Organization_Group 
                    END AS Organization_Group
                `),
                    organizationDbConnection.raw(`
                    CASE 
                        WHEN L.Location_Code IS NOT NULL 
                        THEN CONCAT(L.Location_Code, ' - ', L.Location_Name) 
                        ELSE L.Location_Name 
                    END AS Location_Name
                `),
                    organizationDbConnection.raw(`
                    CASE 
                        WHEN ET.Employee_Type_Code IS NOT NULL 
                        THEN CONCAT(ET.Employee_Type_Code, ' - ', ET.Employee_Type) 
                        ELSE ET.Employee_Type 
                    END AS Employee_Type
                `),
                'EJ.Job_Role_Ids',
                    "WS.Title as Work_Schedule_Name", "EMP.Profession_Name", organizationDbConnection.raw('CONCAT_WS(" ",EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as Manager_Name'),)
                .leftJoin(ehrTables.roles + " as R", "R.Roles_Id", "EJ.Roles_Id")
                .leftJoin(ehrTables.designation + " as DES", "DES.Designation_Id", "EJ.Designation_Id")
                .leftJoin(ehrTables.department + " as DEP", "DEP.Department_Id", "EJ.Department_Id")
                .leftJoin(ehrTables.location + " as L", "L.Location_Id", "EJ.Location_Id")
                .leftJoin(ehrTables.employeeType + " as ET", "ET.EmpType_Id", "EJ.EmpType_Id")
                .leftJoin(ehrTables.workSchedule + " as WS", "WS.WorkSchedule_Id", "EJ.Work_Schedule")
                .leftJoin(ehrTables.empPersonalInfo + " as EP", 'EJ.Manager_Id', 'EP.Employee_Id')
                .leftJoin(ehrTables.empProfession + " as EMP", "EMP.Profession_Id", "EJ.Emp_Profession")
                .leftJoin(ehrTables.serviceProvider + " as SP", "SP.Service_Provider_Id", "EJ.Service_Provider_Id")
                .leftJoin(ehrTables.businessUnit + " as BU", "BU.Business_Unit_Id", "EJ.Business_Unit_Id")
                .leftJoin(ehrTables.esicReason + " as ER", "ER.Reason_Id", "EJ.Reason_Id")
                .leftJoin(ehrTables.organizationGroup + " as OG", "OG.Organization_Group_Id", "EJ.Organization_Group_Id")
                .innerJoin(ehrTables.orgDetails + " as EO")
                .where('EJ.Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidateExperience + " as EE")
                .select("EE.*", "EED.File_Name", "EER.Reference_Name", "EER.Reference_Number", "EER.Reference_Email")
                .leftJoin(ehrTables.candidateExperienceReference + " as EER", "EER.Experience_Id", "EE.Experience_Id")
                .leftJoin(ehrTables.candidateExperienceDocuments + " as EED", "EED.Experience_Id", "EE.Experience_Id")
                .where('EE.Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidateAssets)
                .select('*')
                .where('Candidate_Id', args.candidateId)
        ]);

        jobInfoDetails = await fetchJobRoleDetails(jobInfoDetails, organizationDbConnection);

        //Group experiences by Experience_Id
        const groupedExperience = experienceDetails.reduce((acc, record) => {
            const experienceId = record.Experience_Id;
            if (!acc[experienceId]) {
                acc[experienceId] = {
                    ...record,
                    Experience_Reference: [],
                };

            }

            // Add references to the array
            if (record.Reference_Name?.length) {
                acc[experienceId].Experience_Reference.push({
                    Reference_Email: record.Reference_Email,
                    Reference_Name: record.Reference_Name,
                    Reference_Number: record.Reference_Number,
                });
            }

            //Remove 
            delete acc[experienceId]?.Reference_Name;
            delete acc[experienceId]?.Reference_Number;
            delete acc[experienceId]?.Reference_Email;

            return acc;
        }, {});

        let updatedExperienceDetails = Object.values(groupedExperience);

        return {
            jobInfoDetails,
            experienceDetails: updatedExperienceDetails,
            assetDetails
        };
    } catch (err) {
        console.log('Error in getJobInfo main function', err);
        throw err
    }
}
