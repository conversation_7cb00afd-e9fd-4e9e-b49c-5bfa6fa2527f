//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')

//function to add / update contact details
module.exports.updateContactDetails = async (parent, args, context, info) => {
    console.log('Inside updateContactDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            const fieldValidations = {
                permanent_appartmentName: "IVE0338",
                permanent_streetName: "IVE0339",
                permanent_city: "IVE0340",
                permanent_state: "IVE0341",
                permanent_country: "IVE0342",
                permanent_pinCode: "IVE0343",
                landlineNo: "IVE0344",
                mobileNo: "IVE0345",
                mobileNoCountryCode: "IVE0346",
                current_appartmentName: "IVE0347",
                current_streetName: "IVE0348",
                current_city: "IVE0349",
                current_state: "IVE0350",
                current_country: "IVE0351",
                current_pinCode: "IVE0352",
                office_appartmentName: "IVE0353",
                office_streetName: "IVE0354",
                office_city: "IVE0355",
                office_state: "IVE0356",
                office_country: "IVE0357",
                office_pinCode: "IVE0358",
            };
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let contactData = {
                    Candidate_Id: args.candidateId,
                    pApartment_Name: args.permanent_appartmentName,
                    pStreet_Name: args.permanent_streetName,
                    pCity: args.permanent_city,
                    pCity_Id: args.permanent_city_id || null,
                    pState: args.permanent_state,
                    pBarangay: args.permanent_barangay || null,
                    pBarangay_Id: args.permanent_barangay_id || null,
                    pRegion: args.permanent_region || null,
                    cCity: args.current_city,
                    cCity_Id: args.current_city_id || null,
                    cBarangay: args.current_barangay || null,
                    cBarangay_Id: args.current_barangay_id || null,
                    cRegion: args.current_region || null,
                    oCity: args.office_city,
                    oCity_Id: args.office_city_id || null,
                    oBarangay: args.office_barangay || null,
                    oBarangay_Id: args.office_barangay_id || null,
                    oRegion: args.office_region || null,
                    pCountry: args.permanent_country,
                    pPincode: args.permanent_pinCode,
                    cApartment_Name: args.current_appartmentName,
                    cStreet_Name: args.current_streetName,
                    cCity: args.current_city,
                    cState: args.current_state,
                    cCountry: args.current_country,
                    cPincode: args.current_pinCode,
                    oApartment_Name: args.office_appartmentName,
                    oStreet_Name: args.office_streetName,
                    oCity: args.office_city,
                    oState: args.office_state,
                    oCountry: args.office_country,
                    oPincode: args.office_pinCode,
                    Land_Line_No: args.landlineNo,
                    Mobile_No: args.mobileNo,
                    Mobile_No_Country_Code: args.mobileNoCountryCode,
                    Use_Location_Address: args.useLocationAddress ? args.useLocationAddress : 0,
                    Fax_No: args.faxNo,
                    Emergency_Contact_Name: args.emergencyContactName,
                    Emergency_Contact_Relation: args.emergencyContactRelation
                }
                //Validating contact detail already exists
                await validateMobileNo(organizationDbConnection, args)
                return (
                    organizationDbConnection(ehrTables.candidateContactDetails)
                        .update(contactData)
                        .where('Candidate_Id', contactData.Candidate_Id)
                        .then(async (updateContact) => {
                            if (updateContact) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The candidate contact details has been updated.`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);
                                organizationDbConnection ? organizationDbConnection.destroy() : null;

                                return { errorCode: "", message: "Candidate Contact details has been updated successfully" };
                            } else {
                                throw 'IO0113';
                            }

                        }).catch((catchError) => {
                            console.log('Error in updateContactDetails .catch block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0113');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                throw 'IVE0000';
            }
        } else {
            throw '_DB0102';
        }
    } catch (mainCatchError) {
        console.log('Error in updateContactDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in updateContactDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0013');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

async function validateMobileNo(organizationDbConnection, args) {
    try {
        return (
            organizationDbConnection(ehrTables.contactDetails + " as CD")
                .select("CD.Employee_Id")
                .innerJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "CD.Employee_Id")
                .where('CD.Mobile_No', args.mobileNo)
                .where('CD.Mobile_No_Country_Code', args.mobileNoCountryCode)
                .where("EJ.Emp_Status", "Active")
                .then((data) => {
                    if (data && data.length) {
                        throw 'IO0114'
                    } else {
                        return true
                    }
                })
                .catch((err) => {
                    console.log('Error in validateMobileNo .catch function', err)
                    throw err
                })
        )
    } catch (err) {
        console.log('Error in validateMobileNo main function', err)
        throw err
    }
}