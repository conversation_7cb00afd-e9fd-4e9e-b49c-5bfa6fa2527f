const resolvers = {
    Query : {
        getDependent : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');
            var dateFormat = require('dateformat');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig('candidate_dependent')
                        .select('candidate_dependent.*')
                        .where('Candidate_Id',url[0].Candidate_Id)
                        .then(async (dependent)=>{

                            const relation = await knexconfig('candidate_relation_details')
                            .select('candidate_relation_details.*').where('Candidate_Id',url[0].Candidate_Id)

                            if(!dependent[0]){
                                return {
                                    dependent: null,
                                    relation: relation
                                }
                            } else {
                                var newDependent = dependent.map((arr)=>{ 
                                    arr['Dependent_DOB']  = dateFormat(new Date(arr['Dependent_DOB']), "yyyy-mm-dd");
                                    return arr;
                                });
                                return {
                                    dependent: newDependent,
                                    relation: relation
                                }
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return result;
            }).catch(function(err){
                console.log('Error in getDependent',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;