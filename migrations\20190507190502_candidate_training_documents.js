
exports.up = function(knex, Promise) {
    return knex.schema.createTable("candidate_training_documents",function(table){
        table.increments();
        table.integer('Training_Id').notNullable();
        table.string('File_Hash',300).notNullable();
        table.string('File_Name',50).notNullable();
        table.integer('Sub_Type_Id').notNullable();
  })
};

exports.down = function(knex, Promise) {
    return knex.schema.dropTable('candidate_training_documents');
};
