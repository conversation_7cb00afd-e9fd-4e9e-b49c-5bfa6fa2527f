// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
var dbConnection = require('../resolvers/dbConnection');
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
var ehrTables = require('../common/tableAlias').ehrTables;

module.exports.retrieveListEduInstitutionAndSpecialization = async (parent, args, context, info) => {
    let organizationDbConnection;
    console.log("Inside retrieveListEduInstitutionAndSpecialization function.")
    try {
       
        //initiate transaction in organizationDb
        if(args && args.Org_Code){
            var connection = await dbConnection.getConnection(args.Org_Code);
            organizationDbConnection = knex(connection);
        }else{
            organizationDbConnection = knex(context.connection.OrganizationDb);
        }

         const [institution, specialization] =  await Promise.all([
            organizationDbConnection(ehrTables.eduInstitution).select('Institution_Id', 'Institution_Code', 'Institution').orderBy('Institution', 'asc'), 
            organizationDbConnection(ehrTables.eduSpecialization).select('Specialization_Id', 'Specialization_Code', 'Specialization').orderBy('Specialization', 'asc')]);
        //destroy the connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        // Push the details into JSON
        return { errorCode: '',  institution: institution,  specialization: specialization, message: 'Education institution and specialization retrieved successfully.'}
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveListEduInstitutionAndSpecialization function main catch block.', e);
        let errResult = commonLib.func.getError(e, '_UH0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}