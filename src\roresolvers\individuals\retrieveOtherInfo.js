// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appConstants');

module.exports.retrieveOtherInfo = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveOtherInfo function.");
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const data = await getOtherInfo(organizationDbConnection, args);
        if (data) {
            const { bankDetails, insuranceDetails } = data;
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return {
                errorCode: "",
                message: "Candidate Other info details retrieved successfully.",
                bankDetails: JSON.stringify(bankDetails),
                insuranceDetails: JSON.stringify(insuranceDetails),
            };
        } else {
            throw 'IO0124'
        }
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveOtherInfo function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'IO0023');
        throw new ApolloError(errResult.message, errResult.code);
    }
};

async function getOtherInfo(organizationDbConnection, args) {
    try {
        const [bankDetails, insuranceDetails] = await Promise.all([
            organizationDbConnection(ehrTables.candidateBankDetails + " as EBD")
                .select("EBD.*", "AT.Account_Type", "BD.Bank_Name as bankName")
                .leftJoin(ehrTables.accountType + " as AT", "AT.Account_Type_Id", "EBD.Account_Type_Id")
                .leftJoin(ehrTables.bankDetails + " as BD", "BD.Bank_Id", "EBD.Emp_Bank_Id")
                .where('EBD.Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidateInsurance + " as EI")
                .select("EI.*", "IT.Insurance_Name")
                .leftJoin(ehrTables.insuranceType + " as IT", "IT.InsuranceType_Id", "EI.InsuranceType_Id")
                .where('EI.Candidate_Id', args.candidateId),

        ]);

        return {
            bankDetails,
            insuranceDetails
        };
    } catch (err) {
        console.log('Error in getOtherInfo function()', err);
        throw err
    }
}

