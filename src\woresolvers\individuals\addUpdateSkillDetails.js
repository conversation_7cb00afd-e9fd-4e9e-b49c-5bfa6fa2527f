//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const {validateCandidateInputs} = require('../../common/commonFunctions')

//function to add / update skill details
module.exports.addUpdateSkillDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateSkillDetails function');
    let organizationDbConnection;
    let validationError = {};
    let isUpdate = 0;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && checkRights.Role_Add === 1) {
            const fieldValidations = {
                primarySkill: "IVE0393",
                secondarySkill: "IVE0394",
                knownSkills: "IVE0395",
                handsOn: "IVE0396",
            };
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let skillData = {
                    Candidate_Id: args.candidateId,
                    Primary: args.primarySkill,
                    Skills: args.secondarySkill,
                    Proficiency: args.knownSkills,
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            return (
                                organizationDbConnection(ehrTables.candidateSkills)
                                .select('Candidate_Id')
                                .where('Candidate_Id', skillData.Candidate_Id)
                                .transacting(trx)
                                .then((data)=>{
                                    if(data && data.length){
                                        isUpdate = 1
                                        return(
                                        organizationDbConnection(ehrTables.candidateSkills)
                                            .update(skillData)
                                            .where('Candidate_Id', skillData.Candidate_Id)
                                            .transacting(trx)
                                            .then((updateSkills) => {
                                                if (updateSkills) {
                                                    return 'success'
                                                } else {
                                                    console.log('Error while updating the skill details', skillData)
                                                    throw 'IO0116'
                                                }
                                            })
                                        )
                                    }else{
                                        return(
                                        organizationDbConnection(ehrTables.candidateSkills)
                                            .insert(skillData)
                                            .transacting(trx)
                                            .then((data) => {
                                                if (data) {
                                                    return 'success'
                                                } else {
                                                    console.log('Error while inserting the skill details', skillData)
                                                    throw 'IO0116'
                                                }
                                            })
                                        )
                                    }
                                })
                            )

                        })
                        .then(async(response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The skill details has been ${isUpdate ? 'updated': 'added'}.`
                                };
    
                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);
                                organizationDbConnection ? organizationDbConnection.destroy() : null;

                                return { errorCode: "", message: "Skill details has been added/updated successfully" };
                            } else {
                                throw 'IO0116'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateSkillDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0116');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the skill details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateSkillDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateSkillDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0015');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}