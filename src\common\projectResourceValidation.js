//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

//Function to validate project resource data

module.exports.projectResourceValidation = async (args) => {
    console.log('Inside projectResouceValidation function');
    try {
        let validationError = {};
            //Validate the vendor id
            if (!(args.vendorId || commonLib.commonValidation.numberValidation(args.vendorId))) {
                validationError['IVE0251'] = commonLib.func.getError('', 'IVE0251').message1;
                throw '_EC0007'
            }

        //Validate No_Of_Employees
        if (args.noOfEmployeesWithinOrganization) {
            if (!commonLib.commonValidation.numberValidation(args.noOfEmployeesWithinOrganization)) {
                validationError['IVE0251'] = commonLib.func.getError('', 'IVE0251').message2;
            }
            else if (!commonLib.commonValidation.checkLength(args.noOfEmployeesWithinOrganization, 0, 5)) {
                validationError['IVE0251'] = commonLib.func.getError('', 'IVE0251').message3;
            }
        }

        //Validate the noOfSubcontractors
        if (args.noOfSubcontractors) {
            if (!commonLib.commonValidation.numberValidation(args.noOfSubcontractors)) {
                validationError['IVE0251'] = commonLib.func.getError('', 'IVE0251').message5;
            }
            else if (!commonLib.commonValidation.checkLength(args.noOfSubcontractors, 0, 5)) {
                validationError['IVE0251'] = commonLib.func.getError('', 'IVE0251').message6;
            }
        }

        //Validate the noOfEmployeesToBeAllocatedToContract
        if (args.noOfEmployeesToBeAllocatedToContract) {
            if (!commonLib.commonValidation.numberValidation(args.noOfEmployeesToBeAllocatedToContract)) {
                validationError['IVE0251'] = commonLib.func.getError('', 'IVE0251').message7;
            }
            else if (!commonLib.commonValidation.checkLength(args.noOfEmployeesToBeAllocatedToContract, 0, 5)) {
                validationError['IVE0251'] = commonLib.func.getError('', 'IVE0251').message8;
            }
        }

        return validationError;
    }
    catch (err) {
        console.log('Error in the projectResourceValidation() function in the main catch block.', err);
        throw err;
    }
}
