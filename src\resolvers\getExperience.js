const resolvers = {
    Query: {
        getExperience: async (root, args) => {
            console.log('Inside the getExperience() function.');
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');
            var dateFormat = require('dateformat');

            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_url')
                    .where('Url_Hash', args.Url_Hash)
                    .then((url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } else {
                            if (url[0].Candidate_Id > 0) {
                                return knexconfig.select('candidate_experience.*', 'candidate_experience_documents.File_Name', 'candidate_experience_reference.Reference_Email',
                                    'candidate_experience_reference.Reference_Name',
                                    'candidate_experience_reference.Reference_Number')
                                    .from('candidate_experience')
                                    .leftJoin('candidate_experience_documents', 'candidate_experience.Experience_Id', 'candidate_experience_documents.Experience_Id')
                                    .leftJoin('candidate_experience_reference', 'candidate_experience.Experience_Id', 'candidate_experience_reference.Experience_Id')
                                    .where('candidate_experience.Candidate_Id', url[0].Candidate_Id)
                                    .orderBy('candidate_experience.Experience_Id')
                                    .then((experience) => {
                                        let getExperienceResponse = {};
                                        if (!experience[0]) {
                                            getExperienceResponse.experience = null;
                                        } else {
                                            //Group experiences by Experience_Id
                                            const groupedExperience = experience.reduce((acc, record) => {
                                                const experienceId = record.Experience_Id;
                                                if (!acc[experienceId]) {
                                                    acc[experienceId] = {
                                                        ...record,
                                                        Start_Date: record.Start_Date
                                                            ? dateFormat(new Date(record['Start_Date']), "yyyy-mm-dd")
                                                            : null,
                                                        End_Date: record.End_Date
                                                            ? dateFormat(new Date(record['End_Date']), "yyyy-mm-dd")
                                                            : null,
                                                        Experience_Reference: [],
                                                    };
                                                }

                                                // Add references to the array
                                                if (record.Reference_Name?.length) {
                                                    acc[experienceId].Experience_Reference.push({
                                                        Reference_Email: record.Reference_Email,
                                                        Reference_Name: record.Reference_Name,
                                                        Reference_Number: record.Reference_Number,
                                                    });
                                                }

                                                return acc;

                                            }, {});

                                            let newExperience = Object.values(groupedExperience);

                                            getExperienceResponse.experience = newExperience;
                                        }

                                        // Get the employee previous experience
                                        return knexconfig.select('Previous_Employee_Experience')
                                            .from('candidate_job')
                                            .where('Candidate_Id', url[0].Candidate_Id)
                                            .then((previousExperienceTotalMonthDetail) => {
                                                let previousExperienceYear = 0;
                                                let previousExperienceMonth = 0;
                                                if (previousExperienceTotalMonthDetail.length > 0 && previousExperienceTotalMonthDetail[0].Previous_Employee_Experience) {
                                                    //From total months, get the year. convert the year to the nearest integer(Example: 1.9 -> 1)
                                                    previousExperienceYear = Math.floor((previousExperienceTotalMonthDetail[0].Previous_Employee_Experience) / 12);
                                                    //From total months, get the month.
                                                    previousExperienceMonth = (previousExperienceTotalMonthDetail[0].Previous_Employee_Experience) % 12;
                                                } else {
                                                    console.log('Employee previous experience does not exist in the getExperience() function.', previousExperienceTotalMonthDetail);
                                                }

                                                getExperienceResponse.previousExperienceYear = previousExperienceYear;
                                                getExperienceResponse.previousExperienceMonth = previousExperienceMonth;

                                                return getExperienceResponse;
                                            })
                                    })
                            } else {
                                console.log('Candidate id does not exist for the URL hash in the getExperience() function.', url);
                                throw new Error('Candidate id does not exist for the URL hash.');
                            }
                        }
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                console.log('return successful response from getExperience');
                return result;
            }).catch(function (err) {
                console.log('Error in getExperience', err);
                if (err.message == "REO0101") {
                    console.log('URL not found')
                    throw new ApolloError("URL not found", "REO0101")
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;