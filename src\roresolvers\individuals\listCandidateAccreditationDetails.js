// Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Require knex to make DB connection
const knex = require('knex');
// Require Apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { getDataFromCandidateUrlAccordingToUrlHash } = require('../../common/commonFunctions');

// Function to list accreditation
module.exports.listCandidateAccreditationDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Step 1: Validate the Url_Hash
        const candidateUrlData = await getDataFromCandidateUrlAccordingToUrlHash(organizationDbConnection, args.urlHash);
        if (!candidateUrlData?.length) throw 'EO0101';

        const candidateId = candidateUrlData[0].Candidate_Id;

        // Step 2: Get Accreditation Category/Type Ids
        const candidateAccreditationGroupData = await organizationDbConnection(ehrTables.candidateAccreditationGroups + ' as CAG')
            .select('ACETG.Accreditation_Category_And_Type_Id', 'CAG.Group_Id')
            .innerJoin(ehrTables.accreditationCategoryTypeEnforcementGroups + ' as ACETG', 'ACETG.Group_Id', 'CAG.Group_Id')
            .where('CAG.Candidate_Id', candidateId);

        const mandatoryAccreditationIds = candidateAccreditationGroupData.map(acc => acc.Accreditation_Category_And_Type_Id);
        const groupIds = new Set(candidateAccreditationGroupData.map(acc => acc.Group_Id));

        // Step 3: Get all the accreditation details that match the criteria
        let allAccreditationDetails = [];
        if (mandatoryAccreditationIds.length) {
            allAccreditationDetails = await organizationDbConnection(ehrTables.accreditationCategoryAndType)
                .modify(qb => {
                    if (!groupIds.has(1)) {
                        qb.whereIn('Accreditation_Category_And_Type_Id', mandatoryAccreditationIds);
                    }
                });
        }

        // Step 4: Filter accreditations based on Mandatory and Enforce_Dependent flags
        let mandatoryDocumentDetails = [];

        // Get accreditations that are mandatory for candidates (Mandatory=Yes)
        const candidateAccreditations = allAccreditationDetails.filter(acc => acc.Mandatory === 'Yes');
        mandatoryDocumentDetails = [...candidateAccreditations];

        // Step 5: Handle dependent accreditations (Enforce_Dependent=Yes/1)
        const dependentAccreditations = allAccreditationDetails.filter(acc => acc.Enforce_Dependent == 1);
        if (dependentAccreditations.length) {
            const candidateDependents = await organizationDbConnection(ehrTables.candidateDependent)
                .where('Candidate_Id', candidateId);

            if (candidateDependents.length) {
                // Add dependent accreditations to the list
                mandatoryDocumentDetails = [
                    ...mandatoryDocumentDetails,
                    ...dependentAccreditations.flatMap(depAcc =>
                        candidateDependents.map(dep => ({
                            ...depAcc,
                            Dependent_Id: dep.Dependent_Id,
                            Dependent_First_Name: dep.Dependent_First_Name,
                            Dependent_Last_Name: dep.Dependent_Last_Name,
                            Gender: dep.Gender,
                            Relationship: dep.Relationship,
                            Dependent_DOB: dep.Dependent_DOB
                        }))
                    )
                ];
            }
        }

        // Step 6: Return the accreditation details
        return {
            errorCode: "",
            message: "Candidate accreditation data has been fetched successfully.",
            accreditationDetails: mandatoryDocumentDetails
        };

    } catch (e) {
        console.error('Error in listCandidateAccreditationDetails:', e);
        const errResult = commonLib.func.getError(e, 'EO0105');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
};
