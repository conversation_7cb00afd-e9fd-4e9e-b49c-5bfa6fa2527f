const resolvers = {
    Query : {
        fileDeleteHr : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Candidate_Id',args.Candidate_Id)
                .then(async(url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        let path;
                        let bucketName;

                        if(args.Category == "profileImage"){
                            path = "hrapp_upload/"+args.Org_Code+"_tmp/images/"+ args.File_Name
                            bucketName = process.env.imageBucket;
                        } else if(args.Category == "document"){
                            path = process.env.domainName+"/"+args.Org_Code+"/"+"Employees Document Upload/"+ args.File_Name;
                            bucketName = process.env.documentBucket;
                        } else if(args.Category == "experience"){
                            path = process.env.domainName+"/"+args.Org_Code+"/"+"Employee Experience/"+ args.File_Name;
                            bucketName = process.env.documentBucket;
                        }
                        else if(args.Category == "accreditation")
                        {
                            path = process.env.domainName+"/"+args.Org_Code+"/"+"Employee Accreditation/"+ args.File_Name;
                            bucketName = process.env.documentBucket;
                        }
                        else {
                            throw new Error("Invalid document type")
                        }

                        try{

                            let params = {
                                Bucket: bucketName,
                                Key: path
                            };

                            const AWS = require("aws-sdk");   

                            const s3 = new AWS.S3();

                            let deleteResponse = await s3.deleteObject(params).promise();

                            return {
                                message: "File has been deleted."
                            }
                        }
                        catch(err){
                            console.log("Err",err);
                            throw new Error("Something went wrong")
                        }
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return result;
            }).catch(function(err){
                console.log('Error in fileDelete',err);
                if (err.message == "REO0101"){
                    console.log('Candidate not found')
                    throw new ApolloError("Candidate not found","REO" )
                } else if (err.message == "Invalid document type"){
                    throw new UserInputError(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;