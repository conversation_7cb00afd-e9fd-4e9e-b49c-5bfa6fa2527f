
exports.up = function(knex, Promise) {
    return knex.schema.createTable('candidate_certifications',function(table){
        table.increments('Certification_Id',11).primary();
        table.integer('Candidate_Id',11).notNullable();
        table.string('Certification_Name',30).notNullable();
        table.date('Received_Date').notNullable();
        table.string('Certificate_Received_From',30).notNullable();
    })
};

exports.down = function(knex, Promise) {
    return knex.schema.dropTable('candidate_certifications');
};
