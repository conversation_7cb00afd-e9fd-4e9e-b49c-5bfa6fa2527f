// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const dbConnection = require('../resolvers/dbConnection');
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
var ehrTables = require('../common/tableAlias').ehrTables;
var moment = require('moment-timezone');

module.exports.passcodeValidatePortalAccess = async (parent, args, context, info) => {

    let organizationDbConnection;
    console.log("Inside passcodeValidatePortalAccess function.")
    try {
        
        var connection = await dbConnection.getConnection(context.Org_Code);
        organizationDbConnection = knex(connection);

        if(!args.emailId || !args.passCode){
            throw 'IVE0530'; //The email address or passcode provided seems to be invalid.
        }

        const candidateData = await organizationDbConnection(ehrTables.candidatePersonalInfo + ' as CPI')
        .select('CPI.Candidate_Id', 'CPI.Personal_Email', 'CRI.Archived', 
            'CRI.Blacklisted', 'CRI.Portal_Access_Enabeld', 'CPI.Talent_Pool_Id', 'CRI.Passcode_Expire_Time')
        .innerJoin(ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
        .where('CPI.Personal_Email', args.emailId).where('CRI.Portal_Access_Passcode', args.passCode).first();

        if(!candidateData){
          throw 'EO0115'; //Sorry! The email address or passcode is not recognized. Please verify your details and try again.
        }

        const expireTimeUTC = moment.utc(candidateData.Passcode_Expire_Time, 'YYYY-MM-DD HH:mm:ss');
        const currentUTC = moment.utc();
        if(expireTimeUTC.isBefore(currentUTC)) {
            throw 'EO0116'; //Sorry! This passcode is expired. Please request a new passcode and try again.
        }

        return {
            message: 'Candidate portal access validated and authorized successfully',
            errorCode: '',
            data: candidateData.Candidate_Id
        }
    } catch (e) {
        console.error('Error in passcodeValidatePortalAccess function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'EO0113');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}