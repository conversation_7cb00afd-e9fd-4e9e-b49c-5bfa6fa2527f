//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');


//fuction to check the vendor status before converting an employee
let organizationDbConnection;
module.exports.checkVendorStatus = async (parent, args, context, info) => {
    console.log('Inside checkVendorStatus function');
    let validationError = {}
    try {
        if (!args.candidateId) {
            validationError['IVE0228'] = commonLib.func.getError('', 'IVE0228').message1;
        }
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.candidate, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
        if (Object.keys(validationError).length == 0) {
            return (
                organizationDbConnection(ehrTables.candidateJob)
                    .select("CJ.Service_Provider_Id as serviceProviderId", "OV.Vendor_Status as vendorStatus")
                    .leftJoin(ehrTables.serviceProvider + " as OV", "OV.Service_Provider_Id", "CJ.Service_Provider_Id")
                    .from(ehrTables.candidateJob + " as CJ")
                    .where("CJ.Candidate_Id", args.candidateId)
                    .then((res) => {
                        if(res.length){
                            let data = res[0]
                            if (data.serviceProviderId == null) {
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Candidate is not associated with any vendor" };
                            } else {
                                if (data.vendorStatus == 'Active') {
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    return { errorCode: "", message: `Candidate's Vendor status is Active` };
                                } else {
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    console.log(`Candidate's Vendor status is ${data.vendorStatus} and can't be converted into an Employee` )
                                    throw 'VO0131'
                                }
                            }
                        }else{
                            throw 'CDG0015';
                        }
                    })
                    .catch((catchError) => {
                        console.log('Error in checkVendorStatus .catch() block', catchError);
                        throw 'VO0128'
                    })
            )
        } else {
            throw 'IVE0000';
        }
    }else{
        console.log('No rights to check vendor status');
        throw '_DB0111';
    }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in checkVendorStatus function main catch block.', e);
        if (e === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in checkVendorStatus function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(e, 'VO0017');
            throw new ApolloError(errResult.message, errResult.code)
        }
    }
}