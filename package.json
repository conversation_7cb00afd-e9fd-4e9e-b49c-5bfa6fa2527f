{"name": "hrapp", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "sls offline --stage dev --region ap-south-1 --reload<PERSON>andler", "local": "sls offline --stage local --region ap-south-1 --reload<PERSON>andler"}, "author": "", "license": "ISC", "dependencies": {"@cksiva09/hrapp-corelib": "git+https://cksiva09:<EMAIL>/cksiva09/hrapp-corelib.git", "@cksiva09/validationlib": "^1.3.39", "@haftahave/serverless-ses-template": "^1.3.0", "apollo-server-lambda": "^2.6.1", "aws-sdk": "^2.593.0", "axios": "^1.6.8", "dateformat": "^3.0.3", "fs": "0.0.2", "full-age-calculator": "0.0.4", "graphql": "^14.3.1", "graphql-tools": "^4.0.4", "kms-json": "^1.1.1", "knex": "2.3.0", "moment-timezone": "^0.5.26", "mysql": "^2.17.1", "nodemailer": "^6.10.0", "path": "^0.12.7", "randomatic": "^3.1.1", "serverless-domain-manager": "^7.1.2", "serverless-prune-plugin": "^2.0.2", "serverless-step-functions": "^3.17.0", "sha3": "^2.0.4"}, "devDependencies": {"serverless-offline": "^13.2.0"}}