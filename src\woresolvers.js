const insertCandidateAccreditationDetails = require('./woresolvers/insertCandidateAccreditationDetails');
const retrieveAccreditationAndType=require('./roresolvers/retrieveAccreditationAndType');
const updateCandidateAccreditationDetails=require('./woresolvers/updateCandidateAccreditationDetails')
const insertSuperannuationDetails=require('./woresolvers/insertSuperannuationDetails')
const updateSuperannuationDetails=require('./woresolvers/updateSuperannuationDetails')
const generateUrlAndEmail=require('./woresolvers/generateUrlAndEmail')
const deleteAccreditationDetails=require('./woresolvers/deleteAccreditationDetails')
const saveVendorBasicDetails=require('./woresolvers/saveVendorBasicDetails')
const addUpdateInsurance = require('./woresolvers/addUpdateInsurance');
const bulkInviteOnboardEmployees = require('./woresolvers/bulkInviteOnboardEmployees');
const updateProjectResources = require('./woresolvers/updateProjectResources');
const changeFinishStatus=require('./woresolvers/changeFinishStatus');
const changeStatus=require('./woresolvers/changeStatus');
const updatePerformanceRatings=require('./woresolvers/updatePerformanceRatings');
const updateVendorStatusLevel = require('./woresolvers/updateVendorStatusLevel');
const deleteVendorInsurance=require('./woresolvers/deleteVendorInsurance');
const addUpdateVendorDocuments=require('./woresolvers/addUpdateVendorDocuments');
const deleteVendorDocument = require('./woresolvers/deleteVendorDocument');
const addUpdateVendorDynamicForm = require('./woresolvers/addUpdateVendorDynamicForm');
const addUpdateVendorBankDetails = require('./woresolvers/addUpdateVendorBankDetails');
const updatePersonalInfo = require('./woresolvers/individuals/updatePersonalInfo');
const updateJobDetails = require('./woresolvers/individuals/updateJobDetails');
const addCandidateInviteOnboard = require('./woresolvers/addCandidateInviteOnboard');
const updateContactDetails = require('./woresolvers/individuals/updateContactDetails');
const addUpdateSkillDetails = require('./woresolvers/individuals/addUpdateSkillDetails');
const addUpdateCandidateDocuments = require('./woresolvers/individuals/addUpdateCandidateDocuments');
const addUpdateDependentDetails = require('./woresolvers/individuals/addUpdateDependentDetails');
const addUpdateExperienceDetails = require('./woresolvers/individuals/addUpdateExperienceDetails');
const addUpdateAssetDetails = require('./woresolvers/individuals/addUpdateAssetDetails');
const addUpdateAwardDetails = require('./woresolvers/individuals/addUpdateAwardDetails');
const addUpdateBankDetails = require('./woresolvers/individuals/addUpdateBankDetails');
const addUpdateInsuranceDetails = require('./woresolvers/individuals/addUpdateInsuranceDetails');
const addUpdateAccreditationDetails = require('./woresolvers/individuals/addUpdateAccreditationDetails');
const addUpdatePassportDetails = require('./woresolvers/individuals/addUpdatePassportDetails');
const addUpdateDrivingLicenseDetails = require('./woresolvers/individuals/addUpdateDrivingLicenseDetails');
const addUpdateEducationDetails = require('./roresolvers/individuals/addUpdateEducationDetails');
const addUpdateCertificationDetails = require('./woresolvers/individuals/addUpdateCertificationDetails');
const addUpdateTrainingDetails = require('./woresolvers/individuals/addUpdateTrainingDetails');
const updateProfilePhoto = require('./woresolvers/individuals/updateProfilePhoto');
const updateCandidateStatus = require('./woresolvers/individuals/updateCandidateStatus');
const convertCandidateToEmployee = require('./woresolvers/individuals/convertCandidateToEmployee');
const addDocumentEnforcementGroup=require('./woresolvers/addDocumentEnforcementGroup')
const generatePassCodeForPortalAccess=require('./woresolvers/generatePassCodeForPortalAccess');
const passcodeValidatePortalAccess = require('./woresolvers/passcodeValidatePortalAccess');

const resolvers = {
    Query: Object.assign({},
        retrieveAccreditationAndType
    ),

    Mutation: Object.assign({},
        insertCandidateAccreditationDetails,
        updateCandidateAccreditationDetails,
        insertSuperannuationDetails,
        updateSuperannuationDetails,
        generateUrlAndEmail,
        deleteAccreditationDetails,
        saveVendorBasicDetails,
        addUpdateInsurance,
        bulkInviteOnboardEmployees,
        updateProjectResources,
        changeFinishStatus,
        changeStatus,
        updatePerformanceRatings,
        updateVendorStatusLevel,
        deleteVendorInsurance,
        addUpdateVendorDocuments,
        deleteVendorDocument,
        addUpdateVendorDynamicForm,
        addUpdateVendorBankDetails,
        updatePersonalInfo,
        updateJobDetails,
        addCandidateInviteOnboard,
        updateContactDetails,
        addUpdateSkillDetails,
        addUpdateCandidateDocuments,
        addUpdateDependentDetails,
        addUpdateExperienceDetails,
        addUpdateAssetDetails,
        addUpdateAwardDetails,
        addUpdateBankDetails,
        addUpdateInsuranceDetails,
        addUpdateAccreditationDetails,
        addUpdatePassportDetails,
        addUpdateDrivingLicenseDetails,
        addUpdateEducationDetails,
        addUpdateCertificationDetails,
        addUpdateTrainingDetails,
        updateProfilePhoto,
        updateCandidateStatus,
        convertCandidateToEmployee,
        addDocumentEnforcementGroup,
        generatePassCodeForPortalAccess,
        passcodeValidatePortalAccess,
    )
};

exports.resolvers = resolvers;
