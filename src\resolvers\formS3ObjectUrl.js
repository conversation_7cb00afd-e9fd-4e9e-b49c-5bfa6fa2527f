// require aws-sdk to use aws services
const AWS = require('aws-sdk')
// Create object for s3 bucket
const s3 = new AWS.S3({ region: process.env.region });
// get reportlogo by using orgcode
async function getReportLogo (orgCode,dbConnection){
    return (dbConnection('org_details').select('Report_LogoPath').where('Org_Code', orgCode).then(async (reportLogo) => {
            // Check if reportLogo exists or not
            if (reportLogo.length > 0) {
                var bucket = process.env.logoBucket;
                var fileName = reportLogo[0].Report_LogoPath;
                fileName = process.env.domainName + '_upload/' + orgCode + '_tmp/logos/' + fileName;
                try{
                    // Call function headObject to check files exists or not in s3 bucket. Pass bucket name and file name as input
                    await s3.headObject({ Bucket: bucket, Key: fileName }).promise();
                    // Call function getSignedUrl and pass getObject/putObject function to getpresigned url
                    var url = s3.getSignedUrl('getObject', { Bucket: bucket, Key: fileName, Expires: 60*60 });
                    url = url.split('?');
                    // return url
                    return url[0];
                }catch(error){
                    console.log('error while retrieving the s3 file url', error);
                    return '';
                }
            }else { return ''; }            
        })
    )
}
module.exports.getReportLogo = getReportLogo;
