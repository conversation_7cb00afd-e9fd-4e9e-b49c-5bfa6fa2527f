const resolvers = {
    Mutation : {
        addCandidateRelation : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');
          
            var Candidate_Id;
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then(async (url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        Candidate_Id = url[0].Candidate_Id;

                        /**validate candidate's first name */

                        var multilingualNameValidation = /^[^\d<>]*$/u;
                        var multilingualNameNumericValidation = /^[^<>]*$/u;

                        args.relationDetails.map((relation)=>{ 

                            if (!relation.Relation_Name) {
                                throw new Error("Relation Name should not be empty");
                            } else if (relation.Relation_Name.length < 1 || relation.Relation_Name.length > 150){
                                throw new Error("Relation Name should be greater than 1 and less than 150 characters");
                            } else if (!multilingualNameValidation.test(relation.Relation_Name)){
                                throw new Error("Relation Name only alphabets, spaces and special characters are allowed.")
                            } else if (!relation.Position_Code){
                                throw new Error("Position Code should not be empty");
                            } else if (relation.Position_Code.length < 1 || relation.Position_Code.length > 30){
                                throw new Error("Position Code should be greater than 1 and less than 30 characters");
                            } else if (!multilingualNameNumericValidation.test(relation.Position_Code)){
                                throw new Error("Position Code  Only alphanumeric characters, spaces and special characters are allowed.")
                            }
                        })
                        
                    
                        if(args.Is_Update) {

                            await Promise.all(args.relationDetails.map(async (relation)=>{ 
                                let Relation_Id = relation.Relation_Id;
                                delete relation.Relation_Id;
                                return await knexconfig('candidate_relation_details')
                                .update(relation).transacting(trc)
                                .where('Candidate_Id',Candidate_Id)
                                .where('Relation_Id',Relation_Id)
                            }))

                            
                            return { message:"Candidate Relation updated"}
                         

                        } else {

                            const relationList = args.relationDetails.map((relation)=>{ 
                                return {
                                    Candidate_Id: url[0].Candidate_Id,
                                    Relation_Name: relation.Relation_Name,
                                    Position_Code: relation.Position_Code
                                }
                            });
    
                            return knexconfig('candidate_relation_details')
                            .insert(relationList)
                            .transacting(trc)
                            .then(()=>{
                                return {
                                    message:"Candidate Relation added"
                                }
                            })
                        }
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return knexconfig('candidate_relation_details')
                .select('candidate_relation_details.*')
                .where('Candidate_Id',Candidate_Id)
                .then((relation)=>{
                    return { relation: !relation[0] ? null : relation }
                })
            }).catch(function(err){
                console.log('Error in addCandidateRelation',err);
                if(err.message == "REO0101"){
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Relation Name should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Relation Name should be greater than 1 and less than 150 characters"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Position Code should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Position Code should be greater than 1 and less than 30 characters"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Relation Name only alphabets, spaces and special characters are allowed."){
                    throw new UserInputError(err.message)
                } else if (err.message == "Position Code  Only alphanumeric characters, spaces and special characters are allowed."){
                    throw new UserInputError(err.message)
                }else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;