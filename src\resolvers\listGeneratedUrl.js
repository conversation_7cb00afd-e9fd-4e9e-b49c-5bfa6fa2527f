const resolvers = {
    Query : {
        listGeneratedUrl : async (root,args) =>{
            let orgCode = args.Org_Code;
            let loginEmployeeId = args.Employee_Id;
            //Require common library to access common function
            const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
            //Require constants
            const {formName} = require('../common/appConstants');
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(orgCode);
            const knexconfig = require('knex')(connection);
            var dateFormat = require('dateformat');
            var moment = require('moment-timezone');
            var timeZone = await dbConnection.getTimeZone(orgCode,loginEmployeeId);

            let loginEmployeeServiceProviderId = 0;
            //Check whether instance subscribed to fieldforce or not
            let fieldForce=await commonLib.func.getOrgDetails(orgCode, knexconfig,'',1);
            //If fieldforce is enabled the check whether loggedIn employee is a service provider admin or not
            if(fieldForce===1){
                let checkRights = await commonLib.func.checkEmployeeAccessRights(knexconfig,loginEmployeeId, formName.serviceProviderAdmin,'', 'UI');
                if(Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1){
                    //Get the login employee service providerId
                    loginEmployeeServiceProviderId=await commonLib.func.getEmployeeServiceProviderId(knexconfig, loginEmployeeId);

                    //If service provider id does not exist
                    if(loginEmployeeServiceProviderId <= 0){
                        return { errorCode: '', message: 'No candidate details found', employeeDetails:''};
                    }
                }
            }

            return knexconfig.transaction(function(trc){
                return knexconfig.select('candidate_url.Url_Hash',
                'candidate_url.Url',
                'candidate_url.Pin',
                'candidate_url.Expire_Time',
                'candidate_url.Candidate_Id',
                'candidate_url.Status',
                'candidate_url.Created_At',
                'candidate_url.Updated_At',
                'candidate_url.Job_Code',
                'candidate_url.Date_Of_Join',
                'candidate_url.Probation_Date',
                'candidate_sent_mails.Name',
                'candidate_sent_mails.Email',
                knexconfig.raw("CASE WHEN designation.Designation_Code IS NOT NULL THEN CONCAT(designation.Designation_Code,' -',designation.Designation_Name ) ELSE designation.Designation_Name END AS Designation"),
                knexconfig.raw(`
                    CASE 
                        WHEN department.Department_Code IS NOT NULL 
                        THEN CONCAT(department.Department_Code, ' - ', department.Department_Name) 
                        ELSE department.Department_Name 
                    END AS Department
                `),
                knexconfig.raw(`
                    CASE 
                        WHEN location.Location_Code IS NOT NULL 
                        THEN CONCAT(location.Location_Code, ' - ', location.Location_Name) 
                        ELSE location.Location_Name 
                    END AS Location
                `),
                knexconfig.raw(`
                    CASE 
                        WHEN employee_type.Employee_Type_Code IS NOT NULL 
                        THEN CONCAT(employee_type.Employee_Type_Code, ' - ', employee_type.Employee_Type) 
                        ELSE employee_type.Employee_Type 
                    END AS Employee_Type
                `),
                knexconfig.raw(`
                    CASE 
                        WHEN SP.Service_Provider_Code IS NOT NULL 
                        THEN CONCAT(SP.Service_Provider_Code, ' - ', SP.Service_Provider_Name) 
                        ELSE SP.Service_Provider_Name 
                    END AS Service_Provider_Name
                `),
                knexconfig.raw(
                            "CASE WHEN BU.Business_Unit_Code IS NOT NULL THEN CONCAT(BU.Business_Unit_Code, ' - ', BU.Business_Unit) ELSE BU.Business_Unit END AS Business_Unit"
                        ),
                'work_schedule.Title as Work_Schedule',
                'candidate_url.Service_Provider_Id',
                'candidate_url.Business_Unit_Id',
                knexconfig.raw('CONCAT(EPI1.Emp_First_Name, \' \', EPI1.Emp_Last_Name) as "Manager_Name"'),
                knexconfig.raw('CONCAT(EPI2.Emp_First_Name, \' \', EPI2.Emp_Last_Name) as "Created_By"'),
                knexconfig.raw('CONCAT(EPI3.Emp_First_Name, \' \', EPI3.Emp_Last_Name) as "Updated_By"'),
            )
            .from('candidate_url')
            .leftJoin('designation','candidate_url.Designation_Id','designation.Designation_Id')
            .leftJoin('department','candidate_url.Department_Id','department.Department_Id')
            .leftJoin('location','candidate_url.Location_Id','location.Location_Id')
            .leftJoin('candidate_sent_mails','candidate_url.Url','candidate_sent_mails.Url_Hash')
            .leftJoin('employee_type','candidate_url.EmpType_Id','employee_type.EmpType_Id')
            .leftJoin('work_schedule','candidate_url.Work_Schedule','work_schedule.WorkSchedule_Id')
            .leftJoin('emp_personal_info as EPI1','candidate_url.Manager_Id','EPI1.Employee_Id')
            .leftJoin('emp_personal_info as EPI2','candidate_url.Created_By','EPI2.Employee_Id')
            .leftJoin('emp_personal_info as EPI3','candidate_url.Updated_By','EPI3.Employee_Id')
            .leftJoin('service_provider as SP','candidate_url.Service_Provider_Id','SP.Service_Provider_Id')
            .leftJoin('business_unit as BU','candidate_url.Business_Unit_Id','BU.Business_Unit_Id')
            .groupBy('candidate_url.id')
            .where(qb =>{
                if(loginEmployeeServiceProviderId > 0){
                    qb.andWhere('candidate_url.Service_Provider_Id', loginEmployeeServiceProviderId);
                }
                if (args.Filter_Manager_Id) {
                    qb.andWhere('EPI1.Employee_Id', args.Filter_Manager_Id);
                }
                if (args.Filter_Date_Of_Join_From) {
                    qb.andWhere('candidate_url.Date_Of_Join','>=', args.Filter_Date_Of_Join_From);
                }
                if (args.Filter_Date_Of_Join_To) {
                    qb.andWhere('candidate_url.Date_Of_Join','<=', args.Filter_Date_Of_Join_To);
                }
                if (args.Filter_Url_Validity_From) {
                    qb.andWhere('candidate_url.Expire_Time','>=', args.Filter_Url_Validity_From);
                }
                if (args.Filter_Url_Validity_To) {
                    qb.andWhere('candidate_url.Expire_Time','<=', args.Filter_Url_Validity_To + 1);
                }
                if (args.Filter_Designation) {
                    qb.andWhere('designation.Designation_Id', args.Filter_Designation);
                }
                if (args.Filter_Department) {
                    qb.andWhere('department.Department_Id', args.Filter_Department);
                }
                if (args.Filter_Location) {
                    qb.andWhere('location.Location_Id', args.Filter_Location);
                }
                if (args.Filter_EmployeeType) {
                    qb.andWhere('employee_type.EmpType_Id', args.Filter_EmployeeType);
                }
                if (args.Filter_ServiceProvider) {
                    qb.andWhere('candidate_url.Service_Provider_Id', args.Filter_ServiceProvider);
                }
                if (args.Filter_EmployeeStatus) {
                    qb.andWhere('candidate_url.Status', args.Filter_EmployeeStatus);
                }
            })
            .orderBy('Created_At','desc')
            .then((url)=>{
                if(!url[0]){
                    return {
                        url : null
                    }
                } else {

                    var newUrl = url.map((arr)=>{ 
                        arr['Date_Of_Join'] = dateFormat(new Date(arr['Date_Of_Join']), "yyyy-mm-dd");

                        arr['Probation_Date'] = dateFormat(new Date(arr['Probation_Date']), "yyyy-mm-dd");

                        arr['Expire_Time'] = moment.tz(arr['Expire_Time'],timeZone.TimeZone_Id).format('YYYY-MM-DD HH:mm:ss');

                        arr['Created_At'] = moment.tz(arr['Created_At'],timeZone.TimeZone_Id).format('YYYY-MM-DD HH:mm:ss');

                        arr['Updated_At'] = (!arr["Updated_At"]) ? (null) : moment.tz(arr['Updated_At'],timeZone.TimeZone_Id).format('YYYY-MM-DD HH:mm:ss');

                        arr['Offset'] = timeZone.offset;
                        
                        return arr;
                    });
                    return {
                        url : newUrl
                    }
                }
            })
            .then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listGeneratedUrl')
                return result;
            }).catch(function(err){
                console.log('Error in listGeneratedUrl',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;