// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
const knex=require('knex')
let moment = require('moment-timezone');

const{ehrTables}=require('../common/tableAlias')
const{getDataFromCandidateUrlAccordingToUrlHash,validateInputDateAndGetDesiredDateFormat,isValidExtension}=require('../common/commonFunctions')


module.exports.insertCandidateAccreditationDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError={};
    try{
        console.log("Inside insertCandidateAccreditationDetails() function.")
        let urlHash=args.urlHash;
        let receivedDate=args.receivedDate?args.receivedDate:null;
        let expiryDate= args.expiryDate?args.expiryDate:null;
        let currentDateTime=moment.utc().format("YYYY-MM-DD HH-MM-SS");
        if(!urlHash)
        {
            validationError['IVE0232']=commonLib.func.getError('', 'IVE0232').message;
        }
        let validationErrorAndDates=await validateInputDateAndGetDesiredDateFormat(receivedDate,expiryDate)
        validationError=validationErrorAndDates.validationError;
        let fileExtension=(args.fileName.substr( args.fileName.lastIndexOf('.') + 1 )).toLowerCase();
        let validExtension=await isValidExtension('accreditation',fileExtension);
        if(!validExtension)
        {
            validationError['IVE0236']= commonLib.func.getError('', 'IVE0236').message;
        }
        if(Object.keys(validationError).length===0)
        {
            receivedDate=validationErrorAndDates.receivedDate;
            expiryDate=validationErrorAndDates.expiryDate;
            // get the organization database connection
            organizationDbConnection = knex(context.connection.OrganizationDb);
            let candidateUrlData= await getDataFromCandidateUrlAccordingToUrlHash(organizationDbConnection,urlHash);
            if(!candidateUrlData)
            {
                throw('EO0101');
            }
            if(candidateUrlData && candidateUrlData.length>0)
            {
                let candidateId=candidateUrlData[0].Candidate_Id;
                return(
                    organizationDbConnection(ehrTables.candidateAccreditationDetails)
                    .insert({
                        Candidate_Id:candidateId,
                        Accreditation_Category_And_Type_Id:args.accreditationCategoryAndTypeId,
                        File_Name:args.fileName,
                        Received_Date:receivedDate,
                        Expiry_Date:expiryDate,
                        Identifier:args.identifier?args.identifier:null,
                        Exam_Rating: args.examRating,
                        Exam_Date_Year: args.examDateYear?args.examDateYear:null,
                        Exam_Date_Month: args.examDateMonth?args.examDateMonth:null,
                        Dependent_Id: args.dependentId ? args.dependentId : null,
                        Added_On:currentDateTime,
                        Added_By:candidateId
                    })
                    .then(data=>{
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return {errorCode:"" ,message:"Candidate accreditation details inserted successfully."}
                    })
                    .catch(e=>{
                        console.log("Error in insertCandidateAccreditationDetails() .catch block",e);
                        throw('EO0104')
                    })
                )
            }
            else{
                throw('EO0102')
            }
        }
        else{
            throw('IVE0000')
        }
    }
    catch(e)
    {
        console.log("Error in insertCandidateAccreditationDetails() function main catch block");
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(e=='IVE0000')
        {
            let errResult = commonLib.func.getError(e, 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        let errResult = commonLib.func.getError(e, 'EO0104');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
