const resolvers = {
    Mutation: {
        statusUpdate: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');
            const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
            const { getHRGroupEmployeeEmails, sendCommonEmailTemplate, getHiringTeamEmployeeEmails, sendCustomEmail, getSenderName } = require('../common/commonFunctions')
            let notificationParams = {}
            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_url as CU')
                    .select("CU.*", "CPI.Personal_Email as candidateEmail",'CPI.Candidate_Status',"EJ1.Emp_Email as initiatorEmail", "EJ2.Emp_Email as managerEmail", knexconfig.raw('CONCAT_WS(" ",EP1.Emp_First_Name, EP1.Emp_Middle_Name, EP1.Emp_Last_Name) as initiatorName'),
                        knexconfig.raw('CONCAT_WS(" ",EP2.Emp_First_Name, EP2.Emp_Middle_Name, EP2.Emp_Last_Name) as managetName'),
                        knexconfig.raw('CONCAT_WS(" ",CPI.Emp_First_Name, CPI.Emp_Middle_Name, CPI.Emp_Last_Name) as candidateName'))
                    .where('CU.Url_Hash', args.Url_Hash)
                    .leftJoin('emp_job as EJ1', 'EJ1.Employee_Id', 'CU.Created_By')
                    .leftJoin('emp_job as EJ2', 'EJ2.Employee_Id', 'CU.Manager_Id')
                    .leftJoin('emp_personal_info as EP1', 'EP1.Employee_Id', 'CU.Created_By')
                    .leftJoin('emp_personal_info as EP2', 'EP2.Employee_Id', 'CU.Manager_Id')
                    .leftJoin('candidate_personal_info as CPI', 'CPI.Candidate_Id', 'CU.Candidate_Id')
                    .then((url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } 
                        else if(url[0].Candidate_Status.toLowerCase()==='verified' && args.Status.toLowerCase() === "draft"){
                            console.log("argument status is draft and candidate status is verified")
                            throw new Error('REO0102');
                        }
                        else {
                            return knexconfig("candidate_url")
                                .where('Url_Hash', args.Url_Hash)
                                .update({
                                    Status: args.Status
                                })
                                .transacting(trc)
                                .then(async () => {
                                    if (args.Status.toLowerCase() === "finished") {
                                    return knexconfig("candidate_recruitment_info")
                                    .update({
                                        Candidate_Status: 26
                                    })
                                    .where('Candidate_Id', url[0].Candidate_Id)
                                    .transacting(trc)
                                    .then(async () => {
                                    if(url[0].Candidate_Status?.toLowerCase()==='returned' || args.Status.toLowerCase() === "finished"){
                                        await knexconfig("candidate_personal_info")
                                        .where('Candidate_Id', url[0].Candidate_Id)
                                        .update({
                                            Candidate_Status: "Unverified"
                                        })
                                    }
                                    let context = {
                                      Org_Code: args.Org_Code,
                                    };
                                    const defaultTemplate =
                                      await commonLib.func.getDefaultTemplate(
                                        {
                                          categoryId: 19,
                                          formId: 178,
                                        },
                                        knexconfig
                                      );

                                    if (
                                      defaultTemplate &&
                                      Object.keys(defaultTemplate).length
                                    ) {
                                      const templateId =
                                        defaultTemplate.Template_Id;
                                      let eventVal = {
                                        Source: process.env.emailFrom,
                                        ReplyToAddresses: [
                                          process.env.emailReplyTo,
                                        ],

                                        bucketName: process.env.documentsBucket,
                                        region: process.env.region,
                                      };
                                      const { emailResult, ses, event } =
                                        await commonLib.func.listEmailTemplatePlaceHolderValues(
                                          {
                                            templateId,
                                            candidateId: url[0].Candidate_Id,
                                          },
                                          knexconfig,
                                          context,
                                          eventVal,
                                          []
                                        );
                                      let inviteStatus = await sendCustomEmail(
                                        emailResult,
                                        ses,
                                        event
                                      );
                                      if (
                                        inviteStatus &&
                                        inviteStatus.toLowerCase() === "invited"
                                      ) {
                                        return {
                                          message:
                                            "Status updated successfully",
                                        };
                                      }
                                    }
                                    
                                    let [groupEmails, hiringTeamMail] = await Promise.all([getHRGroupEmployeeEmails(knexconfig), getHiringTeamEmployeeEmails(knexconfig, url[0].Candidate_Id)]);
                                    //Push manager email to groupEmails
                                    if(url[0].managerEmail){
                                    hiringTeamMail.push(url[0].managerEmail)
                                    }
                                    console.log("hiringTeamMail = ", hiringTeamMail)
                                    let orgDetails = await commonLib.func.getOrgDetails(args.Org_Code, knexconfig, 1)
                                    //Form Notification Params
                                    // 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + '/Email-Template-Images/RegistrationImage.png'
                                    let candidateTemplate = {
                                        "emailSubject": 'Self-Onboarding form successfully submitted for verification!',
                                        "orgLogo": orgDetails.logoPath && orgDetails.logoPath !== undefined ? orgDetails.logoPath : '',
                                        "title1": '',
                                        "title2": '',
                                        "centerImage": '',
                                        "subTitle": 'Self-Onboarding form has been submitted successfully. HR Team will revert to you for any clarification during the verification process.',
                                        "redirectionUrl": '',
                                        "buttonText": '',
                                        "footer": '',
                                        "supportEmail": ""
                                    }
                                    let hrGroupTemplate = {
                                        "emailSubject": `Self-Onboarding: ${url[0].candidateName} submitted the onboarding form for verification!`,
                                        "orgLogo": orgDetails.logoPath && orgDetails.logoPath !== undefined ? orgDetails.logoPath : '',
                                        "title1": '',
                                        "title2": '',
                                        "centerImage": '',
                                        "subTitle": `${url[0].candidateName} has successfully completed their onboarding process. We are now ready to move forward with the verification of the candidate's details. Please click below button to continue the process.`,
                                        "redirectionUrl": `https://${args.Org_Code}${process.env.fullDomainName}/v3/onboarding/onboarded-individuals`,
                                        "buttonText": 'Check Out',
                                        "footer": '',
                                        "supportEmail": ""
                                    }

                                    let hiringTeamGroupTemplate = {
                                        "emailSubject": `Self-Onboarding: ${url[0].candidateName} submitted the onboarding form for verification!`,
                                        "orgLogo": orgDetails.logoPath && orgDetails.logoPath !== undefined ? orgDetails.logoPath : '',
                                        "title1": '',
                                        "title2": '',
                                        "centerImage": '',
                                        "subTitle": `${url[0].candidateName} has successfully completed their onboarding process. We are now ready to move forward with the verification of the candidate's details.`,
                                        "redirectionUrl": '',
                                        "buttonText": '',
                                        "footer": '',
                                        "supportEmail": ""
                                    }
                                    
                                    notificationParams.DefaultTemplateData = JSON.stringify(candidateTemplate)
                                    if(url[0].initiatorEmail){
                                    notificationParams.Destinations = [
                                        //HR Group and Initiator
                                        {
                                            Destination: {
                                                "ToAddresses": [url[0].initiatorEmail],
                                                "CcAddresses": [],
                                                "BccAddresses": groupEmails
                                            },
                                            "ReplacementTemplateData": JSON.stringify(hrGroupTemplate)
                                        },
                                    ]
                                }
                                else{
                                    notificationParams.Destinations=[]
                                }
                                    if (url[0].candidateEmail) {
                                        notificationParams.Destinations.push(
                                            //Candidate
                                            {
                                                Destination: {
                                                    "ToAddresses": [url[0].candidateEmail]
                                                },
                                                "ReplacementTemplateData": JSON.stringify(candidateTemplate)
                                            }
                                        )
                                    }
                                    if(hiringTeamMail && hiringTeamMail.length > 0){
                                        notificationParams.Destinations.push(
                                            //Candidate
                                            {
                                                Destination: {
                                                    "ToAddresses": hiringTeamMail
                                                },
                                                "ReplacementTemplateData": JSON.stringify(hiringTeamGroupTemplate)
                                            }
                                        )
                                    }
                                    if((!hiringTeamMail || !hiringTeamMail.length) && !url[0].candidateEmail && !url[0].initiatorEmail){
                                        console.log("no email data found",hiringTeamMail,url[0].candidateEmail,url[0].initiatorEmail)
                                        return {
                                            message: "Status updated successfully"
                                        }
                                    }
                                    let originalSenderName = null;
                                    let result = await getSenderName(knexconfig);
                                    originalSenderName = result?.senderName;
                                    let source = originalSenderName ? `${originalSenderName} <${process.env.emailFrom}>` : process.env.emailFrom;
                                    let region = process.env.sesTemplatesRegion
                                    await sendCommonEmailTemplate(source, region, notificationParams)
                                    return {
                                        message: "Status updated successfully"
                                    }
                                })
                            }else{
                                return true;
                            }
                            })
                        }
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                console.log('return successful response from statusUpdate');
                return result;
            }).catch(function (err) {
                console.log('Error in statusUpdate', err);
                if (err.message == "REO0101") {
                    console.log('URL not found')
                    throw new ApolloError("URL not found", "REO0101")
                } 
                else if (err.message == "REO0102") {
                    throw new ApolloError("Verified candidates status cannot be changed to draft", "REO0101")
                }
                else if (err.message == "Invalid status") {
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;