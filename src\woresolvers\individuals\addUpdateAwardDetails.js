//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')

//function to add / update award details
module.exports.addUpdateAwardDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateAwardDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const fieldValidations = {
                awardName: "IVE0390",
                receivedFrom: "IVE0391",
                receivedFor: "IVE0392",
            }
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let awardData = {
                    Candidate_Id: args.candidateId,
                    Award_Name: args.awardName,
                    Received_On: args.receivedOn,
                    Received_From: args.receivedFrom,
                    Received_For: args.receivedFor,
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (args.awardId) {
                                return (
                                    organizationDbConnection(ehrTables.candidateAwards)
                                        .update(awardData)
                                        .transacting(trx)
                                        .where('Award_Id', args.awardId)
                                        .then(async (updateData) => {
                                            if (updateData) {
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the award details', awardData)
                                                throw 'IO0120'
                                            }
                                        })
                                )
                            } else {
                                return (
                                    organizationDbConnection(ehrTables.candidateAwards)
                                        .insert(awardData)
                                        .transacting(trx)
                                        .then(async (insertData) => {
                                            if (insertData) {
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the award details', awardData)
                                                throw 'IO0120'
                                            }
                                        })
                                )
                            }

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The award details has been ${args.awardId ? 'updated' : 'added'}.`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);

                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Award details has been added/updated successfully." };
                            } else {
                                throw 'IO0120'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateAwardDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0120');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )

            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the award details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateAwardDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateAwardDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0019');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}