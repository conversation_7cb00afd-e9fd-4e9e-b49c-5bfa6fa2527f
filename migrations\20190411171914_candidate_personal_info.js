
exports.up = function(knex, Promise) {
  return knex.schema.createTable('candidate_personal_info',function(table){
      table.increments('Candidate_Id',11).primary();
      table.string('Emp_First_Name',50);
      table.string('Emp_Pref_First_Name',50);
      table.string('Emp_Middle_Name',50);
      table.string('Emp_Last_Name',50);
      table.string('Gender',10);
      table.string('Personal_Email',50);
      table.boolean('Physically_Challenged');
      table.date('DOB');
      table.string('Place_Of_Birth',50);
      table.string('Marital_Status',50);
      table.boolean('Smoker');
      table.date('Smokerasof');
      table.boolean('Military_Service');
      table.boolean('Ethnic_Race');
      table.string('Nationality',50);
      table.string('Nick_Name',50);
      table.string('Religion',50);
      table.string('Caste',50);
      table.string('Blood_Group',20);
      table.boolean('Is_Manager');
      table.string('Salutation',10);
      table.string('Photo_Path',100);
      table.string('PAN',25);
      table.string('Aadhaar_Card_Number',25);
      table.string('UAN',25);
      table.boolean('Is_Illiterate');
      table.boolean('Form_Status');
      table.boolean('UserName_OverWrite');
      table.integer('Lock_Flag',11);
      table.boolean('Section1_Progress');
      table.boolean('Section2_Progress');
      table.boolean('Section3_Progress');
      table.boolean('Section4_Progress');
      table.string('Candidate_Status',20).defaultTo('Draft');
      
  })
};

exports.down = function(knex, Promise) {
    return knex.schema.dropTable('candidate_personal_info');
};
