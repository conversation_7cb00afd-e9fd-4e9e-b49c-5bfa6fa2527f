const resolvers = {
    Mutation : {
        updatePhotoPath : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');
            
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig('candidate_personal_info')
                        .where('Candidate_Id',url[0].Candidate_Id)
                        .update({
                            Photo_Path: (!args.Photo_Path) ? (null) : (args.Photo_Path)
                        })
                        .transacting(trc)
                        .then(()=>{
                            return {
                                message: "Photo path Updated"
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from updatePhotoPath');
                return result;
            }).catch(function(err){
                console.log('Error in updatePhotoPath',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;