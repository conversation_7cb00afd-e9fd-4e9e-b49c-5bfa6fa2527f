//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');

//fuction to check the pin is correct or not
let organizationDbConnection;
let validationError = {};
module.exports.authenticatePin = async (parent, args, context, info) => {
    console.log('Inside authenticatePin function');
    try {
    //validation of pin
    if (args.pin) {
        if (!commonLib.commonValidation.numberValidation(args.pin)) {
            validationError['IVE0250'] = commonLib.func.getError('', 'IVE0250').message1;
        }
        else if (!commonLib.commonValidation.checkLength(args.pin, 1, 6)) {
            validationError['IVE0250'] = commonLib.func.getError('', 'IVE0250').message2;
        }
    }
    else {
        validationError['IVE0250'] = commonLib.func.getError('', 'IVE0250').message3;
    }
        organizationDbConnection = knex(context.connection.OrganizationDb);
        if (Object.keys(validationError).length == 0) {
        return (
            organizationDbConnection(ehrTables.invitedVendors)
            .select("Pass_Code as pin", "Expire_Time as expireTime")
            .from(ehrTables.invitedVendors)
            .where("Url_Hash", args.urlHash)
                .then((data) => {
                    if(!data[0]){
                        throw 'VO0109';
                    }
                    else if(data[0].pin != args.pin){
                        throw 'VO0113';
                    }
                    else if(Date.parse(data[0].expireTime) < Date.now()){
                        throw 'VO0110';
                    }
                    else if(data[0].status == 'Completed'){
                        throw 'VO0111';
                    }
                    else{
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Pin is correct"};
                    }
                    })
                .catch((catchError) => {
                    console.log('Error in checkUrlStatus .catch() block', catchError);
                    errResult = commonLib.func.getError(catchError, 'VO0121');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
            }else{
                throw 'IVE0000';
            }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in authenticatePin function main catch block.', e);
        if (e === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in authenticatePin function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }else{
            let errResult = commonLib.func.getError(e, 'VO0006');
            throw new ApolloError(errResult.message, errResult.code)
        }
    }
}
