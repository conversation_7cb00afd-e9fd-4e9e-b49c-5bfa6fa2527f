// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

module.exports.retrieveDocumentInfo = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveDocumentInfo function.");
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const data = await getDocumentInfo(organizationDbConnection, args);
        if (data) {
            const { documentDetails, accreditationDetails, educationalInfoDetails, certificateInfoDetails, trainingInfoDetails, drivingLicenseDetails, passportDetails } = data;
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return {
                errorCode: "",
                message: "Candidate Document details retrieved successfully.",
                educationalInfoDetails: JSON.stringify(educationalInfoDetails),
                certificateInfoDetails: JSON.stringify(certificateInfoDetails),
                trainingInfoDetails: JSON.stringify(trainingInfoDetails),
                documentDetails: JSON.stringify(documentDetails),
                accreditationDetails: JSON.stringify(accreditationDetails),
                drivingLicenseDetails: JSON.stringify(drivingLicenseDetails),
                passportDetails: JSON.stringify(passportDetails)
            };
        } else {
            throw 'IO0121'
        }
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveDocumentInfo function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'IO0020');
        throw new ApolloError(errResult.message, errResult.code);
    }
};

async function getDocumentInfo(organizationDbConnection, args) {
    try {
        const [documentDetails, accreditationDetails, educationalInfoDetails, certificateInfoDetails, trainingInfoDetails, drivingLicenseDetails, passportDetails] = await Promise.all([
            organizationDbConnection(ehrTables.candidateDocumentCategory + " as EDC")
                .select(
                    'EDC.*', 'EDC.Sub_Document_Type_Id as Document_Sub_Type_Id',
                    'DST.Document_Sub_Type',
                    'DT.Document_Type',
                    'DT.Document_Type_Id',
                    'DC.Category_Fields',
                    'DC.Category_Id',
                    "ED.*",
                )
                .leftJoin(ehrTables.documentSubType + ' as DST', 'DST.Document_Sub_Type_Id', 'EDC.Sub_Document_Type_Id')
                .leftJoin(ehrTables.documentType + ' as DT', 'DT.Document_Type_Id', 'DST.Document_Type_Id')
                .leftJoin(ehrTables.documentCategory + ' as DC', 'DC.Category_Id', 'DT.Category_Id')
                .leftJoin(ehrTables.candidateDocument + " as ED", "ED.Document_Id", "EDC.Document_Id")
                .where('EDC.Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidateAccreditationDetails + " as EAD")
                .select("EAD.*", "ACT.*", "CD.Dependent_Id", "CD.Dependent_First_Name", "CD.Dependent_Last_Name",
                    "CD.Gender", "CD.Relationship", "CD.Dependent_DOB", "ACT.File_Name as Accreditation_File_Name", "EAD.File_Name"
                )
                .leftJoin(ehrTables.accreditationCategoryAndType + " as ACT", "ACT.Accreditation_Category_And_Type_Id", "EAD.Accreditation_Category_And_Type_Id")
                .leftJoin(ehrTables.candidateDependent + " as CD", "CD.Dependent_Id", "EAD.Dependent_Id")
                .where('EAD.Candidate_Id', args.candidateId)
                .groupBy('EAD.Accreditation_Detail_Id'),

            organizationDbConnection(ehrTables.candidateEducation + " as EE")
                .select("EE.*", "CD.Course_Name",'ES.Specialization as Specialization_Name','ES.Specialization_Code','EI.Institution_Code','EI.Institution as Institution_Name_Table',"CED.*")
                .leftJoin(ehrTables.courseDetails + " as CD", "CD.Course_Id", "EE.Education_Type")
                .leftJoin(ehrTables.eduSpecialization + " as ES", "ES.Specialization_Id", "EE.Specialization_Id")
                .leftJoin(ehrTables.eduInstitution + " as EI", "EI.Institution_Id", "EE.Institution_Id")
                .leftJoin(ehrTables.candidateEducationDocuments + " as CED", "CED.Education_Id", "EE.Education_Id")
                .where('EE.Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidateCertifications + " as CC")
                .select("CC.*", "CCD.*")
                .leftJoin(ehrTables.candidateCertificationsDocuments + " as CCD", "CCD.Certification_Id", "CC.Certification_Id")
                .where('CC.Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidateTraining + " as CT")
                .select("CT.*", "CTD.*")
                .leftJoin(ehrTables.candidateTrainingDocuments + " as CTD", "CTD.Training_Id", "CT.Training_Id")
                .where('CT.Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidateDrivingLicense + " as DL")
                .select("DL.*", "C.Country_Name")
                .leftJoin(ehrTables.country + " as C", "C.Country_Code", "DL.Issuing_Country")
                .where('Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidatePassport + " as EP")
                .select("EP.*", "C.Country_Name")
                .leftJoin(ehrTables.country + " as C", "C.Country_Code", "EP.Issuing_Country")
                .where('Candidate_Id', args.candidateId)
        ]);
        return {
            educationalInfoDetails,
            certificateInfoDetails,
            trainingInfoDetails,
            documentDetails,
            accreditationDetails,
            drivingLicenseDetails,
            passportDetails
        };
    } catch (err) {
        console.log('Error in getDocumentInfo function', err);
        throw err;
    }
}
