const resolvers = {
    Query : {
        listManager : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig("emp_personal_info")
                .where('Is_Manager',true)
                .then((manager)=>{
                    if(!manager[0]){
                        return {
                            managers: null
                        }
                    } else{
                        return {
                            managers: manager
                        }
                    }
                })
                .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listManager')
                return result;
            }).catch(function(err){
                console.log('Error in listManager',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;