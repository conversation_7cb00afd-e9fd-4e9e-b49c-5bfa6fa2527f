
exports.up = function(knex, Promise) {
  return knex.schema.createTable("candidate_education",function(table){
    table.increments('Education_Id',11).primary();
    table.integer('Candidate_Id',11).notNullable();
    table.integer('Education_Type',30).notNullable();
    table.string('Specialisation',30);
    table.string('Institute_Name',50);
    table.string('University',50);
    table.string('Year_Of_Passing',10).notNullable();
    table.string('Percentage',10);
    table.string('Grade',10);
  })
};

exports.down = function(knex, Promise) {
    return knex.schema.dropTable('candidate_education');
};
