service: HRAPPOnboard
plugins:
  - serverless-domain-manager
  - "@haftahave/serverless-ses-template"
  - serverless-prune-plugin
  - serverless-offline
  - serverless-step-functions
provider:
  name: aws
  runtime: nodejs18.x
  stage: ${opt:stage}
  role: ${file(./config.${self:provider.stage}.json):role}
  region: ${opt:region}
  timeout: 30
  environment:
    stage: ${self:provider.stage}
  vpc:
    securityGroupIds: ${file(./config.${self:provider.stage}.json):securityGroupIds}
    subnetIds: ${file(./config.${self:provider.stage}.json):subnetIds}
custom:
  baseUrlUI: ${file(./config.${self:provider.stage}.json):baseUrlUI}
  imageBucket: ${file(./config.${self:provider.stage}.json):imageBucket}
  documentBucket: ${file(./config.${self:provider.stage}.json):documentBucket}
  secretName: ${file(./config.${self:provider.stage}.json):secretName}
  kmsKey: ${file(./config.${self:provider.stage}.json):KmsKeyId}
  emailFrom: ${file(./config.${self:provider.stage}.json):emailFrom}
  emailReplyTo: ${file(./config.${self:provider.stage}.json):emailReplyTo}
  sesTemplatesRegion: ${file(./config.${self:provider.stage}.json):sesRegion}
  sesTemplatesConfigFile: "./ses-email-templates/index.js"
  baseAUUrlUI: ${file(./config.${self:provider.stage}.json):baseAUUrlUI}
  vendorBucketName: ${file(./config.${self:provider.stage}.json):vendorBucketName}
  snsVersion: 2010-03-31
  serverless-offline:
    port: 4000
  customDomain:
    domainName: ${file(./config.${self:provider.stage}.json):customDomainName}
    # basePath: ''
    stage: ${self:provider.stage}
    createRoute53Record: true
    endpointType: "edge"
  prune:
    automatic: true
    number: 3
functions:
  graphqlHandler:
    handler: src/handler.graphqlHandler
    environment:
      baseUrlUI: ${self:custom.baseUrlUI}
      secretName: ${self:custom.secretName}
      kmsKey: ${self:custom.kmsKey}
      imageBucket: ${self:custom.imageBucket}
      documentBucket: ${self:custom.documentBucket}
      emailFrom: ${self:custom.emailFrom}
      emailReplyTo: ${self:custom.emailReplyTo}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      region: ${self:provider.region}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      fullDomainName: ${file(config.${self:provider.stage}.json):fullDomainName}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      baseAUUrlUI: ${self:custom.baseAUUrlUI}
      stageName: ${self:provider.stage}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      vendorBucketName: ${self:custom.vendorBucketName}
    events:
      - http:
          path: graphql
          method: post
          cors: true
  tenantScript:
    handler: src/handler.tenantScript
    environment:
      secretName: ${self:custom.secretName}
      kmsKey: ${self:custom.kmsKey}
      stage: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
  rographqlHandler:
    handler: src/rohandler.graphql
    environment:
      baseUrlUI: ${self:custom.baseUrlUI}
      dbSecretName: ${self:custom.secretName}
      kmsKey: ${self:custom.kmsKey}
      imageBucket: ${self:custom.imageBucket}
      documentBucket: ${self:custom.documentBucket}
      emailFrom: ${self:custom.emailFrom}
      emailReplyTo: ${self:custom.emailReplyTo}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      region: ${self:provider.region}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      fullDomainName: ${file(config.${self:provider.stage}.json):fullDomainName}
      commonAPIDomainName: ${file(config.${self:provider.stage}.json):commonAPIDomainName}
      stageName: ${self:provider.stage}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      baseAUUrlUI: ${self:custom.baseAUUrlUI}
      resendInviteVendorStepFunction: ${file(config.${self:provider.stage}.json):resendInviteVendorStepFunction}
      resendInviteCandidateStepFunction: ${file(config.${self:provider.stage}.json):resendInviteCandidateStepFunction}
    events:
      - http:
          path: rographql
          method: post
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - additional_headers
              - refresh_token
              - partnerid
              - user_ip
  
  regraphql:
    handler: src/rehandler.graphql
    timeout: 900 # Lambda timeout
    environment:
      baseUrlUI: ${self:custom.baseUrlUI}
      dbSecretName: ${self:custom.secretName}
      kmsKey: ${self:custom.kmsKey}
      imageBucket: ${self:custom.imageBucket}
      documentBucket: ${self:custom.documentBucket}
      emailFrom: ${self:custom.emailFrom}
      emailReplyTo: ${self:custom.emailReplyTo}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      region: ${self:provider.region}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      fullDomainName: ${file(config.${self:provider.stage}.json):fullDomainName}
      commonAPIDomainName: ${file(config.${self:provider.stage}.json):commonAPIDomainName}
      stageName: ${self:provider.stage}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      baseAUUrlUI: ${self:custom.baseAUUrlUI}
      resendInviteVendorStepFunction: ${file(config.${self:provider.stage}.json):resendInviteVendorStepFunction}
      resendInviteCandidateStepFunction: ${file(config.${self:provider.stage}.json):resendInviteCandidateStepFunction}
    events:
      - http:
          path: regraphql
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    
  wegraphql:
    handler: src/wehandler.graphql
    timeout: 300 # Lambda timeout
    environment:
      baseUrlUI: ${self:custom.baseUrlUI}
      dbSecretName: ${self:custom.secretName}
      kmsKey: ${self:custom.kmsKey}
      imageBucket: ${self:custom.imageBucket}
      documentBucket: ${self:custom.documentBucket}
      emailFrom: ${self:custom.emailFrom}
      emailReplyTo: ${self:custom.emailReplyTo}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      region: ${self:provider.region}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      fullDomainName: ${file(config.${self:provider.stage}.json):fullDomainName}
      commonAPIDomainName: ${file(config.${self:provider.stage}.json):commonAPIDomainName}
      stageName: ${self:provider.stage}
      secretName: ${self:custom.secretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      baseAUUrlUI: ${self:custom.baseAUUrlUI}
      snsRegion: ${file(config.${self:provider.stage}.json):snsRegion}
      snsEndpoint: ${file(config.${self:provider.stage}.json):snsEndpoint}
      asyncSyntrumAPIStepFunction: ${file(config.${self:provider.stage}.json):asyncSyntrumAPIStepFunction}
    events:
      - http:
          path: wegraphql
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
  wographqlHandler:
    handler: src/wohandler.graphql
    timeout: 900 # Lambda timeout
    environment:
      baseUrlUI: ${self:custom.baseUrlUI}
      dbSecretName: ${self:custom.secretName}
      kmsKey: ${self:custom.kmsKey}
      imageBucket: ${self:custom.imageBucket}
      documentBucket: ${self:custom.documentBucket}
      emailFrom: ${self:custom.emailFrom}
      emailReplyTo: ${self:custom.emailReplyTo}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      region: ${self:provider.region}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      fullDomainName: ${file(config.${self:provider.stage}.json):fullDomainName}
      commonAPIDomainName: ${file(config.${self:provider.stage}.json):commonAPIDomainName}
      stageName: ${self:provider.stage}
      secretName: ${self:custom.secretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      baseAUUrlUI: ${self:custom.baseAUUrlUI}
    events:
      - http:
          path: wographql
          method: post
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - additional_headers
              - refresh_token
              - partnerid
              - user_ip

  resendInviteVendor:
    handler: src/stepFunction/resendInviteVendor.resendInviteVendor
    memorySize: 3008
    timeout: 900
    environment:
      baseUrlUI: ${self:custom.baseUrlUI}
      dbSecretName: ${self:custom.secretName}
      kmsKey: ${self:custom.kmsKey}
      imageBucket: ${self:custom.imageBucket}
      documentBucket: ${self:custom.documentBucket}
      emailFrom: ${self:custom.emailFrom}
      emailReplyTo: ${self:custom.emailReplyTo}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      region: ${self:provider.region}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      stageName: ${self:provider.stage}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      baseAUUrlUI: ${self:custom.baseAUUrlUI}

  resendCandidateInvite:
    handler: src/stepFunction/resendCandidateInvite.resendCandidateInvite
    memorySize: 3008
    timeout: 900
    environment:
      baseUrlUI: ${self:custom.baseUrlUI}
      dbSecretName: ${self:custom.secretName}
      kmsKey: ${self:custom.kmsKey}
      imageBucket: ${self:custom.imageBucket}
      documentBucket: ${self:custom.documentBucket}
      emailFrom: ${self:custom.emailFrom}
      emailReplyTo: ${self:custom.emailReplyTo}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      region: ${self:provider.region}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      stageName: ${self:provider.stage}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      baseAUUrlUI: ${self:custom.baseAUUrlUI}

stepFunctions:
  stateMachines:
    resendInviteVendorStepFunction:
      name: ${opt:stage}ResendInviteVendorStepFunction
      events:
        - http:
            path: resendInviteVendorStepFunction
            method: POST
            cors: true
      definition:
        Comment: "Initiate resend invite vendor."
        StartAt: processresendInviteVendor
        States:
          processresendInviteVendor:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-resendInviteVendor
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: "Resend invite vendor step function execution completed."
            End: true
    
    resendInviteCandidateStepFunction:
      name: ${opt:stage}ResendInviteCandidateStepFunction
      events:
        - http:
            path: resendInviteCandidateStepFunction
            method: POST
            cors: true
      definition:
        Comment: "Initiate resend invite candidate."
        StartAt: processresendInviteCandidate
        States:
          processresendInviteCandidate:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-resendCandidateInvite
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: "Resend invite candidate step function execution completed."
            End: true
resources:
  Resources:
    ApiGatewayRestApi: # Map customized api gateway responses
      Type: AWS::ApiGateway::RestApi
      Properties:
        Name: ${self:service}-${self:provider.stage}

    GatewayResponse4XX: # statusCode 4XX series errorcode
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: "ApiGatewayRestApi"
        ResponseType: DEFAULT_4XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Forbidden." } }'

    GatewayResponse401: # statusCode 401
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: "ApiGatewayRestApi"
        ResponseType: UNAUTHORIZED # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        StatusCode: "401" # API gateway default errorcode
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Unauthorized request." } }'

    GatewayResponse5XX: # statusCode 5XX series error code
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: "ApiGatewayRestApi"
        ResponseType: DEFAULT_5XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "API gateway timeout." } }'
