// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
const knex=require('knex')
let moment = require('moment-timezone');

const{ehrTables}=require('../common/tableAlias')
const{getDataFromCandidateUrlAccordingToUrlHash}=require('../common/commonFunctions')
const{validateCommonSuperannuationInput,validateSuperannuationSuperFundAndSMSFInput,validateSuperFundInput,validateSMSFInput}=require('../common/inputValidation')


module.exports.insertSuperannuationDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError={};
    try{
        console.log("Inside insertSuperannuationDetails function.")
        let urlHash=args.urlHash;
        let currentDateTime=moment.utc().format("YYYY-MM-DD HH-MM-SS");
        if(!urlHash)
        {
            validationError['IVE0232']=commonLib.func.getError('', 'IVE0232').message;
        }
        validationError=await validateCommonSuperannuationInput(args,validationError);
        if(args.superannuationType==='Super Fund')
        {
            validationError=await validateSuperannuationSuperFundAndSMSFInput(args,validationError);
            validationError=await validateSuperFundInput(args,validationError);

        }
        if(args.superannuationType==='SMSF')
        {
            validationError=await validateSuperannuationSuperFundAndSMSFInput(args,validationError);
            validationError=await validateSMSFInput(args,validationError);
        }
        if(Object.keys(validationError).length===0)
        {
            // get the organization database connection
            organizationDbConnection = knex(context.connection.OrganizationDb);
            let candidateUrlData= await getDataFromCandidateUrlAccordingToUrlHash(organizationDbConnection,urlHash);
            if(!candidateUrlData)
            {
                throw('EO0101');
            }
            if(candidateUrlData && candidateUrlData.length>0)
            {
                let candidateId=candidateUrlData[0].Candidate_Id;
                return(
                    organizationDbConnection(ehrTables.candidateSuperannuation)
                    .insert({
                        Candidate_Id:candidateId,
                        Tax_File_Number:args.taxFileNumber,
                        Superannuation_Type:args.superannuationType,
                        Fund_ABN:args.fundABN?args.fundABN:null,
                        Fund_Name:args.fundName?args.fundName:null,
                        Fund_Address:args.fundAddress?args.fundAddress:null,
                        Suburb_Or_Town:args.suburbOrTown?args.suburbOrTown:null,
                        State_Or_Territory:args.stateOrTerritory?args.stateOrTerritory:null,
                        Post_Code:args.postCode?args.postCode:null,
                        Fund_Phone:args.fundPhone?args.fundPhone:null,
                        Fund_Electronic_Service_Address:args.fundElectronicServiceAddress?args.fundElectronicServiceAddress:null,
                        Unique_Superannuation_Identifier:args.uniqueSuperannuationIdentifier?args.uniqueSuperannuationIdentifier:null,
                        Account_Name:args.accountName?args.accountName:null,
                        Member_Number:args.memberNumber?args.memberNumber:null,
                        Added_On:currentDateTime,
                        Added_By:candidateId
                    })
                    .then(data=>{
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return {errorCode:"" ,message:"Candidate superannuation details inserted successfully."}
                    })
                    .catch(e=>{
                        console.log("Error in insertSuperannuationDetails() .catch block",e);
                        throw('EO0107')
                    })
                )
            }
            else{
                throw('EO0102')
            }
        }
        else{
            throw('IVE0000')
        }
    }
    catch(e)
    {
        console.log("Error in insertSuperannuationDetails() function main catch block",e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(e=='IVE0000')
        {
            let errResult = commonLib.func.getError(e, 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        let errResult = commonLib.func.getError(e, 'EO0107');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
