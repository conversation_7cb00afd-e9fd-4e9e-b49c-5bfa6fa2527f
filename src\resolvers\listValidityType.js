const resolvers = {
    Query : {
        listValidityType : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);

            return knexconfig.transaction(function(trc){
                return knexconfig("country")
                .then((country)=>{
                    return knexconfig("candidate_url_validity_type")
                    .then((validity)=>{
                        if(!validity[0]){
                            return {
                                validity: null
                            }
                        } else{
                            return {
                                validity: validity
                            }
                        }
                    })
                })
                .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listValidityType')
                return result;
            }).catch(function(err){
                console.log('Error in listValidityType',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;