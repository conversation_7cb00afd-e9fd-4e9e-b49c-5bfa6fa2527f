// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const knex=require('knex')

const{ehrTables}=require('../common/tableAlias')

module.exports.deleteVendorInsurance = async (parent, args, context, info) => {
    let organizationDbConnection;
    try{
        console.log("Inside deleteVendorInsurance() function.")
        let insuranceId = args.insuranceId;
            // get the organization database connection
            organizationDbConnection = knex(context.connection.OrganizationDb);
            if(insuranceId)
            {
                return(
                    organizationDbConnection(ehrTables.documentCompliance)
                    .del()
                    .where('Insurance_Id',insuranceId)
                    .then(data=>{
                        if(data){
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return {errorCode:'',message:'Vendor Insurance details deleted successfully.'};
                        }else{
                            console.log(`Error while deleting the insurance details for insurance id ${insuranceId}`, data)
                            throw 'VO0135'
                        }
                    })
                    .catch(catchError => {
                        console.log('Error while deleting vendor insurance', catchError);
                        let errResult = commonLib.func.getError(catchError, 'VO0135');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return error response
                        throw new ApolloError(errResult.message, errResult.code);
                    })
                )
            }
            else{
                throw('VO0135')
            }
    }
    catch (mainCatchError) {
        console.log('Error in deleteVendorInsurance function main block', mainCatchError);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'VO0021');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
      }
}