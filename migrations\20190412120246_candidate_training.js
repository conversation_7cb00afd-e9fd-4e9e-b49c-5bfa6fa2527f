
exports.up = function(knex, Promise) {
  return knex.schema.createTable("candidate_training",function(table){
    table.increments('Training_Id',11).primary();
    table.integer('Candidate_Id',11).notNullable();
    table.string('Training_Name',30).notNullable();
    table.date('Training_Start_Date');
    table.date('Training_End_Date');
    table.string('Training_Duration',20);
    table.string('Trainer',30).notNullable();
    table.string('Center',30).notNullable();
  })
};

exports.down = function(knex, Promise) {
  return knex.schema.dropTable('candidate_training');
};
