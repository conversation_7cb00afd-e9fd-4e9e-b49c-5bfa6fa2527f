const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
// require knex
const knex = require('knex')
// require common table alias
const { ehrTables } = require('../common/tableAlias')
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda')
// require common constant files
const { formIds } = require('../common/appConstants');
const moment = require('moment');
module.exports.addDocumentEnforcementGroup = async (
  parent,
  args,
  context,
  info
) => {
  console.log('Inside addDocumentEnforcementGroup() function ')

  let organizationDbConnection;
  try {
    let loginEmployeeId = context.Employee_Id
    let formId=formIds.documentSubType;
    organizationDbConnection = knex(context.connection.OrganizationDb)
    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      '',
      '',
      'UI',
      false,
      formId
    )

    if (
      Object.keys(checkRights).length <= 0 ||
      (checkRights.Role_Add === 0)
    ) {
        throw '_DB0101'
    }
    let groupData = args.groupNames.map(item => ({
      Group_Id:0,
      Group_Name: item,
      Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
      Added_By: loginEmployeeId
  }));
        await organizationDbConnection(
            ehrTables.documentEnforcementGroup
        )
          .insert(
            groupData
        )
          .catch((error) => {
            console.log(
              'Error in addDocumentEnforcementGroup .catch() block',
              error
            )
            throw error
          })
      let systemLogParam = {
        userIp: context.User_Ip,
        employeeId: loginEmployeeId,
        organizationDbConnection: organizationDbConnection,
        message: 'Group has been added sucessfuly'
      }
      await commonLib.func.createSystemLogActivities(systemLogParam)
      organizationDbConnection ? organizationDbConnection.destroy() : null
      return {
        errorCode: '',
        message: 'Group has been added sucessfuly'
      }
  } catch (e) {
    console.error(
      'Error in addDocumentEnforcementGroup function main catch block.',
      e
    )
    organizationDbConnection ? organizationDbConnection.destroy() : null
    let errResult
      let errorCode = e.code === 'ER_DUP_ENTRY'?'ODM0012': e
      errResult = commonLib.func.getError(errorCode, 'ODM0011')
      throw new ApolloError(errResult.message, errResult.code)

  }
}
