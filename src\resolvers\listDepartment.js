const resolvers = {
    Query : {
        listDepartment : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig("department")
                .where('Department_Status','Active')
                .then((department)=>{
                    if(!department[0]){
                        return {
                            department: null
                        }
                    } else{
                        return {
                            department: department
                        }
                    }
                })
                .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listDepartment')
                return result;
            }).catch(function(err){
                console.log('Error in listDepartment',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;