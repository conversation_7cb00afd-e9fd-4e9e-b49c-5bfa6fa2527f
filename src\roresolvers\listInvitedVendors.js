//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');

//fuction to list invited vendors
let organizationDbConnection;
module.exports.listInvitedVendors = async (parent, args, context, info) => {
    console.log('Inside listInvitedVendors function', args);
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.invitedVendors)
                    .select("IV.Department_Id as departmentId", "IV.Manager_Id as managerId",
                        organizationDbConnection.raw('CONCAT_WS(" ",EPI1.Emp_First_Name, EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as managerName'),
                        "IV.Url as url", "IV.Url_Hash as urlHash", "IV.Pass_Code as pin", "IV.Vendor_Name as vendorName", "IV.Vendor_Email as vendorEmailId", "IV.Expire_Time as expireTime",
                        "IV.Status as status", "IV.Vendor_Id as vendorId", "IV.Added_On as addedOn", "IV.Added_By as addedBy", "IV.Updated_On as updatedOn", "IV.Updated_By as updatedBy",
                        organizationDbConnection.raw("CONCAT(EPI.Emp_First_Name, ' ', EPI.Emp_Last_Name) as addedByName"),
                        organizationDbConnection.raw(`
                            CASE 
                                WHEN D.Department_Code IS NOT NULL 
                                THEN CONCAT(D.Department_Code, ' - ', D.Department_Name) 
                                ELSE D.Department_Name 
                            END AS departmentName
                        `),
                        "IV.Type_Of_Industry as typeOfIndustry", "IV.Service_Priority as servicePriority",
                        organizationDbConnection.raw(
                            "GROUP_CONCAT(DG.Group_Name ORDER BY VDEG.Group_Id SEPARATOR ', ') as documentEnforcementGroups"
                        ),
                    )
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "IV.Added_By")
                    .leftJoin(ehrTables.department + " as D", "D.Department_Id", "IV.Department_Id")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI1", "EPI1.Employee_Id", "IV.Manager_Id")
                    .leftJoin(ehrTables.vendorDocumentEnforcementGroups + " as VDEG", "VDEG.Vendor_Id", "IV.Vendor_Id")
                    .leftJoin(ehrTables.documentEnforcementGroup + " as DG", "DG.Group_Id", "VDEG.Group_Id")
                    .from(ehrTables.invitedVendors + " as IV")
                    .groupBy('IV.Vendor_Id')
                    .orderBy('IV.Added_On', 'desc')
                    .then((data) => {
                        if (data && data.length > 0) {
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Invited Vendors data has been fetched successfully.", invitedVendors: data };
                        }
                        else {
                            throw 'VO0101';
                        }
                    })
                    .catch((catchError) => {
                        console.log('Error in listInvitedVendors .catch() block', catchError);
                        errResult = commonLib.func.getError(catchError, 'VO0102');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            console.log('No rights to view Invited Vendors');
            throw '_DB0111';
        }
    }

    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listInvitedVendors function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'VO0001');
        throw new ApolloError(errResult.message, errResult.code)
    }
}