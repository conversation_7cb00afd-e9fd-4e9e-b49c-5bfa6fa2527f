const resolvers = {
    Mutation : {
        addTraining : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');
            var dateFormat = require('dateformat');
            var calculateFullAge = require('full-age-calculator');


            var Candidate_Id;
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        Candidate_Id = url[0].Candidate_Id;
                        if(!args.Training_Name) {
                            throw new Error('Training Name should not be empty');
                        } else if(args.Training_Name.length > 30) {
                            throw new Error('Training Name should be less than or equal to 30 characters');
                        } else if(!args.Trainer) {
                            throw new Error("Trainer Name should not be empty");
                        } else if(!args.Center) {
                            throw new Error("Center Name should not be empty");
                        }
                        try {
                            var Training_Start_Date = (!args.Training_Start_Date || isNaN(Date.parse(args.Training_Start_Date))) ? (null) : ((isNaN(Number(args.Training_Start_Date))) ? dateFormat(new Date((args.Training_Start_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Training_Start_Date)), "yyyy-mm-dd"));
                            var Training_End_Date = (!args.Training_End_Date || isNaN(Date.parse(args.Training_End_Date))) ? (null) : ((isNaN(Number(args.Training_End_Date))) ? dateFormat(new Date((args.Training_End_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Training_End_Date)), "yyyy-mm-dd"));
                        } 
                        catch(err){
                            throw new Error("Invalid Date Format");
                        }

                        var experience = '';
                        if(Training_End_Date){
                            var date = calculateFullAge.getFullAge(Training_Start_Date,Training_End_Date)
                            if (date.years !== 0) {
                                experience = experience + date.years + ' years';
                            }
                            if (date.months !== 0) {
                                experience = experience + ' ' + date.months + ' months';
                            }
                            if (date.days !== 0) {
                                experience = experience + ' ' + date.days + ' days';
                            }
                        }
                        return knexconfig('candidate_training')
                        .insert({
                            Candidate_Id: Candidate_Id,
                            Training_Name: args.Training_Name,
                            Training_Start_Date: Training_Start_Date,
                            Training_End_Date: Training_End_Date,
                            Training_Duration: (!experience) ? (null) : (experience),
                            Trainer: args.Trainer,
                            Center: args.Center,
                        })
                        .transacting(trc)
                        .then((dataId)=>{
                            if(!args.File_Name){
                                return {
                                    message:"Training details added"
                                }
                            } else {
                                return knexconfig('candidate_training_documents')
                                .insert({
                                    Training_Id: dataId,
                                    File_Name: args.File_Name,
                                    Document_Name: args.Document_Name,
                                    Sub_Type_Id: args.Sub_Type_Id
                                })
                                .transacting(trc)
                                .then(()=>{
                                    return {
                                        message:"Training details added"
                                    }
                                })
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return knexconfig.select('candidate_training.*',
                                        'candidate_training_documents.File_Name',
                                        'candidate_training_documents.Document_Name',
                                        'candidate_training_documents.Sub_Type_Id')
                .from('candidate_training')
                .leftJoin('candidate_training_documents','candidate_training.Training_Id','candidate_training_documents.Training_Id')
                .where('candidate_training.Candidate_Id',Candidate_Id)
                .orderBy('candidate_training.Training_Id')
                .then((training)=>{
                    if(!training[0]){
                        return {
                            training: null
                        }
                    } else {
                        var newTraining = training.map((arr)=>{ 
                            arr['Training_Start_Date']  = dateFormat(new Date(arr['Training_Start_Date']), "yyyy-mm-dd");
                            arr['Training_End_Date']  = dateFormat(new Date(arr['Training_End_Date']), "yyyy-mm-dd");
                            return arr;
                        });
                        return {
                            training: newTraining
                        }
                    }
                })
            }).catch(function(err){
                console.log('Error in addTraining',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Training Name should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Training Name should be less than or equal to 30 characters"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Trainer Name should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Center Name should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Invalid Date Format"){
                    throw new UserInputError(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;