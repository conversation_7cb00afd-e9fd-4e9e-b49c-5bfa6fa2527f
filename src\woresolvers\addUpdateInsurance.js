//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');
const { insuranceValidation } = require('../common/insuranceValidation');
const moment = require('moment');

//function to add Insurance details
module.exports.addUpdateInsurance = async (parent, args, context, info) => {
    console.log('Inside addUpdateInsurance function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        let checkRights
        organizationDbConnection = knex(context.connection.OrganizationDb);
        if (args.checkAccess) {
            checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        }
        //Check if vendor exists
        let onboardingVendorId = (args.vendorId) ? args.vendorId : 0;
        if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1))) {
            validationError = await insuranceValidation(args);
            if (Object.keys(validationError).length == 0) {
                let insuranceData = {
                    Vendor_Id: args.vendorId,
                    Insurance_Id: args.insuranceId ? args.insuranceId : 0,
                    Type_Of_Insurance: args.typeOfInsurance,
                    Name_Of_Insurance: args.nameOfInsurance ? args.nameOfInsurance : null,
                    Policy_Number: args.policyNumber,
                    Name_Of_Insurer: args.nameOfInsurer,
                    Name_Of_Insured: args.nameOfInsured,
                    Document_File: args.fileName,
                    Expiry_Date: moment(args.expireDate).format('YYYY-MM-DD'),
                    Description: args.description
                }
                // if it is update we will send the vendorId
                if (onboardingVendorId > 0) {
                    return (
                        organizationDbConnection
                            .transaction(function (trx) {
                                if (args.sumOfInsured) {
                                    insuranceData.Sum_Insured = args.sumOfInsured;
                                } else {
                                    insuranceData.Sum_Insured = null;
                                }
                                if (insuranceData.Insurance_Id) {
                                    if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1)) {
                                        return (
                                            organizationDbConnection(ehrTables.documentCompliance)
                                                .update(insuranceData)
                                                .where('Insurance_Id', insuranceData.Insurance_Id)
                                                .transacting(trx)
                                                .then((updateInsurance) => {
                                                    if (updateInsurance) {
                                                        return 'success'
                                                    } else {
                                                        console.log('Error while updating the insurance details', insuranceData)
                                                        throw 'VO0114'
                                                    }
                                                })
                                        )
                                    } else {
                                        throw '_DB0102'
                                    }
                                } else {
                                    if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_Add === 1)) {
                                        return (
                                            organizationDbConnection(ehrTables.documentCompliance)
                                                .insert(insuranceData)
                                                .transacting(trx)
                                                .then((insertData) => {
                                                    if (insertData) {
                                                        console.log('Insurance was successsfully inserted', insertData)
                                                        if (insuranceData.Type_Of_Insurance.toLowerCase() === 'professional indemnity insurance') {
                                                            return (
                                                                organizationDbConnection(ehrTables.serviceProvider)
                                                                    .update('Section2_Progress', 1)
                                                                    .transacting(trx)
                                                                    .where('Service_Provider_Id', onboardingVendorId)
                                                                    .then((update) => {
                                                                        if (update) {
                                                                            return 'success'
                                                                        } else {
                                                                            console.log('Error while updating the section progress', update)
                                                                            throw 'VO0114'
                                                                        }
                                                                    })
                                                            )
                                                        } else {
                                                            return 'success'
                                                        }
                                                    }
                                                })
                                        )
                                    } else {
                                        throw '_DB0101'
                                    }
                                }
                            })
                            .then((response) => {
                                if (response) {
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    return { errorCode: "", message: "Insurance details has been inserted/updated succesffully" };
                                } else {
                                    throw 'VO0114'
                                }
                            })
                            .catch((catchError) => {
                                console.log('Error in addUpdateInsurance .catch() block', catchError);
                                errResult = commonLib.func.getError(catchError, 'VO0114');
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                //Return error response
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                    )
                }
                else {
                    throw 'VO0103';
                }

            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the vendor');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateInsurance function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateInsurance function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(mainCatchError, 'VO0007');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}