{"securityGroupIds": ["sg-098a0324de5632c9e", "sg-03b2b753038f67293"], "subnetIds": ["subnet-020dff85cddc0752e", "subnet-0b1a6fbc2c7b782ad"], "KmsKeyId": "d7612adb-0171-4165-a705-dd5728706d1d", "role": "arn:aws:iam::692647644057:role/lambdaFullAccess", "secretName": "hrapp-stage", "imageBucket": "s3.hrapp-dev-public-asset", "documentBucket": "caprice-dev-stage", "baseUrlUI": "https://onboard.hrapp.co.in?companyName=", "emailFrom": "<EMAIL>", "emailReplyTo": "<EMAIL>", "sesRegion": "us-east-1", "logoBucket": "s3.hrapp-dev-public-images", "domainName": "hrapp", "documentsBucket": "caprice-dev-stage", "fullDomainName": ".hrapp.co.in", "customDomainName": "onboardapi.hrapp.co.in", "commonAPIDomainName": "api.hrapp.co.in", "dbPrefix": "hrapp_", "baseAUUrlUI": "https://onboarding.hrapp.co.in?companyName=", "authorizerARN": "arn:aws:lambda:ap-south-1:692647644057:function:ATS-dev-firebaseauthorizer", "vendorBucketName": "supplier.hrapp.co.in", "resendInviteVendorStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:devResendInviteVendorStepFunction", "resendInviteCandidateStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:devResendInviteCandidateStepFunction", "asyncSyntrumAPIStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-asyncSyntrumAPIFunction", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:692647644057:function:HRAPPOnboard-dev", "snsRegion": "ap-southeast-1", "snsEndpoint": "sns.ap-southeast-1.amazonaws.com"}