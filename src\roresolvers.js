const retrieveAccreditationAndType = require('./roresolvers/retrieveAccreditationAndType');
const retrieveAccreditationDetails = require('./roresolvers/retrieveAccreditationDetails');
const retrieveSuperannuationDetails = require('./roresolvers/retrieveSuperannuationDetails');
const listInvitedVendors = require('./roresolvers/listInvitedVendors');
const listOnboardedVendors = require('./roresolvers/listOnboardedVendors');
const getVendorDetails = require('./roresolvers/getVendorDetails');
const checkUrlStatus = require('./roresolvers/checkUrlStatus');
const authenticatePin = require('./roresolvers/authenticatePin');
const getVendorInsuranceDetails = require('./roresolvers/getVendorInsuranceDetails');
const listVendorEmployees = require('./roresolvers/listVendorEmployees');
const checkVendorStatus = require('./roresolvers/checkVendorStatus');
const checkMemberStatus = require('./roresolvers/checkMemberStatus');
const retrieveDesignationAccreditationCategoryDetails = require('./roresolvers/retrieveDesignationAccreditationCategoryDetails');
const retrieveProjectAccreditationCategoryDetails = require('./roresolvers/retrieveProjectAccreditationCategoryDetails');
const triggerResendInviteVendor = require('./roresolvers/triggerResendInviteVendor');
const triggerInviteCandidate = require('./roresolvers/triggerInviteCandidate');
const listVendorDocuments = require('./roresolvers/listVendorDocuments');
const getDynamicFormDetails = require('./roresolvers/getDynamicFormDetails');
const getVendorBankDetails = require('./roresolvers/getVendorBankDetails');
const retrieveVendorAdminList = require('./roresolvers/retrieveVendorAdminList');
const listInvitedIndividuals = require('./roresolvers/individuals/listInvitedIndividuals');
const retrievePersonalInfo = require('./roresolvers/individuals/retrievePersonalInfo');
const retrieveJobInfo = require('./roresolvers/individuals/retrieveJobInfo');
const retrieveContactInfo = require('./roresolvers/individuals/retrieveContactInfo');
const retrieveCareerInfo = require('./roresolvers/individuals/retrieveCareerInfo');
const retrieveDocumentInfo = require('./roresolvers/individuals/retrieveDocumentInfo');
const retrieveOtherInfo = require('./roresolvers/individuals/retrieveOtherInfo');
const retrieveMyProfile = require('./roresolvers/individuals/retrieveMyProfile');
const listCandidateDetails = require('./roresolvers/individuals/listCandidateDetails');
const listCandidateDocumentDetails = require('./roresolvers/listCandidateDocumentDetails');
const getCityListWithState = require('./resolvers/getCityListWithState');
const getTimezoneList = require('./resolvers/getTimezoneList');
const getFormFeildsByFormIdAndTab = require('./roresolvers/getFormFeildsByFormIdAndTab');
const listCandidateInvitedIndividuals = require('./roresolvers/individuals/listCandidateInvitedIndividuals');
const retrieveListEduInstitutionAndSpecialization = require('./roresolvers/retrieveListEduInstitutionAndSpecialization');
const retrieveOnboardingSettings = require('./roresolvers/retrieveOnboardingSettings');
const listTimekeepingCareerDetail = require('./roresolvers/listTimekeepingCareerDetail');
const listdocumentEnforcementGroup = require('./roresolvers/listdocumentEnforcementGroup');
const getNewHireEmployeeDetails = require('./roresolvers/getNewHireEmployeeDetails');
const listVendorDocumentDetails = require('./roresolvers/listVendorDocumentDetails');
const listCandidateAccreditationDetails = require('./roresolvers/individuals/listCandidateAccreditationDetails');
const getBarangayListWithCity = require('./resolvers/getBarangayListWithCity');
// Define resolver
const resolvers = {
    Query: Object.assign({},
        retrieveAccreditationAndType,
        retrieveAccreditationDetails,
        retrieveSuperannuationDetails,
        listInvitedVendors,
        listOnboardedVendors,
        getVendorDetails,
        checkUrlStatus,
        authenticatePin,
        getVendorInsuranceDetails,
        listVendorEmployees,
        checkVendorStatus,
        checkMemberStatus,
        retrieveDesignationAccreditationCategoryDetails,
        retrieveProjectAccreditationCategoryDetails,
        triggerResendInviteVendor,
        triggerInviteCandidate,
        listVendorDocuments,
        getDynamicFormDetails,
        getVendorBankDetails,
        retrieveVendorAdminList,
        listInvitedIndividuals,
        retrievePersonalInfo,
        retrieveJobInfo,
        retrieveContactInfo,
        retrieveCareerInfo,
        retrieveDocumentInfo,
        retrieveOtherInfo,
        retrieveMyProfile,
        listCandidateDetails,
        listCandidateDocumentDetails,
        getFormFeildsByFormIdAndTab,
        listCandidateInvitedIndividuals,
        retrieveListEduInstitutionAndSpecialization,
        getCityListWithState,
        retrieveOnboardingSettings,
        listTimekeepingCareerDetail,
        getTimezoneList,
        listdocumentEnforcementGroup,
        getNewHireEmployeeDetails,
        listVendorDocumentDetails,
        getBarangayListWithCity,
        listCandidateAccreditationDetails
    )
}
exports.resolvers = resolvers;