const resolvers = {
    Mutation: {
        addBankDetails: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');

            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_url')
                    .where('Url_Hash', args.Url_Hash)
                    .transacting(trc)
                    .then((url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } else {
                            if (!args.isAustralian && args.IFSC_Code && args.IFSC_Code.length > 50) {
                                throw new Error("IFSC Code should not exceed 50 characters");
                            } else if (!args.Bank_Name) {
                                throw new Error("Bank Name should not be empty");
                            } else if (args.Branch_Name?.length > 50) {
                                throw new Error("Branch Name should not exceed 50 characters");
                            } else if (args.Street && args.Street.length > 300) {
                                throw new Error("Street Name should not exceed 300 characters");
                            } else if (args.City && args.City.length > 50) {
                                throw new Error("City Name should not exceed 50 characters");
                            } else if (args.State && args.State.length > 50) {
                                throw new Error("State Name should not exceed 50 characters");
                            } else if (!args.Emp_Bank_Id) {
                                throw new Error("Bank Id should not be empty");
                            } else if (args.Zip && args.Zip.length > 15) {
                                throw new Error("PinCode should not exceed 15 characters");
                            } else if (!args.Bank_Account_Number) {
                                throw new Error("Account Number should not be empty");
                            } else if (args.Bank_Account_Number.length > 30) {
                                throw new Error("Account Number should not exceed 30 characters");
                            }
                            else if (args.isAustralian && !args.BSB_Code) {
                                throw new Error("BSB_Code should not be empty");
                            }
                            else if (args.isAustralian && args.BSB_Code.length > 7) {
                                throw new Error("BSB_Code should not exceed 7 characters");
                            }
                            else if (args.isAustralian && args.BSB_Code.length < 6) {
                                throw new Error("BSB_Code should not be less than 6 characters");
                            }
                            return knexconfig('candidate_bankdetails')
                                .transacting(trc)
                                .insert({
                                    Candidate_Id: url[0].Candidate_Id,
                                    Bank_Account_Name: args.Bank_Account_Name ? args.Bank_Account_Name : null,
                                    Bank_Account_Number: args.Bank_Account_Number,
                                    Emp_Bank_Id: args.Emp_Bank_Id,
                                    Credit_Account: "Salary Account",
                                    Bank_Name: args.Bank_Name,
                                    Branch_Name: args.Branch_Name,
                                    IFSC_Code: args.IFSC_Code ? args.IFSC_Code : null,
                                    Street: args.Street ? args.Street : null,
                                    City: args.City ? args.City : null,
                                    State: args.State ? args.State : null,
                                    Zip: args.Zip ? args.Zip : null,
                                    Account_Type_Id: args.Account_Type_Id ? args.Account_Type_Id : 1,
                                    BSB_Code: args.BSB_Code ? args.BSB_Code : null,
                                    File_Name: args.File_Name ? args.File_Name : null,
                                }).then(() => {
                                    return {
                                        message: "Bank Details added"
                                    }
                                })
                        }
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                console.log('return successful response from addBankDetails')
                return result;
            }).catch(function (err) {
                console.log('Error in addBankDetails', err);
                switch (err.message) {
                    case "REO0101": throw new ApolloError("URL not found", "REO0101");
                    case "IFSC Code should not be empty":
                    case "IFSC Code should not exceed 50 characters":
                    case "Bank Name should not be empty":
                    case "Branch Name should not be empty":
                    case "Branch Name should not exceed 50 characters":
                    case "Street Name should not exceed 100 characters":
                    case "City Name should not exceed 50 characters":
                    case "State Name should not exceed 50 characters":
                    case "Bank Id should not be empty":
                    case "PinCode should not exceed 15 characters":
                    case "Account Number should not be empty":
                    case "Account Number should not exceed 30 characters":
                    case "BSB_Code should not be empty":
                    case "BSB_Code should not exceed 7 characters":
                    case "BSB_Code should not be less than 6 characters":
                    case "Account Type should not be empty": throw new UserInputError(err.message)
                    default: throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;