const resolvers = {
    Mutation : {
        updateDependent : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var dateFormat = require('dateformat');
            var { ApolloError, UserInputError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        if(!args.Dependent_First_Name) {
                            throw new Error("Dependent First Name should not be empty");
                        } else if(args.Dependent_First_Name.length > 50) {
                            throw new Error("Dependent First Name maximum length is 50");
                        } else if(!args.Dependent_Last_Name) {
                            throw new Error("Dependent Last Name should not be empty");
                        } else if(args.Dependent_Last_Name.length > 50) {
                            throw new Error("Dependent Last Name maximum length is 50");
                        } else if(!args.Relationship) {
                            throw new Error("Relationship should not be empty");
                        } else if(!args.Dependent_DOB) {
                            throw new Error("Dependent DOB should not be empty");
                        }
                        else if(!args.Dependent_Id){
                            throw new Error("Dependent Id should not be empty");
                        }
                        return knexconfig('candidate_dependent')
                        .where('Dependent_Id',args.Dependent_Id)
                        .andWhere('Candidate_Id',url[0].Candidate_Id)
                        .then((details)=>{
                            if(!details[0]){
                                throw new Error("Dependent Detail not found");
                            } else {
                                var Dependent_DOB = (!args.Dependent_DOB || isNaN(Date.parse(args.Dependent_DOB))) ? (null) : ((isNaN(Number(args.Dependent_DOB))) ? dateFormat(new Date((args.Dependent_DOB)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Dependent_DOB)), "yyyy-mm-dd"));
                                return knexconfig('candidate_dependent')
                                .where('Dependent_Id',args.Dependent_Id)
                                .andWhere('Candidate_Id',url[0].Candidate_Id)
                                .update({
                                    Dependent_First_Name: args.Dependent_First_Name,
                                    Dependent_Last_Name: args.Dependent_Last_Name,
                                    Gender: (!args.Gender) ? (null) : (args.Gender),
                                    Gender_Id: args.Gender_Id,
                                    Relationship: args.Relationship,
                                    Dependent_DOB: Dependent_DOB
                                })
                                .transacting(trc)
                                .then(()=>{
                                    return {
                                        message: "Dependent Update"
                                    }
                                })
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from updateDependent');
                return result;
            }).catch(function(err){
                console.log('Error in updateDependent',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Dependent First Name should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Dependent First Name maximum length is 50"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Dependent Last Name should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Dependent Last Name maximum length is 50"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Relationship should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Dependent DOB should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Invalid Date Format"){
                    throw new UserInputError(err.message)
                }
                else if(err.message=="Dependent Id should not be empty")
                {
                    throw new UserInputError(err.message)
                }
                else if (err.message == "Dependent Detail not found"){
                    throw new Error(err.message)
                } 
                else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;