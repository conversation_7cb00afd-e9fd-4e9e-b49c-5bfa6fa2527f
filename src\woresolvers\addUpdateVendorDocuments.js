//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
let moment = require('moment-timezone');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');

//function to update vendor documents
module.exports.addUpdateVendorDocuments = async (parent, args, context, info) => {
    console.log('Inside addUpdateVendorDocuments function');
    let organizationDbConnection;
    try {
        let loginEmployeeId = context.Employee_Id ? context.Employee_Id : null;
        let documentId = args.Document_Id
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights;
        let validationError = {};
        if (args.checkAccess) {
            checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        }
        if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1))) {
            //Input Validation
            // if (!commonLib.commonValidation.checkLength(args.Document_Name, 3, 50)) {
            //     validationError['IVE0229'] = commonLib.func.getError('', 'IVE0229').message4;
            // }
            // else if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.Document_Name)) {
            //     validationError['IVE0229'] = commonLib.func.getError('', 'IVE0229').message2;
            // }
            if (Object.keys(validationError).length == 0) {
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (documentId) {
                                if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1)) {
                                    return (
                                        organizationDbConnection(ehrTables.vendorDocumentCategory)
                                            .update({
                                                'Category_Id': args.Category_Id,
                                                'Vendor_Id': args.Vendor_Id,
                                                "Document_Type_Id": args.Document_Type_Id,
                                                "Document_Sub_Type_Id": args.Document_Sub_Type_Id,
                                                "Document_Name": args.Document_Name,
                                                "Effective_Date": args.Effective_Date ? args.Effective_Date : null,
                                                "End_Date": args.End_Date ? args.End_Date : null,
                                                "Updated_On": moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                                "Updated_By": loginEmployeeId
                                            })
                                            .where('Document_Id', documentId)
                                            .transacting(trx)
                                            .then((updateDocumentCategory) => {
                                                if (updateDocumentCategory) {
                                                    //Update File name in vendor documents
                                                    return (
                                                        organizationDbConnection(ehrTables.vendorDocuments)
                                                            .update({
                                                                'Document_Id': args.Document_Id,
                                                                'File_Name': args.File_Name,
                                                                'File_Size': args.File_Size ? args.File_Size : null,
                                                            })
                                                            .where('Document_Id', documentId)
                                                            .transacting(trx)
                                                            .then(() => {
                                                                return 'success'
                                                            })
                                                    )
                                                } else {
                                                    console.log('Error while updating the vendor documents', updateDocumentCategory)
                                                    throw 'VO0130';
                                                }
                                            })
                                    )
                                } else {
                                    console.log('No rights to update vendor documents')
                                    throw '_DB0102'
                                }
                            } else {
                                if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1)) {
                                    return (
                                        organizationDbConnection(ehrTables.vendorDocumentCategory)
                                            .insert({
                                                'Category_Id': args.Category_Id,
                                                'Vendor_Id': args.Vendor_Id,
                                                "Document_Type_Id": args.Document_Type_Id,
                                                "Document_Sub_Type_Id": args.Document_Sub_Type_Id,
                                                "Document_Name": args.Document_Name,
                                                "Effective_Date": args.Effective_Date ? args.Effective_Date : null,
                                                "End_Date": args.End_Date ? args.End_Date : null,
                                                "Added_On": moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                                "Added_By": loginEmployeeId
                                            })
                                            .transacting(trx)
                                            .then((vendorDocumentCategory) => {
                                                if (vendorDocumentCategory && vendorDocumentCategory.length) {
                                                    return (
                                                        organizationDbConnection(ehrTables.vendorDocuments)
                                                            .insert({
                                                                'Document_Id': vendorDocumentCategory[0],
                                                                'File_Name': args.File_Name,
                                                                'File_Size': args.File_Size,
                                                            })
                                                            .where('Document_Id', documentId)
                                                            .transacting(trx)
                                                            .then(() => {
                                                                return 'success'
                                                            })
                                                    )
                                                } else {
                                                    console.log('Error while inserting the vendor document', vendorDocumentCategory)
                                                    throw 'VO0130';
                                                }
                                            })
                                    )
                                } else {
                                    console.log('No rights to add vendor documents')
                                    throw '_DB0101'
                                }
                            }
                        })
                        .then((response) => {
                            if (response) {
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Vendor documents has been inserted/updated successfully" };
                            } else {
                                throw 'VO0114'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateVendorDocuments .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'VO0137');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                throw 'IVE0000'
            }
        } else {
            console.log('No rights to add / update the vendor documents');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateVendorDocuments function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateVendorDocuments function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'VO0023');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

