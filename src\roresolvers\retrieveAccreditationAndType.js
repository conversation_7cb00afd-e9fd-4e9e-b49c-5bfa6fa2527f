// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
const knex=require('knex')

const{ehrTables}=require('../common/tableAlias')
const{getDataFromCandidateUrlAccordingToUrlHash}=require('../common/commonFunctions')

module.exports.retrieveAccreditationAndType = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError={};
    try{
        console.log("Inside retrieveAccreditationAndType() function.")
        let urlHash=args.urlHash;
        let employeeId=context.Employee_Id;
        if(!employeeId && !urlHash)
        {
            validationError['IVE0248']=commonLib.func.getError('', 'IVE0248').message;
        }
        if(Object.keys(validationError).length===0)
        {
            // get the organization database connection
            organizationDbConnection = knex(context.connection.OrganizationDb);
            if(urlHash)
            {
                let candidateUrlData= await getDataFromCandidateUrlAccordingToUrlHash(organizationDbConnection,urlHash);
                if(!candidateUrlData)
                {
                    throw('EO0101');
                }
                if(candidateUrlData.length==0)
                {
                    throw('EO0102');
                }
            }
            return(
                organizationDbConnection(ehrTables.accreditationCategoryAndType)
                .select('Accreditation_Category_And_Type_Id as accreditationCategoryAndTypeId','Accreditation_Category as accreditationCategory','Accreditation_Type as accreditationType',
                'Mandatory','Instruction'
                )
                .orderBy('Accreditation_Category', 'asc')
                .then(data=>{
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return {errorCode:'',message:'Accreditation Category And Type retrieved successfully.',accreditationAndType:data};
                })
                .catch(e=>{
                    console.log("Error in retrieveAccreditationAndType() function .catch block",e);
                    throw("EO0103")
                })
            )
        }
        else{
            throw('IVE0000')
        }
    }
    catch(e)
    {
        console.log("Error in retrieveAccreditationAndType() function main catch block",e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(e=='IVE0000')
        {
            let errResult = commonLib.func.getError(e, 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        let errResult = commonLib.func.getError(e, 'EO0103');
        throw new ApolloError(errResult.message, errResult.code);
    }
}