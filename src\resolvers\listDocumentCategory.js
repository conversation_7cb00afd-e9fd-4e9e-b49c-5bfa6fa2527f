const resolvers = {
    Query : {
        listDocumentCategory : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0] && !args.Vendor_Based){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig.select("Category_Id",
                        "Category_Fields as Category_Name")
                        .from("document_category")
                        .orderBy('Category_Fields', 'asc')
                        .where(function(){
                            if(args.Vendor_Based){
                                this.where('Vendor_Based', 1)
                            }
                        })
                        .then((category)=>{
                            if(!category[0]){
                                return {
                                    category: null
                                }
                            } else{
                                return {
                                    category: category
                                }
                            }
                        })
                    }
                })
                .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listDocumentCategory')
                return result;
            }).catch(function(err){
                console.log('Error in listDocumentCategory',err);
                if (err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;