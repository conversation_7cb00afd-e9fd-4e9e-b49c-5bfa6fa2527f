const resolvers = {
    Query : {
        fileReplaceHr : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');
            const checkFileExtension = require('./checkFileExtension');
            let category = args.Category;
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Candidate_Id',args.Candidate_Id)
                .then(async(url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        let fileExtension = (args.New_File_Name.substr( args.New_File_Name.lastIndexOf('.') + 1 )).toLowerCase();

                        let path;
                        let bucketName;

                        if(category == "profileImage"){
                            if ( !checkFileExtension.isValid(category,fileExtension) ) {
                                throw new Error("Unsupported file format.Supported file formats are jpg,jpeg,png")
                            } else {
                                path = "hrapp_upload/"+args.Org_Code+"_tmp/images/"+ args.File_Name
                                bucketName = process.env.imageBucket;
                            }
                        } else if(category == "document"){
                            if ( !checkFileExtension.isValid(category,fileExtension) ) {
                                throw new Error("Unsupported file format.Supported file formats are jpg,jpeg,png,pdf,tiff,tif")
                            } else {
                                path = process.env.domainName+"/"+args.Org_Code+"/"+"Employees Document Upload/" + args.File_Name;
                                bucketName = process.env.documentBucket;
                            }
                        } else if(category == "experience"){
                            if ( !checkFileExtension.isValid(category,fileExtension) ) {
                                throw new Error("Unsupported file format.Supported file formats are jpg,jpeg,png,pdf,tiff,tif")
                            } else {
                                path = process.env.domainName+"/"+args.Org_Code+"/"+"Employee Experience/" + args.File_Name;
                                bucketName = process.env.documentBucket;
                            }
                        }
                        else if(category == "accreditation")
                        {
                            if ( !checkFileExtension.isValid(category,fileExtension) ) {
                                throw new Error("Unsupported file format.Supported file formats are jpg,jpeg,png,pdf,tiff,tif")
                            } else {
                                path = process.env.domainName+"/"+args.Org_Code+"/"+"Employee Accreditation/" + args.File_Name;
                                bucketName = process.env.documentBucket;
                            }
                        }
                        else {
                            throw new Error("Invalid document category")
                        }
                        
                        try{
                            let deleteParams = {
                                Bucket: bucketName,
                                Key: path
                            };

                            const AWS = require("aws-sdk");   

                            const s3 = new AWS.S3();

                            let deleteResponse = await s3.deleteObject(deleteParams).promise();

                            let buffer = Buffer.from(args.New_File_Content, 'base64');

                            // let new_file_hash = sha256((Date.now()).toString());
                            let new_file_hash = args.Candidate_Id + '?' + (Date.now()).toString() + '?1?' + args.New_File_Name;

                            let uploadParams = {
                                Bucket: bucketName,
                                Key: path + new_file_hash,
                                Body: buffer,
                                ContentEncoding: 'base64'
                            }

                            let uploadResponse = await s3.upload(uploadParams).promise();

                            return {
                                fileName: new_file_hash
                            }
                        }
                        catch(err){
                            console.log("Err",err);
                            throw new Error("Something went wrong")
                        }
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return result;
            }).catch(function(err){
                console.log('Error in fileReplace',err);
                if (err.message == "REO0101"){
                    console.log('Candidate not found')
                    throw new ApolloError("Candidate not found","REO0101" )
                } else if(err.message == "REO0201"){
                    throw new UserInputError("Unsupported file format.Supported file formats are jpg,jpeg,png")
                } else if(err.message == "REO0202"){
                    throw new UserInputError("Unsupported file format.Supported file formats are jpg,jpeg,png,pdf,tiff,tif")
                } else if (err.message == "Invalid document category"){
                    throw new UserInputError(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;