// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
const knex=require('knex')

const{ehrTables}=require('../common/tableAlias')
const{getDataFromCandidateUrlAccordingToUrlHash}=require('../common/commonFunctions')

module.exports.retrieveSuperannuationDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError={};
    try{
        console.log("Inside retrieveSuperannuationDetails() function.")
        let urlHash=args.urlHash;
        if(!urlHash)
        {
            validationError['IVE0232']=commonLib.func.getError('', 'IVE0232').message;
        }
        if(Object.keys(validationError).length===0)
        {
            // get the organization database connection
            organizationDbConnection = knex(context.connection.OrganizationDb);
            let candidateUrlData= await getDataFromCandidateUrlAccordingToUrlHash(organizationDbConnection,urlHash);
            if(!candidateUrlData)
            {
                throw('EO0101');
            }
            if(candidateUrlData && candidateUrlData.length>0)
            {
                let candidateId=candidateUrlData[0].Candidate_Id;
                return(
                    organizationDbConnection(ehrTables.candidateSuperannuation)
                    .select('Tax_File_Number as taxFileNumber','Superannuation_Type as superannuationType','Fund_ABN as fundABN','Fund_Name as fundName',
                    'Fund_Address as fundAddress','Suburb_Or_Town as suburbOrTown','State_Or_Territory as stateOrTerritory','Post_Code as postCode','Fund_Phone as fundPhone',
                    'Fund_Electronic_Service_Address as fundElectronicServiceAddress','	Unique_Superannuation_Identifier as uniqueSuperannuationIdentifier','Account_Name as accountName','Member_Number as memberNumber')
                    .where('Candidate_Id',candidateId)
                    .then(data=>{
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return {errorCode:'',message:'Candidate superannuation details retrieved successfully.',superannuationDetails:data};
                    })
                    .catch(e=>{
                        console.log("Error in retrieveSuperannuationDetails() function .catch block",e);
                        throw("EO0109")
                    })
                )
            }
            else{
                throw('EO0102')
            }
        }
        else{
            throw('IVE0000')
        }
    }
    catch(e)
    {
        console.log("Error in retrieveSuperannuationDetails() function main catch block",e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(e=='IVE0000')
        {
            let errResult = commonLib.func.getError(e, 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        let errResult = commonLib.func.getError(e, 'EO0109');
        throw new ApolloError(errResult.message, errResult.code);
    }
}