
exports.up = function(knex, Promise) {
  return knex.schema.createTable('candidate_experience',function(table){
      table.increments('Experience_Id',11).primary();
      table.integer('Candidate_Id',11).notNullable();
      table.string('Prev_Company_Name',50).notNullable();
      table.string('Prev_Company_Location',50).notNullable();
      table.string('Designation',50).notNullable();
      table.date('Start_Date');
      table.date('End_Date');
      table.string('Duration',30).notNullable();
      table.integer('Years',4);
      table.integer('Months',2);
    })
};

exports.down = function(knex, Promise) {
    return knex.schema.dropTable('candidate_experience');
};
