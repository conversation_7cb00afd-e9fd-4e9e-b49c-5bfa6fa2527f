const { ApolloServer, gql } = require('apollo-server-lambda');
const { resolvers } = require('./woresolvers');
const fs = require('fs');
const typeDefs = gql(fs.readFileSync(__dirname.concat('/woschema.graphql'), 'utf8'));

// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

module.exports.graphql = (event, context, callback) => {
    console.log('Inside employee onboarding wohandler');
    context.callbackWaitsForEmptyEventLoop = false; 
    //to send the response immediately when callback executes
    let idToken = event.headers.idToken ? event.headers.idToken : '';
    let refreshToken = event.headers.refreshToken ? event.headers.refreshToken : '';      
    let authToken = (event.headers.Authorization && event.headers.Authorization !== 'null' && event.headers.Authorization !== 'undefined') ? event.headers.Authorization : idToken;
    // Create object for ApolloServer
    const server = new ApolloServer({
        typeDefs,
        resolvers,
        context: async ({ event }) => {
            if(authToken || refreshToken)
            {
                let authDetails ={
                    idToken: idToken,
                    refreshToken: refreshToken
                };
                let contextData = await commonLib.func.getContextDataWithEmployeeId(event,authDetails,'wo');
                return {...contextData};
            }
            else{
                let contextData = await commonLib.func.getContextDataWithoutEmployeeId(event,1,'wo');
                return {...contextData};
            }
        }
    });

    const handler = server.createHandler({
        cors: {
            method: 'POST',
            allowHeaders: '*'
        }
    });
    
    function callbackFilter(error, output) {
        // We are appending the idToken and refreshToken in the response. While running this in local this is not returning the response
        // so here checked the stagename as local or not. If it is local then we will no append the token response. 
        // Otherwise token response will be append and response will be returned
        // If any doubts check this task #3794
        if (process.env.stageName !== 'local') {
            // parse the response data
            let responseData = JSON.parse(output.body);
            // push idToken and refresh token into an json object
            output.body = JSON.stringify(responseData);
        }
        
        output.headers['Access-Control-Allow-Origin'] = '*';
        output.headers['Access-Control-Allow-Credentials'] = true;
        callback(error, output);
    }
    return handler(event, context, callbackFilter);
};
