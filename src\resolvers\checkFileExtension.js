function isValid(category, extension) {

    const profileImageFileFormats = [
        'jpg',
        'jpeg',
        'png'
    ];
    const documentFileFormats = [
        'jpg',
        'jpeg',
        'png',
        'pdf',
        'tiff',
        'tif',
        'xlsx',
        'doc',
        'docx'
    ];

    switch (category) {
        case "profileImage": return profileImageFileFormats.includes(extension);
        case "document":
        case "accreditation":
        case "experience": return documentFileFormats.includes(extension);
        case "trusteeAuthorizationDocuments": return documentFileFormats.includes(extension);
        case "projectResources": return documentFileFormats.includes(extension);
        case "insurance": return documentFileFormats.includes(extension);
        case "vendorDocuments": return documentFileFormats.includes(extension);
        case "bank": return documentFileFormats.includes(extension);
        default: return false;
    }
}

module.exports.isValid = isValid;