//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');

//fuction to list onboarded vendors
let organizationDbConnection;
module.exports.listOnboardedVendors = async (parent, args, context, info) => {
    console.log('Inside listOnboardedVendors function', args);
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.serviceProvider)
                    .select("OV.Service_Provider_Id as vendorId", "OV.Vendor_Type as vendorType", "OV.Performance_Ratings as performanceRatings", "OV.Status_Level as statusLevel",
                        organizationDbConnection.raw(`
                    CASE 
                        WHEN OV.Service_Provider_Code IS NOT NULL 
                        THEN CONCAT(OV.Service_Provider_Code, ' - ', OV.Service_Provider_Name) 
                        ELSE OV.Service_Provider_Name 
                    END AS vendorName
                `),
                        organizationDbConnection.raw(`
                    CASE 
                        WHEN D.Department_Code IS NOT NULL 
                        THEN CONCAT(D.Department_Code, ' - ', D.Department_Name) 
                        ELSE D.Department_Name 
                    END AS departmentName
                `),
                        organizationDbConnection.raw('CONCAT_WS(" ",EMPI.Emp_First_Name, EMPI.Emp_Middle_Name, EMPI.Emp_Last_Name) as managerName'),
                        "OV.Department_Id as departmentId", "OV.Manager_Id as managerId",
                        "OV.Business_Number as businessNumber", "OV.Entity_Trust as entityTrust",
                        "OV.Trustee_Name as trusteeName", "OV.GST_Registered as gstRegistered", "OV.Street as street", "OV.City as city",
                        "OV.State_Region as stateRegion", "OV.Zip_Code as zipCode", "OV.Country as country", "OV.Telephone as telephone",
                        "OV.Postal_Street as postalStreet", "OV.Postal_City as postalCity", "OV.Postal_State_Region as postalStateRegion",
                        "OV.Postal_Zip_Code as postalZipCode", "OV.Postal_Country as postalCountry", "OV.Contact_Person_Name as contactNameOfVendor",
                        "OV.Contact_Person_Phone_Number as mobileNo", "OV.Onboarding_Form_Id as onboardingFormId", "OV.Contact_Person_Phone_Number_Country_Code as mobileNoCountryCode", "OV.Email_Id as vendorEmail", "OV.No_Of_Employees_Within_Organization as noOfEmployeesWithinOrganization",
                        "OV.No_Of_Subcontractors as noOfSubcontractors", "OV.No_Of_Employees_To_Be_Allocated_To_Contract as noOfEmployeesToBeAllocatedToContract",
                        "OV.Vendor_Status as vendorStatus", "OV.Trustee_Authorization_Documents as trusteeAuthorizationDocuments", "OV.GST_Number as gstNumber",
                        "OV.Service_Offered as serviceOffered", "OV.Type_Of_Industry as typeOfIndustry", "OV.Service_Priority as servicePriority",
                        "OV.Status as status", "IV.Added_On as addedOn", "IV.Added_By as addedBy", "IV.Updated_On as updatedOn", "IV.Updated_By as updatedBy",
                        organizationDbConnection.raw("CONCAT(EPI.Emp_First_Name, ' ', EPI.Emp_Last_Name) as addedByName"),
                    )
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "OV.Added_By")
                    .leftJoin(ehrTables.invitedVendors + " as IV", "IV.Vendor_Id", "OV.Service_Provider_Id")
                    .leftJoin(ehrTables.department + " as D", "D.Department_Id", "OV.Department_Id")
                    .leftJoin(ehrTables.empPersonalInfo + " as EMPI", "EMPI.Employee_Id", "OV.Manager_Id")
                    .from(ehrTables.serviceProvider + " as OV")
                    // .orderBy('Added_On','desc')
                    .where('OV.Status', '!=', 'Draft')
                    .then((data) => {
                        if (data && data.length > 0) {
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Onboarded Vendors data has been fetched successfully.", onboardedVendors: data };
                        }
                        else {
                            throw 'VO0103';
                        }
                    })
                    .catch((catchError) => {
                        console.log('Error in listOnboardedVendors .catch() block', catchError);
                        errResult = commonLib.func.getError(catchError, 'VO0104');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        } else {
            console.log('No rights to view Invited Vendors');
            throw '_DB0111';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listOnboardedVendors function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'VO0002');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
