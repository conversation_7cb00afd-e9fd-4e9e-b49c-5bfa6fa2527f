// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../common/appConstants');

let organizationDbConnection;
module.exports.retrieveProjectAccreditationCategoryDetails = async (parent, args, context, info) => {
    try {
        console.log("Inside retrieveProjectAccreditationCategoryDetails function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let formName = args.formName ? args.formName : formName.accreditation;
        // get the access rights of the login employee for the accreditation form
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            if(checkRights.Employee_Role == "admin"){
                return (
                    organizationDbConnection(ehrTables.projectDetails)
                    .select('P.Project_Id', 'P.Project_Name', 'PM.Accreditation_Category_And_Type_Id', "AC.Accreditation_Category","AC.Accreditation_Type", "P.Status")
                        .from(ehrTables.projectDetails+" as P")
                        .leftJoin(ehrTables.projectAccreditationCategoryTypeMapping+" as PM","P.Project_Id","PM.Project_Id")
                        .leftJoin(ehrTables.accreditationCategoryAndType+" as AC","PM.Accreditation_Category_And_Type_Id","AC.Accreditation_Category_And_Type_Id")
                        .then((data) => {
                            //destroy the connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Project and Accreditation category mapping details retrieved successfully.", projectMappingDetails: data };
                        })
                        .catch((err)=>{
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            console.log('Error in retrieveProjectAccreditationCategoryDetails function .catch block.', err);
                            let errResult = commonLib.func.getError(err, 'EO0111');
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                throw '_DB0109';
            }
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveProjectAccreditationCategoryDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'EO0111');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
