//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
let moment = require('moment-timezone');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');
const {projectResourceValidation} = require('../common/projectResourceValidation');

//function to add Insurance details
module.exports.updateProjectResources = async (parent, args, context, info) => {
    console.log('Inside updateProjectResources function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        let checkRights
        organizationDbConnection = knex(context.connection.OrganizationDb);
        if(args.isUpdate){
            checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        }
        validationError = await projectResourceValidation(args);
        //Check if vendor exists
        let onboardingVendorId = (args.vendorId) ? args.vendorId : 0;
        if ((!args.isUpdate) || (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1)) {
        if (Object.keys(validationError).length == 0) {
            let projectResources = {}
            if(!args.isUpdate){
                projectResources = {
                    No_Of_Employees_Within_Organization: args.noOfEmployeesWithinOrganization,
                    No_Of_Subcontractors: args.noOfSubcontractors,
                    No_Of_Employees_To_Be_Allocated_To_Contract: args.noOfEmployeesToBeAllocatedToContract,
                    Upload_Resource_Details: args.resourceDetails,
                    Section3_Progress: 1,
                }
            }else{
                projectResources = {
                    No_Of_Employees_Within_Organization: args.noOfEmployeesWithinOrganization,
                    No_Of_Subcontractors: args.noOfSubcontractors,
                    No_Of_Employees_To_Be_Allocated_To_Contract: args.noOfEmployeesToBeAllocatedToContract,
                    Upload_Resource_Details: args.resourceDetails,
                    Updated_On: moment.utc().format("YYYY-MM-DD HH:mm:ss"),
                    Updated_By: loginEmployeeId
                }
            }
            // if it is update we will send the vendorId
                return (
                    organizationDbConnection(ehrTables.serviceProvider)
                        .update(projectResources)
                        .where('Service_Provider_Id', onboardingVendorId)
                        .then(async (updateResult) => {
                            if (updateResult) {
                                return { errorCode: "", message: "Project resources has been updated successfully." };
                            }
                            else {
                                throw 'VO0105';
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in updateProjectResources .catch() block', catchError);
                            errResult = commonLib.func.getError(catchError, 'VO0115');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )

        }else{
            throw "IVE0000";
        }
    }else{
        console.log('No rights to update the vendor');
        throw '_DB0111';
    }
    } catch (mainCatchError) {
        console.log('Error in updateProjectResources function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in updateProjectResources function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(mainCatchError, 'VO0008');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

