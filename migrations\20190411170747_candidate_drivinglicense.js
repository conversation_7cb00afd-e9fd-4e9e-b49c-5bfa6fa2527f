
exports.up = function(knex, Promise) {
  return knex.schema.createTable('candidate_drivinglicense',function(table){
    table.integer('Candidate_Id',11).primary();
    table.string('Driving_License_No',30);
    table.date('License_Issue_Date');
    table.date('License_Expiry_Date');
    table.string('Issuing_Authority',50);
    table.string('Issuing_Country',30);
    table.string('Issuing_State',30);
    table.string('Vehicle_Type',30);
  })
};

exports.down = function(knex, Promise) {
    return knex.schema.dropTable('candidate_drivinglicense');
};
