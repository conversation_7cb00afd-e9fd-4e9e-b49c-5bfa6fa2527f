//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { formName, systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')

module.exports.addUpdateDependentDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateDependentDetails function');
    let organizationDbConnection;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);


        if (Object.keys(checkRights).length === 0 || !(checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            console.log('No rights to add/update dependent details');
            throw '_DB0111';
        }

        const fieldValidations = {
            dependentFirstName: "IVE0334",
            dependentLastName: "IVE0335",
        };
        const validationError = validateCandidateInputs(args, fieldValidations);

        if (!Object.keys(validationError).length) {

            const dependentData = {
                Candidate_Id: args.candidateId,
                Dependent_First_Name: args.dependentFirstName,
                Dependent_Last_Name: args.dependentLastName,
                Gender: args.gender,
                Gender_Id: args.genderId,
                Relationship: args.relationship,
                Dependent_DOB: args.dependentDOB
            };

            await updateOrInsertDependent(organizationDbConnection, args, dependentData, context);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Candidate Dependent details has been added/updated successfully." };

        } else {
            throw 'IVE0000'
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateDependentDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateDependentDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0007');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }

    }
}

async function updateOrInsertDependent(organizationDbConnection, args, dependentData, context) {
    try {
        return organizationDbConnection.transaction(async function (trx) {
            const query = organizationDbConnection(ehrTables.candidateDependent);

            if (args.dependentId) {
                await query.update(dependentData).transacting(trx).where('Dependent_Id', args.dependentId);
            } else {
                await query.insert(dependentData).transacting(trx);

            }

            const systemLogParam = {
                action: systemLogs.onboard,
                userIp: context.User_Ip,
                employeeId: context.Employee_Id,
                formId:  formIds.individuals,
                isEmployeeTimeZone: 0,
                changedData: args,
                organizationDbConnection: organizationDbConnection,
                uniqueId: dependentData.Candidate_Id,
                message: `The dependent details has been ${args.dependentId ? 'updated' : 'added'}.`
            };

            // Call the function to add the system log
            await commonLib.func.createSystemLogActivities(systemLogParam);
        });
    } catch (err) {
        console.log('Error in updateOrInsertDependent main catch()', err);
        throw err;
    }
}