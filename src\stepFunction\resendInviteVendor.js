//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { formName } = require('../common/appConstants');
const { updateResendVendor, getOrganizationName, resendEmail } = require('../common/commonFunctions');
const { ehrTables } = require('../common/tableAlias')

//function to add Insurance details
module.exports.resendInviteVendor = async (args, context) => {
  console.log('Inside resendInviteVendor function');
  let organizationDbConnection,appmanagerDbConnection;
  try {
    let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
    // check whether data exist or not
    if(Object.keys(databaseConnection).length){
      // form app manager database connection
      appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
      let orgCode=args.orgCode;
      let loginEmployeeId = args.logInEmpId;
      let vendorIds = args.vendorIds;
      let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
      appmanagerDbConnection?appmanagerDbConnection.destroy():null;
      if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
        let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
        //Get database connection
        let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
        if(Object.keys(connection).length>0){
          organizationDbConnection = knex(connection.OrganizationDb);
          let orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection, 1);
          let organizationName = await getOrganizationName(organizationDbConnection, orgCode);
          //get the email data params in a form of array
          let emailData = await updateResendVendor(organizationDbConnection, vendorIds, loginEmployeeId, organizationName.orgName, orgDetails)
          if(emailData){
            let invitedVendorIdArray=[];
            for(let email of emailData){
              let vendorId=email.Vendor_Id;
              //method to resend email and update the status
              let inviteStatus=await resendEmail(email)
              if(inviteStatus==='Invited')
              {
                invitedVendorIdArray.push(vendorId);
              }
            }
            if(invitedVendorIdArray.length>0)
            {
              await updateVendorStatusBasedOnType(organizationDbConnection,'Invited',invitedVendorIdArray);
            }
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Vendor has been re-invited successfully" };
          }else{
            throw 'VO0107'
          }
          
        }
        else{
          throw("Error while getting connection for org db.")
        }
      }
    }
    else{
      throw("Error while getting database connection.")
    }
  } catch (mainCatchError) {
    console.log('Error in resendInviteVendor function main block', mainCatchError);
    appmanagerDbConnection?appmanagerDbConnection.destroy():null;
    organizationDbConnection ? organizationDbConnection.destroy() : null;
    let errResult = commonLib.func.getError(mainCatchError, 'VO0015');
    // return response
    throw new ApolloError(errResult.message, errResult.code);
  }
}

async function updateVendorStatusBasedOnType(organizationDbConnection,status,statusArray)
{
  try{
    console.log("Inside updateVendorStatusBasedOnType function.")
    //update the status according to the status of sending email
    return(
      organizationDbConnection(ehrTables.invitedVendors)
      .update("Status", status)
      .whereIn("Vendor_Id", statusArray)
      .then(() => {
          console.log('Vendor status updated successfully.')
          return true;
      })
      .catch(e=>{
        console.log('Error in updateVendorStatusBasedOnType .catch block',e);
        return false;
      })
    )
  }
  catch(e)
  {
    console.log('Error in updateVendorStatusBasedOnType main catch block',e);
    return false;
  }
}

