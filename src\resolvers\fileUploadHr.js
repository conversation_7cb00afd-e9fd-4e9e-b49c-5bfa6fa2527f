const resolvers = {
    Query : {
        fileUploadHr : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            const checkFileExtension = require('./checkFileExtension');
            let category = args.Category;
            var { ApolloError, UserInputError } = require('apollo-server-lambda');
            
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Candidate_Id',args.Candidate_Id)
                .then(async(url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        let fileExtension = (args.File_Name.substr( args.File_Name.lastIndexOf('.') + 1 )).toLowerCase();

                        let path ;
                        let file_hash ;
                        let bucketName;

                        if(category == "profileImage"){
                            if ( !checkFileExtension.isValid(category,fileExtension) ) {
                                throw new Error("REO0201");
                            } else {
                                file_hash = "candidate_" + args.Candidate_Id +'.'+ fileExtension;
                                path = "hrapp_upload/"+args.Org_Code+"_tmp/images/"+ file_hash
                                bucketName = process.env.imageBucket;
                            }
                        } else if(category == "document"){
                            if ( !checkFileExtension.isValid(category,fileExtension) ) {
                                throw new Error("REO0202");
                            } else {
                                file_hash = args.Candidate_Id + '?' + (Date.now()).toString() + '?1?' + args.File_Name;
                                path = process.env.domainName+"/"+args.Org_Code+"/"+"Employees Document Upload/"+ file_hash;
                                bucketName = process.env.documentBucket;
                            }
                        } else if(category == "experience"){
                            if ( !checkFileExtension.isValid(category,fileExtension) ) {
                                throw new Error("REO0202");
                            } else {
                                file_hash = args.Candidate_Id + '?' + (Date.now()).toString() + '?1?' + args.File_Name;
                                path = process.env.domainName+"/"+args.Org_Code+"/"+"Employee Experience/"+file_hash;
                                bucketName = process.env.documentBucket;
                            }
                        }
                        else if(category=="accreditation")
                        {
                            if ( !checkFileExtension.isValid(category,fileExtension) ) {
                                throw new Error("REO0202");
                            }
                            else{
                                file_hash = url[0].Candidate_Id + '?' + (Date.now()).toString() + '?1?' + args.File_Name;
                                path = process.env.domainName+"/"+args.Org_Code+"/"+"Employee Accreditation/"+file_hash;
                                bucketName = process.env.documentBucket; 
                            }
                        }
                         else {
                            throw new Error("Invalid document category")
                        }

                        try{

                            let buffer = Buffer.from(args.File_Content, 'base64');

                            let params = {
                                Bucket: bucketName,
                                Key: path,
                                Body: buffer,
                                ContentEncoding: 'base64'
                            };

                            const AWS = require("aws-sdk");   

                            const s3 = new AWS.S3();

                            let response = await s3.upload(params).promise();

                            return {
                                fileName: file_hash
                            }
                        }
                        catch(err){
                            console.log("Err",err);
                            throw new Error("Something went wrong")
                        }
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return result;
            }).catch(function(err){
                console.log('Error in fileUpload',err);
                if (err.message == "REO0101"){
                    console.log('Candidate not found')
                    throw new ApolloError("Candidate not found","REO0101" )
                } else if(err.message == "REO0201"){
                    throw new UserInputError("Unsupported file format.Supported file formats are jpg,jpeg,png")
                } else if(err.message == "REO0202"){
                    throw new UserInputError("Unsupported file format.Supported file formats are jpg,jpeg,png,pdf,tiff,tif")
                } else if (err.message == "Invalid document category"){
                    throw new UserInputError(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;