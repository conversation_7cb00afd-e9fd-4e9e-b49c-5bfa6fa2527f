let ehrTables = {
    empJob: 'emp_job',
    empPersonalInfo: 'emp_personal_info',
    designation: 'designation',
    department: 'department',
    location: 'location',
    workSchedule: 'work_schedule',
    employeeType: 'employee_type',
    empDrivingLicense: 'emp_drivinglicense',
    empPassport: 'emp_passport',
    employeeLopRecoveryAmountDetails: 'employee_lop_recovery_amount_details',
    empDependent: 'emp_dependent',
    empExperience: 'emp_experience',
    empExperienceDocuments: 'emp_experience_documents',
    contactDetails: 'contact_details',
    empEducation: 'emp_education',
    empCertifications: 'emp_certifications',
    empTraining: 'emp_training',
    empDocumentCategory: 'emp_document_category',
    documentCategory: 'document_category',
    documentType: 'document_type',
    documentSubType: 'document_sub_type',
    empDocuments: 'emp_documents',
    employeeAccreditationDetails: 'employee_accreditation_details',
    empBankDetails: 'emp_bankdetails',
    empLanguages: 'emp_language',
    languages: 'languages',
    empAssets: 'emp_assets',
    empSkills: 'emp_skillset',
    empAwards: 'emp_awards',
    empInsurance: 'emp_insurancepolicyno',
    country: 'country',
    empProfession: 'emp_profession',
    city: 'city',
    state: 'state',
    courseDetails: 'course_details',
    accreditationCategoryAndType: 'accreditation_category_and_type',
    bankDetails: 'bank_details',
    accountType: 'account_type',
    insuranceType: 'insurance_type',
    empJobAuditHistory: 'emp_job_audit_history',
    employeeLog: 'employee_info_timestamp_log',
    externalApiIntegrationLog: 'external_api_integration_log',
    onboardingSettings: 'onboarding_settings',
    salaryDetails: 'salary_details',
    orgDetails: 'org_details',
    serviceProvider: 'service_provider',
    hourlyWages: 'hourly_wages',
    empLeaves: 'emp_leaves',
    leaveImport: 'leave_data_import',
    empAttendace: 'emp_attendance',
    taxConfiguration: 'tax_configuration',
    trstscoreContactDetails: 'trstscore_contact_details',
    businessUnit: 'business_unit',
    leaveTypes: 'leave_types',
    empEligbleLeave: 'emp_eligible_leave',
    empProject: 'emp_project',
    employeeLopRecovery: 'employee_lop_recovery',
    empUser: 'emp_user',
    roles: 'roles',
    rolesBasedOnAccessControl: 'rolesbased_access_control',
    ehrForms: 'ehr_forms',
    modules: 'modules',
    salaryPayslip: 'salary_payslip',
    hourlywagesPayslip: 'hourlywages_payslip',
    salaryDeduction: 'salary_deduction',
    hourlywageDeduction: 'hourlywage_deduction',
    actualTaxableAllowance: 'actual_taxable_allowance',
    actualNontaxableAllowance: 'actual_nontaxable_allowance',
    allowances: 'allowances',
    allowanceType: 'allowance_type',
    allowanceTypeBenefitAssociation: 'allowance_type_benefit_association',
    employeeLopRecovery: 'employee_lop_recovery',
    employeeArchiveLopRecoveryAmountDetails: 'employee_archive_lop_recovery_amount_details',
    employeeArchiveLopRecovery: 'employee_archive_lop_recovery',
    lopRecoverySettings: "lop_recovery_settings",
    workflows: 'workflows',
    taUserTask: 'ta_user_task',
    taUserTaskHistory: 'ta_user_task_history',
    customGroupAssociated: 'custom_group_associated_forms',
    formLevelCoverage: 'form_level_coverage',
    customEmployeeGroupEmployees: 'custom_employee_group_employees',
    employeeSalaryConfiguration: 'employee_salary_configuration',
    contractorTaxSection: 'contractor_tax_section',
    contractorTaxRates: 'contractor_tax_rates',
    contractEmployeeTdsConfiguration: 'contract_employee_tds_configuration',
    taxSection: 'tax_section',
    timesheetHours: 'timesheet_hours',
    compensatoryOffBalance: 'compensatory_off_balance',
    compensatoryOff: 'compensatory_off',
    empResignation: 'emp_resignation',
    maritalStatus: 'marital_status',
    maritalStatusRelationship: 'marital_status_relationship',
    overtimeDetails: 'overtime_details',
    compensatoryOffBalanceHistory: 'compensatory_off_balance_history',
    dataSetupDashboard: 'datasetup_dashboard',
    esicReason: 'esic_reason',
    accreditationCategoryAndType: 'accreditation_category_and_type',
    candidateUrl: 'candidate_url',
    candidateAccreditationDetails: 'candidate_accreditation_details',
    candidateSuperannuation: 'candidate_superannuation',
    invitedVendors: "invited_vendors",
    documentCompliance: "document_compliances_types",
    candidatePersonalInfo: "candidate_personal_info",
    formBuilder: "dynamic_form_builder",
    dynamicFormResponse: "dynamic_form_responses",
    candidateJob: "candidate_job",
    candidateHobbies: "candidate_hobbies",
    candidateContact: "candidate_contact_details",
    candidateSentMail: "candidate_sent_mails",
    projectDetails: "project_details",
    projectAccreditationCategoryTypeMapping: "project_accreditation_category_type_mapping",
    designationAccreditationCategoryTypeMapping: "designation_accreditation_category_type_mapping",
    vendorDocumentCategory: "vendor_document_category",
    vendorDocuments: "vendor_documents",
    hrGroup: 'hr_group',
    vendorAdminList: 'vendor_adminlist',
    candidatePassport: 'candidate_passport',
    candidateLanguage: 'candidate_language',
    candidateContactDetails: 'candidate_contact_details',
    candidateDrivingLicense: 'candidate_drivinglicense',
    candidateDependent: 'candidate_dependent',
    candidateExperience: 'candidate_experience',
    candidateExperienceDocuments: 'candidate_experience_documents',
    candidateEducation: 'candidate_education',
    candidateEducationDocuments: 'candidate_education_documents',
    candidateCertifications: 'candidate_certifications',
    candidateCertificationsDocuments: 'candidate_certifications_documents',
    candidateTraining: 'candidate_training',
    candidateTrainingDocuments: 'candidate_training_documents',
    candidateDocumentCategory: 'candidate_document_category',
    candidateDocument: 'candidate_documents',
    candidateAccreditation: 'candidate_accreditation',
    candidateBankDetails: 'candidate_bankdetails',
    candidateLanguages: 'candidate_language',
    candidateAssets: 'candidate_assets',
    candidateSkills: 'candidate_skills',
    candidateAwards: 'candidate_awards',
    candidateInsurance: 'candidate_insurancepolicyno',
    candidateProfession: 'candidate_profession',
    candidateCity: 'candidate_city',
    candidateState: 'candidate_state',
    candidateCourseDetails: 'candidate_course_details',
    candidateAccreditationCategoryAndType: 'candidate_accreditation_category_and_type',
    candidateBankDetails: 'candidate_bankdetails',
    organizationGroup: 'organization_group',
    commentGeneration: 'comment_generation',
    fields: "fields",
    customizationFields: "customization_fields",
    candidateRecruitmentInfo: 'candidate_recruitment_info',
    jobPost: 'job_post',
    atsStatusTable: 'ats_status_table',
    eduInstitution: 'edu_institution',
    eduSpecialization: 'edu_specialization',
    jobPostLocation: 'job_post_location',
    timekeepingPic: 'timekeeping_pic',
    careerPic: 'career_pic',
    taxDetails: 'tax_details',
    vendorBankDetails: 'vendor_bankdetails',
    jobPostRecruiters: 'job_post_recruiters',
    documentEnforcementGroup: 'document_enforcement_group',
    candidateDocumentEnforcementGroups: 'candidate_document_enforcement_groups',
    documentSubTypeEnforcementGroups: 'document_sub_type_enforcement_groups',
    SFWPOrganizationStructure: 'SFWP_Organization_Structure',
    genderIdentity: 'gender_identity',
    genderExpression: 'gender_expression',
    candidateExperienceReference: 'candidate_experience_reference',
    empExperienceReference: 'emp_experience_reference',
    candidatesCustomFieldValues: 'candidates_custom_field_values',
    teamSummaryCustomFieldValues: 'team_summary_custom_field_values',
    vendorDocumentEnforcementGroups: 'vendor_document_enforcement_groups',
    customEmailTemplates:'custom_email_templates',
    atsStatusStage:'ats_status_stage',
    emailNotificationSetting: 'email_notification_setting',
    jobRoles: 'job_roles',
    candidateAccreditationGroups: 'candidate_accreditation_enforcement_groups',
    accreditationCategoryTypeEnforcementGroups: 'accreditation_category_type_enforcement_groups',
    accreditationEnforcementGroups: 'accreditation_enforcement_group',
    empPrefixConfig :'emp_prefix_config',
    empPrefixSettings : 'emp_prefix_settings',
    barangay:'barangay'
}

module.exports = {
    ehrTables
};