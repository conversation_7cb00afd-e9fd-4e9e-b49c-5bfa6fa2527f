const resolvers = {
    Mutation : {
        deleteDocument : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig('candidate_document_category')
                        .where('Document_Id',args.Document_Id)
                        .andWhere('Candidate_Id',url[0].Candidate_Id)
                        .then((document)=>{
                            if(!document[0]){
                                throw new Error("Document not Found");
                            } else{
                                return knexconfig('candidate_document_category')
                                .where('Document_Id',args.Document_Id)
                                .del()
                                .transacting(trc)
                                .then(()=>{
                                    return knexconfig('candidate_documents')
                                    .where('Document_Id',args.Document_Id)
                                    .del()
                                    .transacting(trc)
                                    .then(()=>{
                                        return {
                                            message: "Document deleted"
                                        }
                                    })
                                })
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return result;
            }).catch(function(err){
                console.log('Error in deleteDocument',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Document not Found"){
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;