# defining custom data type
scalar Date

type Query {
  retrieveAccreditationAndType(
    urlHash: String!
  ): retrieveAccreditationAndTypeResponse!
}

type Mutation {
  insertCandidateAccreditationDetails(
    urlHash: String!
    accreditationCategoryAndTypeId: Int!
    fileName: String
    receivedDate: String
    expiryDate: String
    identifier: String
    examRating: Int
    examDateYear: Int
    examDateMonth: String
    dependentId: Int
  ): commonResponse!
  updateCandidateAccreditationDetails(
    urlHash: String!
    accreditationDetailId: Int!
    accreditationCategoryAndTypeId: Int!
    fileName: String!
    receivedDate: String
    expiryDate: String
    identifier: String
    examRating: Int
    examDateYear: Int
    examDateMonth: String
    dependentId: Int
  ): commonResponse!
  insertSuperannuationDetails(
    urlHash: String!
    taxFileNumber: String!
    superannuationType: String!
    fundABN: String
    fundName: String
    fundAddress: String
    suburbOrTown: String
    stateOrTerritory: String
    postCode: String
    fundPhone: String
    fundElectronicServiceAddress: String
    uniqueSuperannuationIdentifier: String
    accountName: String
    memberNumber: String
  ): commonResponse!
  updateSuperannuationDetails(
    urlHash: String!
    taxFileNumber: String!
    superannuationType: String!
    fundABN: String
    fundName: String
    fundAddress: String
    suburbOrTown: String
    stateOrTerritory: String
    postCode: String
    fundPhone: String
    fundElectronicServiceAddress: String
    uniqueSuperannuationIdentifier: String
    accountName: String
    memberNumber: String
  ): commonResponse!
  deleteAccreditationDetails(
    urlHash: String!
    accreditationDetailId: Int!
  ): commonResponse!
  generateAndSendURL(
    orgCode: String!
    expireValue: Int!
    expireType: Int!
    locationId: Int!
    onboardingFormId: Int
    vendorName: String
    vendorEmail: String
    invitedVendorId: Int
    managerId: Int
    departmentId: Int
    typeOfIndustry: String
    servicePriority: String
    groups: [Int]
  ): commonResponse!
  saveVendorBasicDetails(
    vendorId: Int
    vendorType: String
    vendorName: String
    businessNumber: String
    entityTrust: String
    trusteeName: String
    trusteeAuthorizationDocuments: String
    gstRegistered: String
    gstNumber: String
    serviceOffered: String!
    typeOfIndustry: String!
    servicePriority: String
    street: String
    city: String
    stateRegion: String
    zipCode: String
    country: String
    postalStreet: String
    postalCity: String
    postalStateRegion: String
    postalZipCode: String
    postalCountry: String
    telephone: String
    mobileNo: String
    mobileNoCountryCode: String
    vendorEmail: String
    contactNameOfVendor: String
    performanceRatings: String
    managerId: Int
    departmentId: Int
    statusLevel: String
    vendorStatus: String
    isUpdate: Int
  ): commonResponse!
  addUpdateInsurance(
    insuranceId: Int
    vendorId: Int!
    typeOfInsurance: String!
    nameOfInsurance: String
    policyNumber: String!
    nameOfInsurer: String
    nameOfInsured: String
    sumOfInsured: Int
    expireDate: String
    description: String
    fileName: String
    checkAccess: Int
  ): commonResponse!
  deleteVendorInsurance(insuranceId: Int!): commonResponse
  bulkInviteOnboardEmployees(
    count: Int
    vendorId: Int
    employeeData: [employeeData]!
  ): commonResponse!
  updateProjectResources(
    vendorId: Int
    noOfEmployeesWithinOrganization: Int
    noOfSubcontractors: Int
    noOfEmployeesToBeAllocatedToContract: Int
    resourceDetails: String
    isUpdate: Int
  ): commonResponse!
  changeFinishStatus(vendorId: Int): commonResponse!
  changeStatus(vendorId: Int, status: String): commonResponse!
  updatePerformanceRatings(
    vendorId: Int!
    performanceRatings: String!
  ): commonResponse!
  updateVendorStatusLevel(vendorData: [vendorData]!): commonResponse!
  addUpdateVendorDocuments(
    Document_Id: Int
    Vendor_Id: Int!
    Category_Id: Int!
    Document_Type_Id: Int!
    Document_Sub_Type_Id: Int!
    Document_Name: String
    Effective_Date: String
    End_Date: String
    File_Name: String!
    File_Size: String
    checkAccess: Int
  ): commonResponse!
  deleteVendorDocument(documentId: Int, checkAccess: Int): commonResponse!
  addUpdateVendorDynamicForm(
    response: String!
    status: String!
    checkAccess: Int!
    responseId: Int
    vendorId: Int!
  ): commonResponse!
  addDocumentEnforcementGroup(
  groupNames:[String]!
  ): commonResponse!
  addUpdateVendorBankDetails(
    Vendor_Bank_Id: Int
    Vendor_Id: Int
    Bank_Account_Number: String
    Bank_Name: String
    Bank_Id: Int
    Branch_Name: String
    IFSC_Code: String
    Street: String
    City: String
    State: String
    Zip: String
    Account_Type_Id: Int
    Credit_Account: String
    Beneficiary_Id: Int
    Status: String
    BSB_Code: String
    checkAccess: Int
  ): commonResponse!
  updatePersonalInfo(
    candidateId: Int!
    userDefinedEmpId: String
    biometricIntegrationId: String
    appellation: String
    photoPath: String
    salutation: String!
    empFirstName: String!
    empMiddleName: String
    empLastName: String!
    knownAs: String
    gender: String!
    genderId: Int
    genderIdentityId: Int
    genderExpressionId: Int
    dob: String!
    placeOfBirth: String
    maritalStatus: Int!
    bloodGroup: String
    hobbies: String
    languages: [language]
    nationality: String!
    nationalityId: Int!
    militaryService: Int
    religion: String
    religionId: Int!
    caste: String
    disabled: Int
    isManager: Int
    personalEmail: String
    smoker: Int
    smokerAsOf: String
    aadharNumber: String
    uan: String
    pan: String
    formStatus: Int
    allowUserSignIn: Int
    enableMobileSignIn: Int
    formName: String
    genderOrientations: String
    pronoun: String
    statutoryInsuranceNumber: String
    pranNo: String
    taxCode: String
  ): updatePersonalInfoResponse!
  updateJobDetails(
    Candidate_Id: Int!
    Roles_Id: Int
    Job_Role_Ids:[Int]
    Designation_Id: Int!
    Date_Of_Join: String!
    Department_Id: Int!
    Location_Id: Int!
    Job_Code: String
    Emp_Email: String
    Manager_Id: Int
    Emp_Profession: Int
    Confirmed: Int
    Confirmation_Date: String
    Probation_Date: String
    EmpType_Id: Int!
    Commission_Employee: Int
    Work_Schedule: Int!
    TDS_Exemption: Int
    Attendance_Enforced_Payment: Int
    Previous_Employee_Experience: Int
    Service_Provider_Id: Int
    Business_Unit_Id: Int
    Pf_PolicyNo: String
    Organization_Group_Id: Int
  ): commonResponse!
  addCandidateInviteOnboard(
    Expire_Value:Int!
    Expire_Type:Int!
    Department_Id:Int!
    Designation_Id:Int!
    Location_Id:Int!
    Date_Of_Join:String!
    Job_Code:String!
    Probation_Date:String
    EmpType_Id:Int!
    Manager_Id:Int!
    Work_Schedule:Int!
    Service_Provider_Id:Int!
    Business_Unit_Id:Int!
    Name:String!
    MailTo:String!
    candidateId: Int!
    organizationGroupId:Int
    groupIds: [Int]
    accreditationGroupIds: [Int]
    Job_Role_Ids:[Int]
  ):commonResponse!
  updateContactDetails(
    candidateId: Int!,
    permanent_appartmentName: String!,
    permanent_streetName: String,
    permanent_city: String!,
    permanent_state: String!,
    permanent_country: String!,
    permanent_pinCode: String!,
    current_appartmentName: String!,
    current_streetName: String,
    current_city: String!,
    current_state: String!,
    current_country: String!,
    current_pinCode: String!,
    office_appartmentName: String!,
    office_streetName: String,
    office_city: String!,
    office_state: String!,
    office_country: String!,
    office_pinCode: String!,
    permanent_barangay: String,
    permanent_barangay_id: Int,
    permanent_city_id: Int,
    permanent_region: String,
    current_barangay: String,
    current_barangay_id: Int,
    current_city_id: Int,
    current_region: String,
    office_barangay: String,
    office_barangay_id: Int,
    office_city_id: Int,
    office_region: String,
    landlineNo: String,
    mobileNo: String!,
    mobileNoCountryCode: String!,
    useLocationAddress: Int!
    faxNo: String,
    emergencyContactName: String
    emergencyContactRelation: String
  ): commonResponse!
  addUpdateSkillDetails(
    candidateId: Int!,
    primarySkill: String,
    secondarySkill: String,
    knownSkills: String,
    proficiency: String,
  ): commonResponse
  addUpdateCandidateDocuments(
    documentId: Int
    candidateId: Int!,
    documentName: String!,
    documentType: Int!
    documentCategory: Int!
    documentSubType: Int!,
    fileName: String!,
    fileSize: String,
  ): commonResponse!
  addUpdateDependentDetails (
    candidateId: Int!
    dependentId: Int
    dependentFirstName: String!
    dependentLastName: String!
    gender: String!
    genderId: Int
    relationship: String!
    dependentDOB: String!,
  ): commonResponse!
  addUpdateExperienceDetails (
    candidateId: Int!
    experienceId: Int
    companyName: String!
    companyLocation: String!
    designation: String!
    startDate: String!
    endDate: String!
    duration: String!
    years: Int!
    months: Int!
    fileName: String
    fileSize: String
    referenceDetails: [candidateExperienceReference]
  ): commonResponse!
  addUpdateAssetDetails(
    candidateId: Int!
    assetId: Int
    assetName: String!
    serialNo: String!
    receiveDate: String!
    returnDate: String
  ): commonResponse!
  addUpdateAwardDetails(
    candidateId: Int!,
    awardId: Int,
    awardName: String!,
    receivedOn: String!,
    receivedFrom: String!,
    receivedFor: String!
  ): commonResponse
  addUpdateBankDetails(
    candidateId: Int!,
    bankId: Int,
    bankAccountName: String,
    bankAccountNumber: String!,
    empBankId: Int!,
    branchName: String,
    ifsc: String,
    bsb: String,
    bank_streetName: String,
    bank_city: String,
    bank_state: String,
    bank_pinCode: String,
    accountType: Int!,
    creditAccount: String!
    beneficiaryId: String
    status: String!
    fileName: String,
  ): commonResponse!
  addUpdateInsuranceDetails(
    candidateId: Int!,
    policyId: Int
    insuranceTypeId: Int!,
    policyNo: String!,
    insuranceType: String,
  ): commonResponse!
  addUpdateAccreditationDetails(
    candidateId: Int!,
    accreditationDetailId: Int
    accreditationCategoryAndType: Int!,
    receivedDate: String,
    identifier: String
    expiryDate: String,
    fileName: String!,
    examRating: Int
    examDateYear: Int
    examDateMonth: String
    dependentId: Int
  ): commonResponse!
  addUpdatePassportDetails (
    candidateId: Int!
    passportNo: String!
    passportIssueDate: String!
    passportExpiryDate: String!
    issuingAuthority: String!
    issuingCountry: String!,
    fileName: String
  ): commonResponse!
  addUpdateDrivingLicenseDetails (
    candidateId: Int!
    drivingLicenseNo: String!
    licenseIssueDate: String!
    licenseExpiryDate: String!
    issuingAuthority: String!
    issuingCountry: String!
    issuingState: String!
    vehicleType: String,
    fileName: String
  ): commonResponse!
  addUpdateEducationDetails(
    educationId: Int
    candidateId: Int!,
    educationType: Int,
    specialisation: String,
    instituteName: String,
    specializationId: Int,
    institutionId:Int,
    university: String,
    yearOfStart: Int,
    yearOfPassing: Int,
    percentage: Float,
    grade: String,
    documentSubTypeId: Int,
    fileName: String
    startDate: Date
    endDate: Date
    city: String
    state: String
    country: String
  ): commonResponse!
  addUpdateCertificationDetails(
    certificationId: Int
    candidateId: Int!,
    certificationName: String!,
    receivedDate: String!,
    receivedFrom: String!,
    ranking: String,
    documentSubTypeId: Int,
    fileName: String
  ): commonResponse!
  addUpdateTrainingDetails(
    trainingId: Int
    candidateId: Int!,
    trainingName: String!,
    trainingStartDate: String!,
    trainingEndDate: String!,
    trainingDuration: String!,
    trainer: String!,
    center: String!,
    documentSubTypeId: Int,
    fileName: String
  ): commonResponse!
  updateProfilePhoto(
    candidateId: Int!
    photoPath: String!
  ): commonResponse!
  updateCandidateStatus(
    candidateId: Int!
    status: String!
    formId: Int!
    templateId: Int
  ): commonResponse!
  convertCandidateToEmployee(
    Candidate_Id: Int!
    Roles_Id: Int
    empPrefixSettingId: Int
    Job_Role_Ids:[Int]
    User_Defined_EmpId: String!
    External_EmpId: String!
    Emp_Email: String
    Career_Id: Int
    Timekeeping_Id: Int
  ): commonResponse!
  generatePassCodeForPortalAccess(
    emailId: String!
  ): commonResponse
  passcodeValidatePortalAccess(
    emailId: String!
    passCode: Int!
  ): commonResponse
}

type updatePersonalInfoResponse{
  errorCode: String
  message: String
  candidateId: Int
}


input vendorData {
  vendorId: Int!
  statusLevel: String
}

input employeeData {
  Designation_Id: Int
  Department_Id: Int
  Location_Id: Int
  Job_Code: String
  Date_Of_Join: String
  Probation_Date: String
  EmpType_Id: Int
  Manager_Id: Int
  Work_Schedule: Int
  firstName: String
  lastName: String
  email: String
  expireType: Int
  expireValue: Int
  nominateAsAdmin: Int
}

type retrieveAccreditationAndTypeResponse {
  errorCode: String
  message: String
  accreditationAndType: [accreditationAndType]
}

type accreditationAndType {
  accreditationCategoryAndTypeId: Int!
  accreditationCategory: String!
  accreditationType: String!
}

type commonResponse {
  errorCode: String
  message: String
  data: String
}

input language{
  Lang_Known: Int
  Lang_Spoken: Int
  Lang_Read_Write: Int
  Lang_Proficiency: String
}

input candidateExperienceReference{
  Reference_Name: String
  Reference_Email: String
  Reference_Number: String
}

schema {
  query: Query
  mutation: Mutation
}
