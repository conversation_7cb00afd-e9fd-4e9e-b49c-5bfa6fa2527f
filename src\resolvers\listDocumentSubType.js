const resolvers = {
    Query: {
        listDocumentSubType: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_url')
                    .where('Url_Hash', args.Url_Hash)
                    .then((url) => {
                        if (!url[0] && !args.Vendor_Based) {
                            throw new Error('REO0101')
                        } else {
                            return knexconfig("document_sub_type")
                                .orderBy('Document_Sub_Type', 'asc')
                                .where('Document_Type_Id', args.type_id)
                                .modify(function (queryBuilder) {
                                    if (args.isDefault!== undefined && args.isDefault!== null) {
                                        queryBuilder.where('Is_Default', args.isDefault)
                                    }
                                })
                                .where('Only_For_Email', 'No')
                                .then((subType) => {
                                    if (!subType[0]) {
                                        return {
                                            documentSubType: null
                                        }
                                    } else {
                                        return {
                                            documentSubType: subType
                                        }
                                    }
                                })
                        }
                    })
                    .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                console.log('return successful response from listDocumentSubType')
                return result;
            }).catch(function (err) {
                console.log('Error in listDocumentSubType', err);
                if (err.message == "REO0101") {
                    console.log('URL not found')
                    throw new ApolloError("URL not found", "REO0101")
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;