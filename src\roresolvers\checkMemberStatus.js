//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');


//fuction to check the members status associated to the vendor before changing the status
let organizationDbConnection;
module.exports.checkMemberStatus = async (parent, args, context, info) => {
    console.log('Inside checkMemberStatus function');
    let validationError = {}
    try {
        if (!args.vendorId) {
            validationError['IVE0262'] = commonLib.func.getError('', 'IVE0262').message;
        }
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            if (Object.keys(validationError).length == 0) {
                return (
                    organizationDbConnection(ehrTables.empJob)
                        .select("Service_Provider_Id as vendorId")
                        .where('Service_Provider_Id', args.vendorId)
                        .andWhere('Emp_Status', 'Active')
                        .from(ehrTables.empJob)
                        .then(async (res) => {
                            if (res.length) {
                                //Active members associcated with vendor are found
                                throw 'VO0132';
                            } else {
                                //No active members are found
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: `No active members associated with the vendor` };
                            }
                        })
                        .catch((err)=>{
                            console.log('Error while checking the member status', err)
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            if(err.code = 'VO0132'){
                                let errResult = commonLib.func.getError(err, 'VO0132');
                                throw new ApolloError(errResult.message, errResult.code)
                            }else{
                                let errResult = commonLib.func.getError(err, 'VO0134');
                                throw new ApolloError(errResult.message, errResult.code)
                            }
                        })
                )
            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to check member status');
            throw '_DB0111';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in checkMemberStatus function main catch block.', e);
        if (e === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in checkMemberStatus function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(e, 'VO0020');
            throw new ApolloError(errResult.message, errResult.code)
        }
    }
}