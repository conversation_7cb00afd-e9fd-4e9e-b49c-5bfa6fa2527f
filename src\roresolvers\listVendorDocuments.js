//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');

//fuction to list vendor employees
let organizationDbConnection;
module.exports.listVendorDocuments = async (parent, args, context, info) => {
    console.log('Inside listVendorDocuments function', args);
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id ? context.Employee_Id : null;
        let checkRights;
        if (args.checkAccess) {
            checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        }
        if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1)) {
            return (
                organizationDbConnection(ehrTables.vendorDocumentCategory)
                    .select('VDC.Document_Id', 'VDC.Effective_Date', 'VDC.End_Date', 'VDC.Vendor_Id', 'VDC.Document_Name', 'DC.Category_Id', 'DC.Category_Fields as Category_Name', 'DT.Document_Type_Id',
                        'DT.Document_Type', 'DST.Document_Sub_Type_Id', 'DST.Document_Sub_Type', 'VD.File_Name', 'VDC.Added_On', 'VDC.Updated_On', organizationDbConnection.raw("CONCAT(EPI1.Emp_First_Name, ' ', EPI1.Emp_Last_Name) as Added_By"),
                        organizationDbConnection.raw("CONCAT(EPI2.Emp_First_Name, ' ', EPI2.Emp_Last_Name) as Updated_By"), "DST.Mandatory",
                        "DST.Instruction")
                    .leftJoin(ehrTables.documentCategory + " as DC", "DC.Category_Id", "VDC.Category_Id")
                    .leftJoin(ehrTables.documentType + " as DT", "DT.Document_Type_Id", "VDC.Document_Type_Id")
                    .leftJoin(ehrTables.documentSubType + " as DST", "DST.Document_Sub_Type_Id", "VDC.Document_Sub_Type_Id")
                    .leftJoin(ehrTables.vendorDocuments + " as VD", "VD.Document_Id", "VDC.Document_Id")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI1", "EPI1.Employee_Id", "VDC.Added_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "VDC.Updated_By")
                    .from(ehrTables.vendorDocumentCategory + " as VDC")
                    .where('VDC.Vendor_Id', args.vendorId)
                    .then((data) => {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Vendor's Employee data has been fetched successfully.", vendorDocuments: data };
                    })
                    .catch((catchError) => {
                        console.log('Error in listVendorDocuments .catch() block', catchError);
                        let errResult = commonLib.func.getError(catchError, 'VO0136');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        } else {
            console.log('No rights to view the vendor documents');
            throw '_DB0100';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listVendorDocuments function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'VO0022');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
