//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
let moment = require('moment-timezone');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');

//function to add/update vendor bank details
module.exports.addUpdateVendorBankDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateVendorBankDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id ? context.Employee_Id : null;
        let vendorBankId = args.Vendor_Bank_Id
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights;
        if (args.checkAccess) {
            checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        }
        if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1))) {
            validationError = await bankDetailsValidation(args);
            if (Object.keys(validationError).length == 0) {
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (vendorBankId) {
                                if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1)) {
                                    return (
                                        organizationDbConnection(ehrTables.vendorBankDetails)
                                            .update({
                                                'Bank_Account_Number': args.Bank_Account_Number,
                                                'Bank_Name': args.Bank_Name,
                                                'Bank_Id': args.Bank_Id,
                                                'Bank_Name': args.Bank_Name,
                                                'Branch_Name': args.Branch_Name,
                                                'IFSC_Code': args.IFSC_Code,
                                                'BSB_Code': args.BSB_Code,
                                                'Street': args.Street,
                                                'City': args.City,
                                                'State': args.State,
                                                'Zip': args.Zip,
                                                'Account_Type_Id': args.Account_Type_Id,
                                                'Credit_Account': args.Credit_Account,
                                                'Beneficiary_Id': args.Beneficiary_Id,
                                                'Status': args.Status,
                                                'Updated_On': moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                                                'Updated_By': loginEmployeeId ? loginEmployeeId : null
                                            })
                                            .where('Vendor_Bank_Id', vendorBankId)
                                            .transacting(trx)
                                            .then((updateVendorBankDetails) => {
                                                if (updateVendorBankDetails) {
                                                    return 'success';
                                                } else {
                                                    console.log('Error while updating the vendor bank details', updateVendorBankDetails)
                                                    throw 'VO0130';
                                                }
                                            })
                                    )
                                } else {
                                    console.log('No rights to update vendor bank details')
                                    throw '_DB0102'
                                }
                            } else {
                                if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1)) {
                                    return (
                                        organizationDbConnection(ehrTables.vendorBankDetails)
                                            .insert({
                                                'Vendor_Id': args.Vendor_Id,
                                                'Bank_Account_Number': args.Bank_Account_Number,
                                                'Bank_Name': args.Bank_Name,
                                                'Bank_Id': args.Bank_Id,
                                                'Bank_Name': args.Bank_Name,
                                                'Branch_Name': args.Branch_Name,
                                                'IFSC_Code': args.IFSC_Code,
                                                'BSB_Code': args.BSB_Code,
                                                'Street': args.Street,
                                                'City': args.City,
                                                'State': args.State,
                                                'Zip': args.Zip,
                                                'Account_Type_Id': args.Account_Type_Id,
                                                'Credit_Account': args.Credit_Account,
                                                'Beneficiary_Id': args.Beneficiary_Id,
                                                'Status': args.Status,
                                                'Added_On': moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                                                'Added_By': loginEmployeeId ? loginEmployeeId : null
                                            })
                                            .transacting(trx)
                                            .then((insertVendorBank) => {
                                                if (insertVendorBank) {
                                                    return 'success'
                                                } else {
                                                    console.log('Error while inserting the vendor bank details', insertVendorBank)
                                                    throw 'VO0130';
                                                }
                                            })
                                    )
                                } else {
                                    console.log('No rights to add vendor bank details')
                                    throw '_DB0101'
                                }
                            }
                        })
                        .then((response) => {
                            if (response) {
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "vendor bank details has been submitted successfully" };
                            } else {
                                throw 'VO0114'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateVendorBankDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'VO0142');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                throw 'IVE0000'
            }

        } else {
            console.log('No rights to add / update the vendor bank details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateVendorBankDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'VO0028');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function bankDetailsValidation(args) {
    console.log('Inside bankDetailsValidation function');
    let ifscValidation = /^[A-Z]{4}[0][A-Z0-9]{6}$/
    let bsbValidation = /\d{3}-?\d{3}/
    try {
        let validationError = {};
        //Validate the Bank_Account_Number
        if (!(commonLib.commonValidation.checkLength(args.Bank_Account_Number, 3, 30))) {
            validationError['IVE0015'] = commonLib.func.getError('', 'IVE0015').message;
        }

        //Validate the Bank_Name
        if (args.Bank_Name) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.Bank_Name)) {
                validationError['IVE0303'] = commonLib.func.getError('', 'IVE0303').message;
            }
            else if (!commonLib.commonValidation.checkLength(args.Bank_Name, 3, 50)) {
                validationError['IVE0303'] = commonLib.func.getError('', 'IVE0303').message1;
            }
        }

        //Validate the Branch_Name
        if (args.Branch_Name) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.Branch_Name)) {
                validationError['IVE0304'] = commonLib.func.getError('', 'IVE0304').message;
            }
            else if (!commonLib.commonValidation.checkLength(args.Branch_Name, 3, 50)) {
                validationError['IVE0304'] = commonLib.func.getError('', 'IVE0304').message1;
            }
        }

        //Validate the IFSC_Code
        if (args.IFSC_Code) {
            if (!ifscValidation.test(args.IFSC_Code)) {
                validationError['IVE0305'] = commonLib.func.getError('', 'IVE0305').message;
            }
        }

        //Validate the BSB_Code
        if (args.BSB_Code) {
            if (!bsbValidation.test(args.BSB_Code)) {
                validationError['IVE0306'] = commonLib.func.getError('', 'IVE0306').message8;
            }
        }

        //Validate the Street
        if (args.Street) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.Street)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message;
            }
            else if (!commonLib.commonValidation.checkLength(args.Street, 3, 300)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message1;
            }
        }

        //Validate the City
        if (args.City) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.City)) {
                validationError['IVE0308'] = commonLib.func.getError('', 'IVE0308').message;
            }
            else if (!commonLib.commonValidation.checkLength(args.City, 3, 50)) {
                validationError['IVE0308'] = commonLib.func.getError('', 'IVE0308').message1;
            }
        }

        //Validate the State
        if (args.State) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.State)) {
                validationError['IVE0309'] = commonLib.func.getError('', 'IVE0309').message;
            }
            else if (!commonLib.commonValidation.checkLength(args.State, 3, 50)) {
                validationError['IVE0309'] = commonLib.func.getError('', 'IVE0309').message1;
            }
        }
        return validationError;
    }
    catch (err) {
        console.log('Error in the bankDetailsValidation() function in the main catch block.', err);
        throw err;
    }
}

