
exports.up = function(knex, Promise) {
  return knex.schema.createTable('candidate_dependent',function(table){
      table.increments('Dependent_Id',11).primary();
      table.integer('Candidate_Id',11).notNullable();
      table.string('Dependent_First_Name',30).notNullable();
      table.string('Dependent_Last_Name',30).notNullable();
      table.string('Gender',10);
      table.string('Relationship',50).notNullable();
      table.date('Dependent_DOB').notNullable();
  })
};

exports.down = function(knex, Promise) {
    return knex.schema.dropTable('candidate_dependent');
};
