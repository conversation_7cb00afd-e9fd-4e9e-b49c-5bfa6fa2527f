//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { validateInviteVendorData } = require('../common/inviteVendorValidation');
const { formName, systemLogs } = require('../common/appConstants');
const { sendEmail, getOrganizationName } = require('../common/commonFunctions');
const SHA3 = require('sha3');
let randomize = require('randomatic');
let moment = require('moment-timezone');
const { encodeUsingBase64, getLocationDetails } = require('../common/commonFunctions');

module.exports.generateAndSendURL = async (parent, args, context, info) => {
    console.log('Inside generateAndSendURL function');
    let organizationDbConnection;
    let validationError = {};
    let loginEmployeeId = context.Employee_Id;
    let orgCode = context.Org_Code;
    let systemLogParam;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        //Form Access check for add and update invited vendor data
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        let orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection, 1);
        let companyName = orgDetails.orgName ? orgDetails.orgName : orgCode
        let companyEmail = orgDetails.hrAdminEmailAddress ? orgDetails.hrAdminEmailAddress : ''
        let locationDetails = await getLocationDetails(organizationDbConnection, args.locationId);

        //Code when we introduce new location

        let countryCode;
        if (locationDetails) {
            if (locationDetails.length > 0 && locationDetails[0]['Country_Code']) {
                countryCode = locationDetails[0]['Country_Code'];
            }
        }
        else {
            throw new Error('Error occurred while getting locationDetails');
        }

        if (Object.keys(checkRights).length > 0 && checkRights.Role_Add === 1 || checkRights.Role_Update === 1) {
            //Check if vendor is already invited
            let invitedVendorId = (args.invitedVendorId) ? args.invitedVendorId : 0;
            validationError = await validateInviteVendorData(args, invitedVendorId);
            if (Object.keys(validationError).length == 0) {
                let hash = new SHA3.SHA3Hash().update(' ' + Date.now()).digest('hex');
                //get appmanager db connection
                let appManagerDbConnection = knex(context.connection.AppManagerDb);
                let regionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection, orgCode);
                appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                if (Object.keys(regionDetails).length == 0) {
                    throw new Error('Region details not found.');
                }
                let baseUrl = process.env.baseAUUrlUI;
                let full_url = baseUrl + companyName + '&country=' + countryCode + '&code=' + orgCode + '&companyEmail=' + companyEmail + '&isVendor=yes' + '&identifier=' + hash + '&d_code=' + regionDetails.Data_Region + '&b_code=' + regionDetails.Bucket_Region;
                let index = full_url.indexOf('?');
                let encodeParam = (full_url.substr(index + 1));
                let leftUrl = full_url.substr(0, index);
                full_url = leftUrl + '?' + await encodeUsingBase64(encodeParam);
                let pin = randomize('?', 6, { chars: '*********' });
                let expire_time;
                let duration_type;
                let loginEmployeeCurrentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                if (args.expireType == 1) {
                    duration_type = "Minutes"
                    expire_time = moment.utc().add(args.expireValue, 'minutes').format('YYYY-MM-DD HH:mm:ss');
                } else if (args.expireType == 2) {
                    duration_type = "Hours"
                    expire_time = moment.utc().add(args.expireValue, 'hours').format('YYYY-MM-DD HH:mm:ss');
                } else {
                    duration_type = "Days"
                    expire_time = moment.utc().add(args.expireValue, 'days').format('YYYY-MM-DD HH:mm:ss');
                }

                return (
                    organizationDbConnection
                        .transaction(async (trx) => {
                            let invitedVendorData = {
                                Url_Hash: hash,
                                Url: full_url,
                                Pass_Code: pin,
                                Expire_Time: expire_time,
                                Location_Id: args.locationId,
                                Vendor_Name: args.vendorName,
                                Vendor_Email: args.vendorEmail,
                                URL_Expiry_Duration_Measure: duration_type,
                                URL_Expiry_Duration: args.expireValue,
                                Manager_Id: args.managerId ? args.managerId : null,
                                Department_Id: args.departmentId ? args.departmentId : null,
                                Type_Of_Industry: args.typeOfIndustry ? args.typeOfIndustry : null,
                                Service_Priority: args.servicePriority ? args.servicePriority : 'Critical',
                                Status: "Invited",
                            }
                            let uploadData = {
                                Expire_Time: expire_time,
                                Location_Id: args.locationId,
                                Onboarding_Form_Id: args.onboardingFormId ? args.onboardingFormId : null,
                                Vendor_Name: args.vendorName,
                                Vendor_Email: args.vendorEmail,
                                Manager_Id: args.managerId ? args.managerId : null,
                                Department_Id: args.departmentId ? args.departmentId : null,
                                Type_Of_Industry: args.typeOfIndustry ? args.typeOfIndustry : null,
                                Service_Priority: args.servicePriority ? args.servicePriority : 'Critical',
                            }
                            // if it is update we will send the vendorId
                            if (invitedVendorId > 0 && checkRights.Role_Update === 1) {
                                uploadData.Updated_On = loginEmployeeCurrentDateTime;
                                uploadData.Updated_By = loginEmployeeId;
                                return (
                                    organizationDbConnection(ehrTables.invitedVendors)
                                        .update(uploadData)
                                        .where('Invited_Vendor_Id', invitedVendorId)
                                        .then(async (updateResult) => {
                                            if (updateResult) {
                                                return { errorCode: "", message: "Invited vendor has been updated successfully." };
                                            }
                                            else {
                                                throw 'VO0105';
                                            }
                                        })
                                )
                            }

                            // Insert if there is no invited vendor id
                            else if (checkRights.Role_Add === 1) {
                                invitedVendorData.Added_On = loginEmployeeCurrentDateTime;
                                invitedVendorData.Added_By = loginEmployeeId;
                                return (organizationDbConnection(ehrTables.serviceProvider)
                                    .insert({
                                        Service_Provider_Name: args.vendorName,
                                        Email_Id: args.vendorEmail,
                                        Location_Id: args.locationId,
                                        Onboarding_Form_Id: args.onboardingFormId ? args.onboardingFormId : null,
                                        Manager_Id: args.managerId ? args.managerId : null,
                                        Department_Id: args.departmentId ? args.departmentId : null,
                                        Type_Of_Industry: args.typeOfIndustry ? args.typeOfIndustry : null,
                                        Service_Priority: args.servicePriority ? args.servicePriority : 'Critical',
                                        Added_On: loginEmployeeCurrentDateTime,
                                        Added_By: loginEmployeeId
                                    })
                                    .transacting(trx)
                                    .then(async (vendorId) => {
                                        if (vendorId) {
                                            let addedVendorId = vendorId[0]
                                            await updateVendorGroupDetails(organizationDbConnection, trx, args.groups, addedVendorId);
                                            invitedVendorData.Vendor_Id = addedVendorId;
                                            return (
                                                organizationDbConnection(ehrTables.invitedVendors)
                                                    .insert(invitedVendorData)
                                                    .transacting(trx)
                                                    .then(async (inviteId) => {
                                                        systemLogParam = {
                                                            action: systemLogs.roleAdd,
                                                            userIp: context.User_Ip,
                                                            employeeId: loginEmployeeId,
                                                            formName: formName.vendorOnboarding,
                                                            trackingColumn: '',
                                                            organizationDbConnection: organizationDbConnection,
                                                            uniqueId: addedVendorId
                                                        };
                                                        if (inviteId) {
                                                            let organizationName = await getOrganizationName(organizationDbConnection, orgCode);
                                                            let buttonColors = await commonLib.func.getButtonColor(organizationDbConnection);
                                                            const templateData = {
                                                                employeeName: args.vendorName,
                                                                orgLogo: orgDetails.logoPath ? orgDetails.logoPath : '',
                                                                topCardImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + '/Email-Template-Images/topCard.png',
                                                                redirectionUrl: full_url,
                                                                domainName: process.env.domainName.toUpperCase(),
                                                                domainContentInSubject: "Hello",
                                                                registrationImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + '/Email-Template-Images/RegistrationImage.png',
                                                                notificationSubject: "Invitation to join " + organizationName.orgName ? organizationName.orgName : orgCode,
                                                                isEmailClient: 0,
                                                                isVendorBased: 1,
                                                                isCandidate: 0,
                                                                passCode: pin,
                                                                orgCode: organizationName.orgName ? organizationName.orgName : orgCode,
                                                                bgColors: buttonColors,
                                                            }
                                                            const params = {
                                                                "Source": process.env.emailFrom,
                                                                "Template": "welcomeEmailToClient",
                                                                "Destination": {
                                                                    "ToAddresses": [args.vendorEmail]
                                                                },
                                                                "ReplyToAddresses": orgDetails.hrAdminEmailAddress ? [orgDetails.hrAdminEmailAddress] : [process.env.emailReplyTo],
                                                                "TemplateData": JSON.stringify(templateData)
                                                            }

                                                            let emailResponse = await sendEmail(params);
                                                            if (emailResponse) {
                                                                return { errorCode: "", message: "Vendor has been invited successfully" };
                                                            } else {
                                                                return (organizationDbConnection(ehrTables.invitedVendors)
                                                                    .update({
                                                                        Status: 'Pending'
                                                                    })
                                                                    .where('Vendor_Id', addedVendorId)
                                                                    .transacting(trx)
                                                                    .then(async (updateResult) => {
                                                                        console.log('Something went wrong while inviting through mail', updateResult)
                                                                        return { errorCode: "", message: "Something went wrong while inviting through mail" }
                                                                    })
                                                                )
                                                            }
                                                        }

                                                    })
                                                    .catch((err) => {
                                                        console.log('Error while inviting the vendor', err);
                                                        throw 'VO0106';
                                                    })
                                            )
                                        } else {
                                            throw 'VO0106';
                                        }
                                    })
                                )
                            }


                        })
                        .then(async (response) => {
                            //Call the function to add the system log
                            await commonLib.func.createSystemLogActivities(systemLogParam);
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Vendor has been invited / details are updated successfully." };
                        }
                        )
                        .catch((catchError) => {
                            console.log('Error in generateAndSendURL .catch() block', catchError);
                            errResult = commonLib.func.getError(catchError, 'VO0107');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                );
            }
            else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to invite or update vendors');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in generateAndSendURL function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in generateAndSendURL function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(mainCatchError, 'VO0003');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

//Function to add/update vendor group details
async function updateVendorGroupDetails(organizationDbConnection, trx, groups, vendorId) {
    try {
        let vendorGroupData = [];
        if (groups && groups.length > 0) {
            //Form the vendor group data
            vendorGroupData = groups.map((groupId) => {
                return {
                    Vendor_Id: vendorId,
                    Group_Id: groupId
                }
            })
            await organizationDbConnection(ehrTables.vendorDocumentEnforcementGroups)
                .insert(vendorGroupData)
                .transacting(trx);
        }
        return true
    }
    catch (error) {
        console.log('Error in updateVendorGroupDetails function', error);
        throw error;
    }
}
