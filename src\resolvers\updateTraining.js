const resolvers = {
    Mutation : {
        updateTraining : async (root,args) =>{
            var dateFormat = require('dateformat');
            var calculateFullAge = require('full-age-calculator');
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        if(!args.Training_Name) {
                            throw new Error('Training Name should not be empty');
                        } else if(args.Training_Name.length > 30) {
                            throw new Error('Training Name should be less than or equal to 30 characters');
                        } else if(!args.Trainer) {
                            throw new Error("Trainer Name should not be empty");
                        } else if(!args.Center) {
                            throw new Error("Center Name should not be empty");
                        }
                        return knexconfig('candidate_training')
                        .where('Training_Id',args.Training_Id)
                        .andWhere('Candidate_Id',url[0].Candidate_Id)
                        .then((details)=>{
                            if(!details[0]){
                                throw new Error("Training detail not found")
                            } else {
                                try{
                                    var Training_Start_Date = (!args.Training_Start_Date || isNaN(Date.parse(args.Training_Start_Date))) ? (null) : ((isNaN(Number(args.Training_Start_Date))) ? dateFormat(new Date((args.Training_Start_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Training_Start_Date)), "yyyy-mm-dd"));
                                    var Training_End_Date = (!args.Training_End_Date || isNaN(Date.parse(args.Training_End_Date))) ? (null) : ((isNaN(Number(args.Training_End_Date))) ? dateFormat(new Date((args.Training_End_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Training_End_Date)), "yyyy-mm-dd"));
                                }
                                catch(err){
                                    throw new Error("Invalid Date Format");
                                }
                                var experience = '';
                                if(Training_End_Date){
                                    var date = calculateFullAge.getFullAge(Training_Start_Date,Training_End_Date)
                                    if (date.years !== 0) {
                                        experience = experience + date.years + ' years';
                                    }
                                    if (date.months !== 0) {
                                        experience = experience + ' ' + date.months + ' months';
                                    }
                                    if (date.days !== 0) {
                                        experience = experience + ' ' + date.days + ' days';
                                    }
                                }
                                return knexconfig('candidate_training')
                                .where('Training_Id',args.Training_Id)
                                .andWhere('Candidate_Id',url[0].Candidate_Id)
                                .update({
                                    Training_Name: (!args.Training_Name) ? (null) : (args.Training_Name),
                                    Training_Start_Date: Training_Start_Date,
                                    Training_End_Date: Training_End_Date,
                                    Training_Duration: (!experience) ? (null) : (experience),
                                    Trainer: (!args.Trainer) ? (null) : (args.Trainer),
                                    Center: (!args.Center) ? (null) : (args.Center),
                                })
                                .transacting(trc)
                                .then(()=>{
                                    return knexconfig('candidate_training_documents')
                                    .where('Training_Id',args.Training_Id)
                                    .then((document_exist)=>{
                                        if(!document_exist[0]){
                                            if(!args.File_Name){
                                                return {
                                                    message:"Education details updated"
                                                }
                                            } else {
                                                return knexconfig('candidate_training_documents')
                                                .insert({
                                                    Training_Id: args.Training_Id,
                                                    File_Name: args.File_Name,
                                                    Document_Name: args.Document_Name,
                                                    Sub_Type_Id: args.Sub_Type_Id
                                                })
                                                .transacting(trc)
                                                .then(()=>{
                                                    return {
                                                        message:"Education details updated"
                                                    }
                                                })
                                            }
                                        } else {
                                            if(!args.File_Name){
                                                return knexconfig('candidate_training_documents')
                                                .where('Training_Id',args.Training_Id)
                                                .del()
                                                .transacting(trc)
                                                .then(()=>{
                                                    return {
                                                        message:"Education details updated"
                                                    }
                                                })
                                            } else {
                                                return knexconfig('candidate_training_documents')
                                                .where('Training_Id',args.Training_Id)
                                                .update({
                                                    File_Name: args.File_Name,
                                                    Document_Name: args.Document_Name,
                                                    Sub_Type_Id: args.Sub_Type_Id
                                                })
                                                .transacting(trc)
                                                .then(()=>{
                                                    return {
                                                        message:"Education details updated"
                                                    }
                                                })
                                            }
                                        }
                                    })
                                })
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from updateTraining');
                return result;
            }).catch(function(err){
                console.log('Error in updateTraining',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Training Name should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Training Name should be less than or equal to 30 characters"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Trainer Name should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Center Name should not be empty"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Invalid Date Format"){
                    throw new UserInputError(err.message)
                } else if (err.message == "Training detail not found"){
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;