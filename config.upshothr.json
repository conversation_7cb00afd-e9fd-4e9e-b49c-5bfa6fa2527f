{"securityGroupIds": ["sg-04bc41f567ae532b6"], "subnetIds": ["subnet-0cbbb2391d86f1d0e", "subnet-0120a712c5936a292"], "KmsKeyId": "arn:aws:kms:eu-west-2:327313496531:key/ce09f3ce-87c9-4552-b25f-505c6bb1e246", "role": "arn:aws:iam::327313496531:role/lambdaFullAccess", "secretName": "PROD/UPSHOT/PGACCESS", "imageBucket": "s3.images.upshothr.uk", "documentBucket": "s3.taxdocs.upshothr.uk", "baseUrlUI": "http://onboard.upshothr.uk?companyName=", "emailFrom": "<EMAIL>", "emailReplyTo": "<EMAIL>", "domainName": "upshothr", "fullDomainName": ".upshothr.uk", "sesRegion": "eu-west-2", "logoBucket": "s3.logos.upshothr.uk", "customDomainName": "onboardapi.upshothr.uk", "dbPrefix": "upshothr_", "baseAUUrlUI": "http://onboarding.upshothr.uk?companyName=", "vendorBucketName": "supplier.upshothr.uk", "authorizerARN": "arn:aws:lambda:ap-south-1:327313496531:function:ATS-upshothr-firebaseauthorizer", "resendInviteVendorStepFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothrResendInviteVendorStepFunction", "resendInviteCandidateStepFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothrResendInviteCandidateStepFunction", "asyncSyntrumAPIStepFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-asyncSyntrumAPIFunction", "resourceArnPrefix": "arn:aws:lambda:eu-west-2:327313496531:function:HRAPPOnboard-upshothr", "snsRegion": "eu-west-2", "snsEndpoint": "sns.eu-west-2.amazonaws.com"}