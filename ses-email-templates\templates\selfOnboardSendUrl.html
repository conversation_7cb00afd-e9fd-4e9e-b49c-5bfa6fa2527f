<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
  <!-- NAME: 1 COLUMN - BANDED -->
  <!--[if gte mso 15]>
      <xml>
        <o:OfficeDocumentSettings>
          <o:AllowPNG/>
          <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
      </xml>
    <![endif]-->
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Self Onboard</title>
 
  <style type="text/css">
    p{
        margin:10px 0;
        padding:0;
      }
      table{
        border-collapse:collapse;
      }
      h1,h2,h3,h4,h5,h6{
        display:block;
        margin:0;
        padding:0;
      }
      img,a img{
        border:0;
        height:auto;
        outline:none;
        text-decoration:none;
      }
      body,#bodyTable,#bodyCell{
        height:100%;
        margin:0;
        padding:0;
        width:100%;
      }
      #outlook a{
        padding:0;
      }
      img{
        -ms-interpolation-mode:bicubic;
      }

      .ReadMsgBody{
        width:100%;
      }
      .ExternalClass{
        width:100%;
      }

      a[href^=tel],a[href^=sms]{
        color:inherit;
        cursor:default;
        text-decoration:none;
      }
      p,a,li,td,body,table,blockquote{
        -ms-text-size-adjust:100%;
        -webkit-text-size-adjust:100%;
      }
      .ExternalClass,.ExternalClass p,.ExternalClass td,.ExternalClass div,.ExternalClass span,.ExternalClass font{
        line-height:100%;
      }
      a[x-apple-data-detectors]{
        color:inherit !important;
        text-decoration:none !important;
        font-size:inherit !important;
        font-family:inherit !important;
        font-weight:inherit !important;
        line-height:inherit !important;
      }
      .templateContainer{
        max-width:600px !important;
      }
      a.mcnButton{
        display:block;
      }
      .mcnImage{
        vertical-align:bottom;
      }
      .mcnTextContent{
        word-break:break-word;
      }
      .mcnTextContent img{
        height:auto !important;
      }
      .a6S {display: none !important;}
      .mcnDividerBlock{
        table-layout:fixed !important;
      }
      /*
      @tab Page
      @section Background Style
      @tip Set the background color and top border for your email. You may want to choose colors that match your company's branding.
      */
      body,#bodyTable{
        /*@editable*/background-color:#1f2a44;
      }
      /*
      @tab Page
      @section Background Style
      @tip Set the background color and top border for your email. You may want to choose colors that match your company's branding.
      */
      #bodyCell{
        /*@editable*/border-top:0;
      }
      /*
      @tab Page
      @section Heading 1
      @tip Set the styling for all first-level headings in your emails. These should be the largest of your headings.
      @style heading 1
      */
      h1{
        /*@editable*/color:#202020;
        /*@editable*/font-family:'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;
        /*@editable*/font-size:26px;
        /*@editable*/font-style:normal;
        /*@editable*/font-weight:bold;
        /*@editable*/line-height:125%;
        /*@editable*/letter-spacing:normal;
        /*@editable*/text-align:left;
      }
      /*
      @tab Page
      @section Heading 2
      @tip Set the styling for all second-level headings in your emails.
      @style heading 2
      */
      h2{
        /*@editable*/color:#202020;
        /*@editable*/font-family:Helvetica;
        /*@editable*/font-size:22px;
        /*@editable*/font-style:normal;
        /*@editable*/font-weight:bold;
        /*@editable*/line-height:125%;
        /*@editable*/letter-spacing:normal;
        /*@editable*/text-align:left;
      }
      /*
      @tab Page
      @section Heading 3
      @tip Set the styling for all third-level headings in your emails.
      @style heading 3
      */
      h3{
        /*@editable*/color:#202020;
        /*@editable*/font-family:Helvetica;
        /*@editable*/font-size:20px;
        /*@editable*/font-style:normal;
        /*@editable*/font-weight:bold;
        /*@editable*/line-height:125%;
        /*@editable*/letter-spacing:normal;
        /*@editable*/text-align:left;
      }
      /*
      @tab Page
      @section Heading 4
      @tip Set the styling for all fourth-level headings in your emails. These should be the smallest of your headings.
      @style heading 4
      */
      h4{
        /*@editable*/color:#202020;
        /*@editable*/font-family:Helvetica;
        /*@editable*/font-size:18px;
        /*@editable*/font-style:normal;
        /*@editable*/font-weight:bold;
        /*@editable*/line-height:125%;
        /*@editable*/letter-spacing:normal;
        /*@editable*/text-align:left;
      }
      /*
      @tab Preheader
      @section Preheader Style
      @tip Set the background color and borders for your email's preheader area.
      */
      #templatePreheader{
        /*@editable*/background:#1b2e58;
        /*@editable*/border-top:0;
        /*@editable*/border-bottom:0;
        /*@editable*/padding-top:9px;
        /*@editable*/padding-bottom:9px;
      }
      /*
      @tab Preheader
      @section Preheader Text
      @tip Set the styling for your email's preheader text. Choose a size and color that is easy to read.
      */
      #templatePreheader .mcnTextContent,#templatePreheader .mcnTextContent p{
        /*@editable*/color:#656565;
        /*@editable*/font-family:Helvetica;
        /*@editable*/font-size:12px;
        /*@editable*/line-height:150%;
        /*@editable*/text-align:left;
      }
      /*
      @tab Preheader
      @section Preheader Link
      @tip Set the styling for your email's preheader links. Choose a color that helps them stand out from your text.
      */
      #templatePreheader .mcnTextContent a,#templatePreheader .mcnTextContent p a{
        /*@editable*/color:#656565;
        /*@editable*/font-weight:normal;
        /*@editable*/text-decoration:underline;
      }
      /*
      @tab Header
      @section Header Style
      @tip Set the background color and borders for your email's header area.
      */
      #templateHeader{
        /*@editable*/background-color:#FFFFFF;
        /*@editable*/border-top:0;
        /*@editable*/border-bottom:0;
        /*@editable*/padding-top:9px;
        /*@editable*/padding-bottom:0;
      }
      /*
      @tab Header
      @section Header Text
      @tip Set the styling for your email's header text. Choose a size and color that is easy to read.
      */
      #templateHeader .mcnTextContent,#templateHeader .mcnTextContent p{
        /*@editable*/color:#202020;
        /*@editable*/font-family:Helvetica;
        /*@editable*/font-size:16px;
        /*@editable*/line-height:150%;
        /*@editable*/text-align:left;
      }
      /*
      @tab Header
      @section Header Link
      @tip Set the styling for your email's header links. Choose a color that helps them stand out from your text.
      */
      #templateHeader .mcnTextContent a,#templateHeader .mcnTextContent p a{
        /*@editable*/color:#2BAADF;
        /*@editable*/font-weight:normal;
        /*@editable*/text-decoration:underline;
      }
      /*
      @tab Body
      @section Body Style
      @tip Set the background color and borders for your email's body area.
      */
      #templateBody{
        /*@editable*/background-color:#FFFFFF;
        /*@editable*/border-top:0;
        /*@editable*/border-bottom:0;
        /*@editable*/padding-top:9px;
        /*@editable*/padding-bottom:9px;
      }
      /*
      @tab Body
      @section Body Text
      @tip Set the styling for your email's body text. Choose a size and color that is easy to read.
      */
      #templateBody .mcnTextContent,#templateBody .mcnTextContent p{
        /*@editable*/color:#1e2945;
        /*@editable*/font-family:'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;
        /*@editable*/font-size:18px;
        /*@editable*/line-height:150%;
        /*@editable*/text-align:left;
      }
      /*
      @tab Body
      @section Body Link
      @tip Set the styling for your email's body links. Choose a color that helps them stand out from your text.
      */
      #templateBody .mcnTextContent a,#templateBody .mcnTextContent p a{
        /*@editable*/color:#2BAADF;
        /*@editable*/font-weight:normal;
        /*@editable*/text-decoration:underline;
      }
      /*
      @tab Footer
      @section Footer Style
      @tip Set the background color and borders for your email's footer area.
      */
      #templateFooter{
        /*@editable*/background-color:#1dcb8b;
        /*@editable*/border-top:0;
        /*@editable*/border-bottom:0;
        /*@editable*/padding-top:30px;
        /*@editable*/padding-bottom:30px;
      }
      /*
      @tab Footer
      @section Footer Text
      @tip Set the styling for your email's footer text. Choose a size and color that is easy to read.
      */
      #templateFooter .mcnTextContent,#templateFooter .mcnTextContent p{
        /*@editable*/color:#656565;
        /*@editable*/font-family:Helvetica;
        /*@editable*/font-size:12px;
        /*@editable*/line-height:150%;
        /*@editable*/text-align:center;
      }
      /*
      @tab Footer
      @section Footer Link
      @tip Set the styling for your email's footer links. Choose a color that helps them stand out from your text.
      */
      #templateFooter .mcnTextContent a,#templateFooter .mcnTextContent p a{
        /*@editable*/color:#1f2a44;
        /*@editable*/font-weight:normal;
        /*@editable*/text-decoration:none;
      }
      @media only screen and (min-width:768px){
        .templateContainer{
          width:600px !important;
        }
  
      }	@media only screen and (max-width: 480px){
        body,table,td,p,a,li,blockquote{
          -webkit-text-size-adjust:none !important;
        }
  
      }	@media only screen and (max-width: 480px){
        body{
          width:100% !important;
          min-width:100% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        #bodyCell{
          padding-top:10px !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcnImage{
          width:100% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcnCaptionTopContent,.mcnCaptionBottomContent,.mcnTextContentContainer,.mcnBoxedTextContentContainer,.mcnImageGroupContentContainer,.mcnCaptionLeftTextContentContainer,.mcnCaptionRightTextContentContainer,.mcnCaptionLeftImageContentContainer,.mcnCaptionRightImageContentContainer,.mcnImageCardLeftTextContentContainer,.mcnImageCardRightTextContentContainer{
          max-width:100% !important;
          width:100% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcnBoxedTextContentContainer{
          min-width:100% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcnImageGroupContent{
          padding:9px !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcnCaptionLeftContentOuter .mcnTextContent,.mcnCaptionRightContentOuter .mcnTextContent{
          padding-top:9px !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcnImageCardTopImageContent,.mcnCaptionBlockInner .mcnCaptionTopContent:last-child .mcnTextContent{
          padding-top:18px !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcnImageCardBottomImageContent{
          padding-bottom:9px !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcnImageGroupBlockInner{
          padding-top:0 !important;
          padding-bottom:0 !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcnImageGroupBlockOuter{
          padding-top:9px !important;
          padding-bottom:9px !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcnTextContent,.mcnBoxedTextContentColumn{
          padding-right:18px !important;
          padding-left:18px !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcnImageCardLeftImageContent,.mcnImageCardRightImageContent{
          padding-right:18px !important;
          padding-bottom:0 !important;
          padding-left:18px !important;
        }
  
      }	@media only screen and (max-width: 480px){
        .mcpreview-image-uploader{
          display:none !important;
          width:100% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        /*
        @tab Mobile Styles
        @section Heading 1
        @tip Make the first-level headings larger in size for better readability on small screens.
        */
        h1{
          /*@editable*/font-size:22px !important;
          /*@editable*/line-height:125% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        /*
        @tab Mobile Styles
        @section Heading 2
        @tip Make the second-level headings larger in size for better readability on small screens.
        */
        h2{
          /*@editable*/font-size:20px !important;
          /*@editable*/line-height:125% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        /*
        @tab Mobile Styles
        @section Heading 3
        @tip Make the third-level headings larger in size for better readability on small screens.
        */
        h3{
          /*@editable*/font-size:18px !important;
          /*@editable*/line-height:125% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        /*
        @tab Mobile Styles
        @section Heading 4
        @tip Make the fourth-level headings larger in size for better readability on small screens.
        */
        h4{
          /*@editable*/font-size:16px !important;
          /*@editable*/line-height:150% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        /*
        @tab Mobile Styles
        @section Boxed Text
        @tip Make the boxed text larger in size for better readability on small screens. We recommend a font size of at least 16px.
        */
        .mcnBoxedTextContentContainer .mcnTextContent,.mcnBoxedTextContentContainer .mcnTextContent p{
          /*@editable*/font-size:14px !important;
          /*@editable*/line-height:150% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        /*
        @tab Mobile Styles
        @section Preheader Visibility
        @tip Set the visibility of the email's preheader on small screens. You can hide it to save space.
        */
        #templatePreheader{
          /*@editable*/display:block !important;
        }
  
      }	@media only screen and (max-width: 480px){
        /*
        @tab Mobile Styles
        @section Preheader Text
        @tip Make the preheader text larger in size for better readability on small screens.
        */
        #templatePreheader .mcnTextContent,#templatePreheader .mcnTextContent p{
          /*@editable*/font-size:14px !important;
          /*@editable*/line-height:150% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        /*
        @tab Mobile Styles
        @section Header Text
        @tip Make the header text larger in size for better readability on small screens.
        */
        #templateHeader .mcnTextContent,#templateHeader .mcnTextContent p{
          /*@editable*/font-size:16px !important;
          /*@editable*/line-height:150% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        /*
        @tab Mobile Styles
        @section Body Text
        @tip Make the body text larger in size for better readability on small screens. We recommend a font size of at least 16px.
        */
        #templateBody .mcnTextContent,#templateBody .mcnTextContent p{
          /*@editable*/font-size:16px !important;
          /*@editable*/line-height:150% !important;
        }
  
      }	@media only screen and (max-width: 480px){
        /*
        @tab Mobile Styles
        @section Footer Text
        @tip Make the footer content text larger in size for better readability on small screens.
        */
        #templateFooter .mcnTextContent,#templateFooter .mcnTextContent p{
          /*@editable*/font-size:14px !important;
          /*@editable*/line-height:150% !important;
        }
  
      }
  </style>
</head>

<body>

  <center>
    <table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable">
      <tr>
        <td align="center" valign="top" id="bodyCell">
          <!-- BEGIN TEMPLATE // -->
          <table border="0" cellpadding="0" cellspacing="0" width="100%">
           
              <div >
                <tr style="background:#FFFFFF">
                <td align="center" valign="top" >
                  <!--[if gte mso 9]>
                      <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
                        <tr>
                          <td align="center" valign="top" width="600" style="width:600px;">
                          <![endif]-->
                  <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer">
                    <tr>
                      <td valign="top" class="preheaderContainer">
                        <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnImageBlock" style="min-width:100%;">
                          <tbody class="mcnImageBlockOuter">
                            <tr>
                              <td valign="top" style="padding:9px" class="mcnImageBlockInner">
                                <table align="left" width="100%" border="0" cellpadding="0" cellspacing="0" class="mcnImageContentContainer" style="min-width:100%;">
                                  <tbody>
                                    <tr>
                                      <td class="mcnImageContent" valign="top" style="text-align:center;">
                                        <a href="" style="cursor:default;"></a>
                                        <img align="center" alt="" src= {{orgLogo}} style="max-width:200px;height: auto;margin: 30px; padding-bottom: 0; display: inline !important; vertical-align: bottom; width: 250px;" >
                                      </a>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </table>
                  <!--[if gte mso 9]>
                          </td>
                        </tr>
                      </table>
                    <![endif]-->
                </td>
              </tr>
              </div>
            
            <tr>
              <td align="center" valign="top" id="templateHeader">
                <!--[if gte mso 9]>
                    <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
                      <tr>
                        <td align="center" valign="top" width="600" style="width:600px;">
                        <![endif]-->
                <table align="center" border="0" cellpadding="0" cellspacing="0" width="50%"  class="templateContainer">
                  <tr>
                    <td valign="top" class="headerContainer">
                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnImageBlock" style="min-width:100%;">
                        <tbody class="mcnImageBlockOuter">
                          <tr>
                            <td valign="top" style="padding:0px" class="mcnImageBlockInner">
                              <table align="left" width="100%" border="0" cellpadding="0" cellspacing="0" class="mcnImageContentContainer" style="min-width:100%;">
                                <tbody >
                                  <tr>
                                    <td class="mcnTextContent" valign="top" style= "color: rgb(72, 110, 207); text-align:center;font-size: 20px;">
                                    <!-- <td class="mcnImageContent" valign="top" style="padding-right: 0px; padding-left: 0px; padding-top: 0; padding-bottom: 0; text-align:center;"> -->
                                      <!-- <a href="https://spiritjs.io" title="" class="" target="_blank"> -->
                                        <!-- <img align="center" alt="" src="https://gallery.mailchimp.com/6d5e686af3a776f67a79d56e6/images/7922b4e7-8a8f-434c-8f44-e0f29619b4fd.png" width="438.5" style="max-width:877px; padding-bottom: 0; display: inline !important; vertical-align: bottom;" class="mcnImage"> -->
                                      </a>
                                       Welcome to {{companyName}} Onboarding Process
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width:100%;">
                        <tbody class="mcnTextBlockOuter">
                          <tr>
                            <td valign="top" class="mcnTextBlockInner">
                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width:100%;" class="mcnTextContentContainer">
                                <tbody>
                                  <tr>
                                    <td valign="top" class="mcnTextContent" style="padding: 9px 18px;color: #1E2945;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;text-align: left;">
                                      <p style="line-height: 150%;color: #1E2945;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;text-align: left;"><strong>Hi {{name}}, Welcome!</strong>
                                        <br>
                                        <br>We are thrilled to have you at {{companyName}}.
                                        <br>&nbsp;</p>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnCaptionBlock">
                        <tbody class="mcnCaptionBlockOuter">
                          <tr>
                            <td class="mcnCaptionBlockInner" valign="top" style="padding:9px;">
                              <table border="0" cellpadding="0" cellspacing="0" class="mcnCaptionRightContentOuter" width="100%">
                                <tbody>
                                  <tr>
                                    <td valign="top" class="mcnCaptionRightContentInner" style="padding:0 9px ;">
                                      <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnCaptionRightImageContentContainer">
                                        <tbody>
                                          <tr>
                                            <td class="mcnCaptionRightImageContent" valign="top">
                                              <img alt="" class = "a6S" src="https://s3.amazonaws.com/hrapp-email-images/img1.gif" width="264" style="max-width:340px;" class="mcnImage">
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                      <table class="mcnCaptionRightTextContentContainer" align="right" border="0" cellpadding="0" cellspacing="0" width="264">
                                        <tbody>
                                          <tr>
                                            <td valign="top" class="mcnTextContent" style="color: #1F2A44;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;">
                                              <p style="color: #1F2A44;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;"> We know you’re going to be a valuable asset to our company and can’t wait to see what you accomplish.</p>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnCaptionBlock">
                        <tbody class="mcnCaptionBlockOuter">
                          <tr>
                            <td class="mcnCaptionBlockInner" valign="top" style="padding:9px;">
                              <table border="0" cellpadding="0" cellspacing="0" class="mcnCaptionLeftContentOuter" width="100%">
                                <tbody>
                                  <tr>
                                    <td valign="top" class="mcnCaptionLeftContentInner" style="padding:0 9px ;">
                                      <table align="right" border="0" cellpadding="0" cellspacing="0" class="mcnCaptionLeftImageContentContainer">
                                        <tbody>
                                          <tr>
                                            <td class="mcnCaptionLeftImageContent" valign="top">
                                              <img alt="" class = "a6S" src="https://s3.amazonaws.com/hrapp-email-images/img2.gif" width="264" style="max-width:340px;" class="mcnImage">
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                      <table class="mcnCaptionLeftTextContentContainer" align="left" border="0" cellpadding="0" cellspacing="0" width="264">
                                        <tbody>
                                          <tr>
                                            <td valign="top" class="mcnTextContent" style="color: #1F2A44;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;">
                                              <p style="color: #1F2A44;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;"><span class="s1">Fill your details with extraordinary user interface. </span>
                                              </p>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnCaptionBlock">
                        <tbody class="mcnCaptionBlockOuter">
                          <tr>
                            <td class="mcnCaptionBlockInner" valign="top" style="padding:9px;">
                              <table border="0" cellpadding="0" cellspacing="0" class="mcnCaptionRightContentOuter" width="100%">
                                <tbody>
                                  <tr>
                                    <td valign="top" class="mcnCaptionRightContentInner" style="padding:0 9px ;">
                                      <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnCaptionRightImageContentContainer">
                                        <tbody>
                                          <tr>
                                            <td class="mcnCaptionRightImageContent" valign="top">
                                              <img alt="" class = "a6S" src="https://s3.amazonaws.com/hrapp-email-images/img3.gif" width="264" style="max-width:340px;" class="mcnImage">
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                      <table class="mcnCaptionRightTextContentContainer" align="right" border="0" cellpadding="0" cellspacing="0" width="264">
                                        <tbody>
                                          <tr>
                                            <td valign="top" class="mcnTextContent" style="color: #1F2A44;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;">
                                              <p style="color: #1F2A44;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;"><span class="s1">The details you have entered here are used after your confirmation</span>
                                              </p>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnCaptionBlock">
                        <tbody class="mcnCaptionBlockOuter">
                          <tr>
                            <td class="mcnCaptionBlockInner" valign="top" style="padding:9px;">
                              <table border="0" cellpadding="0" cellspacing="0" class="mcnCaptionLeftContentOuter" width="100%">
                                <tbody>
                                  <tr>
                                    <td valign="top" class="mcnCaptionLeftContentInner" style="padding:0 9px ;">
                                      <table align="right" border="0" cellpadding="0" cellspacing="0" class="mcnCaptionLeftImageContentContainer">
                                        <tbody>
                                          <tr>
                                            <td class="mcnCaptionLeftImageContent" valign="top">
                                              <img alt="" class = "a6S" src="https://s3.amazonaws.com/hrapp-email-images/img4.gif" width="264" style="max-width:340px;" class="mcnImage">
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                      <table class="mcnCaptionLeftTextContentContainer" align="left" border="0" cellpadding="0" cellspacing="0" width="264">
                                        <tbody>
                                          <tr>
                                            <td valign="top" class="mcnTextContent" style="color: #1F2A44;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;">
                                              <p style="color: #1F2A44;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;"><span class="s1">Use the pin <font size="4"><b>{{pin}}</b></font>   for authentication</span>
                                              </p>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <!-- <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnCaptionBlock">
                        <tbody class="mcnCaptionBlockOuter">
                          <tr>
                            <td class="mcnCaptionBlockInner" valign="top" style="padding:9px;">
                              <table border="0" cellpadding="0" cellspacing="0" class="mcnCaptionRightContentOuter" width="100%">
                                <tbody>
                                  <tr>
                                    <td valign="top" class="mcnCaptionRightContentInner" style="padding:0 9px ;">
                                      <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnCaptionRightImageContentContainer">
                                        <tbody>
                                          <tr>
                                            <td class="mcnCaptionRightImageContent" valign="top">
                                              <img alt="" src="https://gallery.mailchimp.com/6d5e686af3a776f67a79d56e6/images/75b2c7e5-b914-4bfc-abd1-18e3a3c159a8.gif" width="264" style="max-width:340px;" class="mcnImage">
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                      <table class="mcnCaptionRightTextContentContainer" align="right" border="0" cellpadding="0" cellspacing="0" width="264">
                                        <tbody>
                                          <tr>
                                            <td valign="top" class="mcnTextContent" style="color: #1F2A44;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;">
                                              <p style="color: #1F2A44;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;"><span class="s1">Use the pin {{pin}} for authentication
<br></span>
                                              </p>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table> -->
                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnCaptionBlock">
                        <tbody class="mcnCaptionBlockOuter">
                          <tr>
                            <td class="mcnCaptionBlockInner" valign="top" style="padding:9px;">
                              <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnCaptionTopContent" width="false">
                                <tbody>
                                  <tr>
                                    <td class="mcnTextContent" valign="top" style="padding: 0px 9px;color: #1F2A44;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 150%;" width="564">
                                      <div style="text-align: center;">
                                        <br> <span style="line-height:20.8px">
                                           <a href= {{url}} >
                                            <script> 
                                                var Window; 
                                                function windowOpen() { 
                                                    Window = window.open("https://www.google.com","_blank"); 
                                                } 
                                            </script> 
                                         <button  
                                         id="button-backround"
                                         style="border: none;
                                          color: white;
                                          padding: 14px 28px;
                                          font-size: 16px;
                                          cursor: pointer;
                                          border-radius: 30px;width: 190px;
                                          background-color: #00263E",onclick="windowOpen()"> Fill Details</button>
                                           </a>  
                                          </span>
                                      </div>
                                      <br> <span style="line-height:20.8px">Once again, we are excited to have you on the team and hope you’re looking forward to your first day as much as we are.</span>

                                    </td>
                                  </tr>
                                  <tr>
                                    <td class="mcnCaptionTopImageContent" align="center" valign="top" style="padding:9px 9px 0 9px;">
                                      <img alt="" class = "a6S" src="https://s3.amazonaws.com/hrapp-email-images/img5.gif" width="340" style="max-width:340px;" class="mcnImage">
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width:100%;">
                        <tbody class="mcnTextBlockOuter">
                          <tr>
                            <td valign="top" class="mcnTextBlockInner">
                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width:100%;" class="mcnTextContentContainer">
                                <tbody>
                                  <tr>
                                    <td valign="top" class="mcnTextContent" style="padding: 9px 18px;color: #1E2945;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 18px;font-style: normal;font-weight: normal;line-height: 10px;">We hope to see you soon.
                                      <br>
                                      <br>
                                      <!-- <a href="http://inlet.nl" target="_blank">Patrick Brouwer</a> -->
                                      <!-- <br> -->
                                      <!-- <br> -->
                                      <br>
                                      <br>&nbsp;</td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </table>
                <!--[if gte mso 9]>
                        </td>
                      </tr>
                    </table>
                  <![endif]-->
              </td>
            </tr>
            <tr>
              <td align="center" valign="top" id="templateBody">
                <!--[if gte mso 9]>
                    <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
                      <tr>
                        <td align="center" valign="top" width="600" style="width:600px;">
                        <![endif]-->
                <table align="center" border="0" cellpadding="0" cellspacing="0" width="50%" class="templateContainer">
                  <tr>
                    <td valign="top" class="bodyContainer"></td>
                  </tr>
                </table>
                <!--[if gte mso 9]>
                        </td>
                      </tr>
                    </table>
                  <![endif]-->
              </td>
            </tr>
            <tr style="background:#FFFFFF">
              <td align="center" valign="top" >
                <!--[if gte mso 9]>
                    <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
                      <tr>
                        <td align="center" valign="top" width="600" style="width:600px;">
                        <![endif]-->
                <table align="center" border="0" cellpadding="0" cellspacing="0" width="50%" class="templateContainer">
                  <tr>
                    <td valign="top" class="footerContainer">
                      <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width:100%;">
                        <tbody class="mcnTextBlockOuter">
                          <tr>
                            <td valign="top" class="mcnTextBlockInner">
                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width:100%;" class="mcnTextContentContainer">
                                <tbody>
                                  <tr style="text-align: center;">
                                    <td valign="top" class="mcnTextContent" style="color: #222222;font-family: 'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace;font-size: 16px;padding-bottom: 100px;"> 
                                      <em>Copyright © <script>document.write( new Date().getFullYear() );</script> HrApp, All rights reserved.</em>
                                      <br>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </table>
                <!--[if gte mso 9]>
                        </td>
                      </tr>
                    </table>
                  <![endif]-->
              </td>
            </tr>
          </table>
          <!-- // END TEMPLATE -->
        </td>
      </tr>
    </table>
  </center>
</body>
<script>
  function applyStyles(elementId, bgColor, textColor) {
      var element = document.getElementById(elementId);
      if (element) {
        if(bgColor){
          element.style.backgroundColor = bgColor;
        }
        
        if(textColor){
          element.style.color = textColor;
        }
      } else {
          console.error('Element with ID ' + elementId + ' not found.');
      }
  }
  // Define variables for styles
  var backgroundColor = bgColors;
  var fontColor = fontColors;
  // Apply styles to the element
  applyStyles('button-backround', backgroundColor, fontColor);
</script>
</html>