const generateUrl = require('./resolvers/generateUrl');
const regeneratePin = require('./resolvers/regeneratePin');

const pinAuth = require('./resolvers/pinAuth');
const checkUrlStatus = require('./resolvers/checkUrlStatus');
const sendUrlToCandidate = require('./resolvers/sendUrlToCandidate');
const listLanguage = require('./resolvers/listLanguage');
const listMartialStatus = require('./resolvers/listMartialStatus');
const listWorkSchedule = require('./resolvers/listWorkSchedule');
const listDesignation = require('./resolvers/listDesignation');
const listDepartment = require('./resolvers/listDepartment');
const listCountry = require('./resolvers/listCountry');
const listLocation = require('./resolvers/listLocation');
const listManager = require('./resolvers/listManager');
const listMartialStatusRelation = require('./resolvers/listMartialStatusRelation');
const listValidityType = require('./resolvers/listValidityType');
const listCourseDetails = require('./resolvers/listCourseDetails');
const listGeneratedUrl = require('./resolvers/listGeneratedUrl');
const listAccountType = require('./resolvers/listAccountType');
const listEmployeeType = require('./resolvers/listEmployeeType');
const listDocumentCategory = require('./resolvers/listDocumentCategory');
const listDocumentType = require('./resolvers/listDocumentType');
const listDocumentSubType = require('./resolvers/listDocumentSubType');
const listBankName = require('./resolvers/listBankName');
const updatePersonalInfo = require('./resolvers/updatePersonalInfo');
const updatePhotoPath = require('./resolvers/updatePhotoPath');
const getPersonalInfo = require('./resolvers/getPersonalInfo');
const addCertification = require('./resolvers/addCertification');
const getCertification = require('./resolvers/getCertification');
const updateCertification = require('./resolvers/updateCertification');
const deleteCertification = require('./resolvers/deleteCertification');
const addDependent = require('./resolvers/addDependent');
const getDependent = require('./resolvers/getDependent');
const updateDependent = require('./resolvers/updateDependent');
const deleteDependent = require('./resolvers/deleteDependent');
const addEducation = require('./resolvers/addEducation');
const getEducation = require('./resolvers/getEducation');
const updateEducation = require('./resolvers/updateEducation');
const deleteEducation = require('./resolvers/deleteEducation');
const addExperience = require('./resolvers/addExperience');
const getExperience = require('./resolvers/getExperience');
const updateExperience = require('./resolvers/updateExperience');
const deleteExperience = require('./resolvers/deleteExperience');
const addTraining = require('./resolvers/addTraining');
const getTraining = require('./resolvers/getTraining');
const updateTraining = require('./resolvers/updateTraining');
const deleteTraining = require('./resolvers/deleteTraining');
const addBankDetails = require('./resolvers/addBankDetails');
const updateBankDetails = require('./resolvers/updateBankDetails');
const getBankDetails = require('./resolvers/getBankDetails');
const fileUpload = require('./resolvers/fileUpload');
const fileRetrieve = require('./resolvers/fileRetrieve');
const fileDelete = require('./resolvers/fileDelete');
const fileReplace = require('./resolvers/fileReplace');
const statusUpdate = require('./resolvers/statusUpdate');

const fileUploadHr = require('./resolvers/fileUploadHr');
const fileRetrieveHr = require('./resolvers/fileRetrieveHr');
const fileDeleteHr = require('./resolvers/fileDeleteHr');
const fileReplaceHr = require('./resolvers/fileReplaceHr');

const getAllDocuments = require('./resolvers/getAllDocuments');
const addDocument = require('./resolvers/addDocument');
const updateDocument = require('./resolvers/updateDocument');
const deleteDocument = require('./resolvers/deleteDocument');
const addDocumentSubType = require('./resolvers/addDocumentSubType');

const migrateDocuments = require('./resolvers/migrateDocuments');

const updateEmployeePreviousExperience = require('./resolvers/updateEmployeePreviousExperience');
const retrieveNationalityList = require('./resolvers/retrieveNationalityList');
const retrieveReligionList = require('./resolvers/retrieveReligionList');
const retrieveListEduInstitutionAndSpecialization = require('./roresolvers/retrieveListEduInstitutionAndSpecialization');
const retrieveGenderList = require('./resolvers/retrieveGenderList');
const getCompanyPartner = require('./resolvers/getCompanyPartner');

const addCandidateRelation = require('./resolvers/addCandidateRelation');
const deleteCandidateRelation = require('./resolvers/deleteCandidateRelation');

const resolvers = {
    Query: Object.assign({}, pinAuth.resolvers.Query,
      checkUrlStatus.resolvers.Query, 
      listLanguage.resolvers.Query, 
      listMartialStatus.resolvers.Query,
      listWorkSchedule.resolvers.Query,
      listDesignation.resolvers.Query,
      listDepartment.resolvers.Query,
      listCountry.resolvers.Query,
      listLocation.resolvers.Query,
      listManager.resolvers.Query,
      listMartialStatusRelation.resolvers.Query,
      listValidityType.resolvers.Query,
      listGeneratedUrl.resolvers.Query,
      listCourseDetails.resolvers.Query,
      listAccountType.resolvers.Query,
      listEmployeeType.resolvers.Query,
      listDocumentCategory.resolvers.Query,
      listDocumentType.resolvers.Query,
      listDocumentSubType.resolvers.Query,
      listBankName.resolvers.Query,
      getExperience.resolvers.Query,
      getCertification.resolvers.Query,
      getDependent.resolvers.Query,
      getEducation.resolvers.Query,
      getTraining.resolvers.Query,
      getPersonalInfo.resolvers.Query,
      getBankDetails.resolvers.Query,
      sendUrlToCandidate.resolvers.Query,
      fileUpload.resolvers.Query,
      fileRetrieve.resolvers.Query,
      fileDelete.resolvers.Query,
      fileReplace.resolvers.Query,
      fileUploadHr.resolvers.Query,
      fileRetrieveHr.resolvers.Query,
      fileDeleteHr.resolvers.Query,
      fileReplaceHr.resolvers.Query,
      getAllDocuments.resolvers.Query,
      retrieveNationalityList.resolvers.Query,
      retrieveReligionList.resolvers.Query,
      retrieveGenderList.resolvers.Query,
      retrieveListEduInstitutionAndSpecialization,
      getCompanyPartner.resolvers.Query
    ),
    Mutation: Object.assign({},  generateUrl.resolvers.Mutation,
      regeneratePin.resolvers.Mutation,
      updatePersonalInfo.resolvers.Mutation,
      updatePhotoPath.resolvers.Mutation,
      addExperience.resolvers.Mutation,
      updateExperience.resolvers.Mutation,
      deleteExperience.resolvers.Mutation,
      addDependent.resolvers.Mutation,
      updateDependent.resolvers.Mutation,
      deleteDependent.resolvers.Mutation,
      addCertification.resolvers.Mutation,
      updateCertification.resolvers.Mutation,
      deleteCertification.resolvers.Mutation,
      addTraining.resolvers.Mutation,
      updateTraining.resolvers.Mutation,
      deleteTraining.resolvers.Mutation,
      addEducation.resolvers.Mutation,
      updateEducation.resolvers.Mutation,
      deleteEducation.resolvers.Mutation,
      addBankDetails.resolvers.Mutation,
      updateBankDetails.resolvers.Mutation,
      statusUpdate.resolvers.Mutation,
      addDocument.resolvers.Mutation,
      updateDocument.resolvers.Mutation,
      deleteDocument.resolvers.Mutation,
      addDocumentSubType.resolvers.Mutation,
      migrateDocuments.resolvers.Mutation,
      updateEmployeePreviousExperience.resolvers.Mutation,
      addCandidateRelation.resolvers.Mutation,
      deleteCandidateRelation.resolvers.Mutation
    )
  }

exports.resolvers = resolvers;