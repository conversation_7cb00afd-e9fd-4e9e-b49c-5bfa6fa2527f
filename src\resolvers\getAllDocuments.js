const resolvers = {
    Query : {
        getAllDocuments : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig.select('candidate_document_category.Document_Id',
                            'candidate_document_category.Candidate_Id',
                            'candidate_document_category.Document_Name',
                            'document_category.Category_Id',
                            'document_category.Category_Fields as Category_Name',
                            'document_sub_type.Document_Type_Id',
                            'document_sub_type.Is_Default',
                            'document_type.Document_Type',
                            'document_sub_type.Document_Sub_Type_Id',
                            'document_sub_type.Document_Sub_Type',
                            'candidate_documents.File_Name',
                            'document_sub_type.Mandatory','document_sub_type.Instruction'
                        )
                        .from('candidate_document_category')
                        .leftJoin('document_sub_type','candidate_document_category.Sub_Document_Type_Id','document_sub_type.Document_Sub_Type_Id')
                        .leftJoin('document_type','document_sub_type.Document_Type_Id','document_type.Document_Type_Id')
                        .leftJoin('candidate_documents','candidate_document_category.Document_Id','candidate_documents.Document_Id')
                        .leftJoin('document_category','document_type.Category_Id','document_category.Category_Id')
                        .where('candidate_document_category.Candidate_Id',url[0].Candidate_Id)
                        .then((documents)=>{
                            if(!documents[0]){
                                return {
                                    documents : null
                                }
                            } else {
                                return {
                                    documents : documents
                                }
                            }
                        })
                    }
                }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                return result;
            }).catch(function(err){
                console.log('Error in getAllDocuments',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;