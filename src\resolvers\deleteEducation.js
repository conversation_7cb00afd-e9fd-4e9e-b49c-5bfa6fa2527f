const resolvers = {
    Mutation : {
        deleteEducation : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');


            var Candidate_Id;
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        Candidate_Id = url[0].Candidate_Id;
                        return knexconfig('candidate_education')
                        .where('Education_Id',args.Education_Id)
                        .andWhere('Candidate_Id',Candidate_Id)
                        .then((education)=>{
                        if(!education[0]){
                            throw new Error("Education Detail not found");
                        } else {
                                return knexconfig('candidate_education')
                                .where('Education_Id',args.Education_Id)
                                .del()
                                .transacting(trc)
                                .then(()=>{
                                    return knexconfig('candidate_education_documents')
                                    .where('Education_Id',args.Education_Id)
                                    .then((document_exist)=>{
                                        if(!document_exist[0]){
                                            return {
                                                message:"Education details deleted"
                                            }
                                        } else {
                                            return knexconfig('candidate_education_documents')
                                            .where('Education_Id',args.Education_Id)
                                            .del()
                                            .transacting(trc)
                                            .then(()=>{
                                                return {
                                                    message:"Education details deleted"
                                                }
                                            })
                                        }
                                    })
                                })
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return knexconfig.select('candidate_education.*',
                    'candidate_education_documents.File_Name',
                    'candidate_education_documents.Document_Name',
                    'candidate_education_documents.Sub_Type_Id',
                    'course_details.Course_Name as Education_Type_Name'
                )
                .from('candidate_education')
                .leftJoin('course_details','candidate_education.Education_Type','course_details.Course_Id')
                .leftJoin('candidate_education_documents','candidate_education.Education_Id','candidate_education_documents.Education_Id')
                .where('candidate_education.Candidate_Id',Candidate_Id)
                .orderBy('candidate_education.Education_Id')
                .then((education)=>{
                    if(!education[0]){
                        return knexconfig('candidate_personal_info')
                        .where('Candidate_Id',Candidate_Id)
                        .update({
                            Is_Illiterate: 0
                        })
                        .then(()=>{
                            return {
                                education: null
                            }
                        })
                    } else {
                        return {
                            education: education
                        }
                    }
                })
            }).catch(function(err){
                console.log('Error in deleteEducation',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Education Detail not found"){
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;