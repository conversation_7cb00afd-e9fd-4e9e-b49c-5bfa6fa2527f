//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
let moment = require('moment');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');
const { vendorDetailsValidation } = require('../common/vendorDetailsValidation');

module.exports.saveVendorBasicDetails = async (parent, args, context, info) => {
    console.log('Inside saveVendorBasicDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        let checkRights
        organizationDbConnection = knex(context.connection.OrganizationDb);
        if (args.isUpdate) {
            checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        }
        //Check if vendor id exists
        let onboardingVendorId = (args.vendorId) ? args.vendorId : 0;
        if ((!args.isUpdate) || (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1)) {
            validationError = await vendorDetailsValidation(args, args.isUpdate);
            if (Object.keys(validationError).length == 0) {
                let onboardingVendorData = {}
                if (!args.isUpdate) {
                    onboardingVendorData = {
                        Vendor_Type: args.vendorType,
                        Service_Provider_Name: args.vendorName,
                        Business_Number: args.businessNumber,
                        Entity_Trust: args.entityTrust,
                        Trustee_Name: args.trusteeName,
                        GST_Registered: args.gstRegistered,
                        GST_Number: args.gstNumber,
                        Service_Offered: args.serviceOffered,
                        Type_Of_Industry: args.typeOfIndustry,
                        Street: args.street,
                        City: args.city,
                        State_Region: args.stateRegion,
                        Zip_Code: args.zipCode,
                        Country: args.country,
                        Postal_Street: args.postalStreet,
                        Postal_City: args.postalCity,
                        Postal_State_Region: args.postalStateRegion,
                        Postal_Zip_Code: args.postalZipCode,
                        Postal_Country: args.postalCountry,
                        Telephone: args.telephone,
                        Contact_Person_Phone_Number: args.mobileNo,
                        Contact_Person_Phone_Number_Country_Code: args.mobileNoCountryCode,
                        Email_Id: args.vendorEmail,
                        Trustee_Authorization_Documents: args.trusteeAuthorizationDocuments,
                        Contact_Person_Name: args.contactNameOfVendor,
                        Section1_Progress: 1,
                    }
                } else {
                    onboardingVendorData = {
                        Vendor_Type: args.vendorType,
                        Service_Provider_Name: args.vendorName,
                        Entity_Trust: args.entityTrust,
                        Trustee_Name: args.trusteeName,
                        GST_Registered: args.gstRegistered,
                        GST_Number: args.gstNumber,
                        Service_Offered: args.serviceOffered,
                        Type_Of_Industry: args.typeOfIndustry,
                        Service_Priority: args.servicePriority ? args.servicePriority : 'Critical',
                        Street: args.street,
                        City: args.city,
                        State_Region: args.stateRegion,
                        Zip_Code: args.zipCode,
                        Country: args.country,
                        Postal_Street: args.postalStreet,
                        Postal_City: args.postalCity,
                        Postal_State_Region: args.postalStateRegion,
                        Postal_Zip_Code: args.postalZipCode,
                        Postal_Country: args.postalCountry,
                        Telephone: args.telephone,
                        Contact_Person_Phone_Number: args.mobileNo,
                        Email_Id: args.vendorEmail,
                        Trustee_Authorization_Documents: args.trusteeAuthorizationDocuments,
                        Contact_Person_Name: args.contactNameOfVendor,
                        Performance_Ratings: args.performanceRatings,
                        Status_Level: args.statusLevel,
                        Vendor_Status: args.vendorStatus,
                        Manager_Id: args.managerId ? args.managerId : null,
                        Department_Id: args.departmentId ? args.departmentId : null,
                        Updated_On: moment.utc().format("YYYY-MM-DD HH:mm:ss"),
                        Updated_By: loginEmployeeId
                    }
                }
                if (onboardingVendorId > 0) {
                    return (
                        organizationDbConnection(ehrTables.serviceProvider)
                            .update(onboardingVendorData)
                            .where('Service_Provider_Id', onboardingVendorId)
                            .then(async (updateResult) => {
                                if (updateResult) {
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    return { errorCode: "", message: "Vendor basic details have been updated successfully." };
                                }
                                else {
                                    throw 'VO0105';
                                }

                            }).catch((catchError) => {
                                console.log('Error in saveVendorBasicDetails .catch() block', catchError);
                                errResult = commonLib.func.getError(catchError, 'VO0116');
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                //Return error response
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                    )

                } else {
                    throw 'VO0103';
                }
            }
            else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to update the vendor');
            throw '_DB0111';
        }

    } catch (mainCatchError) {
        console.log('Error in saveVendorBasicDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in saveVendorBasicDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(mainCatchError, 'VO0009');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}