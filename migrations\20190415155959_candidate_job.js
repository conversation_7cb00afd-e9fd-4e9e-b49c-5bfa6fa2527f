
exports.up = function(knex, Promise) {
    return knex.schema.createTable("candidate_job",function(table){
        table.integer('Candidate_Id',11).primary();
        table.integer('Designation_Id',11);
        table.integer('Department_Id',11);
        table.integer('Location_Id',11);
        table.date('Date_Of_Join');
        table.string('Job_Code',30);
        table.string('Emp_Email',50);
        table.integer('Manager_Id',11);
        table.integer('Emp_Profession',11);
        table.boolean('Confirmed');
        table.string('Emp_Status',20);
        table.date('Emp_InActive_Date');
        table.date('Confirmation_Date');
        table.date('Probation_Date');
        table.integer('EmpType_Id',11);
        table.boolean('Commission_Employee');
        table.integer('Work_Schedule',11);
        table.string('External_EmpId',15);
        table.string('User_Defined_EmpId',19);
        table.string('Pf_PolicyNo',30);
        table.boolean('TDS_Exemption');
        table.string('Reason_Id',11);
  })
};

exports.down = function(knex, Promise) {
    return knex.schema.dropTable('candidate_job');
};
