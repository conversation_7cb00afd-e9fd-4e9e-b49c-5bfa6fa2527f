const resolvers = {
    Mutation: {
        addCertification: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');
            var dateFormat = require('dateformat');

            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_url')
                    .where('Url_Hash', args.Url_Hash)
                    .then((url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } else {
                            Candidate_Id = url[0].Candidate_Id;
                            if (!args.Certification_Name) {
                                throw new Error("Certification Name should not be empty");
                            } else if (args.Certification_Name.length > 100) {
                                throw new Error("Certification Name should be less than or equal to 100 characters");
                            } else if (!args.Received_Date) {
                                throw new Error("Received Date should not be empty");
                            } else if (!args.Certificate_Received_From) {
                                throw new Error("Certificate Received From should not be empty");
                            } else if (args.Certificate_Received_From.length > 100) {
                                throw new Error("Certificate Received From should be less than or equal to 100 characters");
                            }
                            try {
                                var Received_Date = (!args.Received_Date || isNaN(Date.parse(args.Received_Date))) ? (null) : ((isNaN(Number(args.Received_Date))) ? dateFormat(new Date((args.Received_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Received_Date)), "yyyy-mm-dd"));
                            }
                            catch (err) {
                                throw new Error("Invalid Date Format")
                            }
                            return knexconfig('candidate_certifications')
                                .insert({
                                    Candidate_Id: Candidate_Id,
                                    Certification_Name: args.Certification_Name,
                                    Received_Date: Received_Date,
                                    Ranking: args.Ranking ? args.Ranking : null,
                                    Certificate_Received_From: args.Certificate_Received_From,
                                })
                                .transacting(trc)
                                .then((dataId) => {
                                    if (!args.File_Name) {
                                        return {
                                            message: "Certificate Added"
                                        }
                                    } else {
                                        return knexconfig('candidate_certifications_documents')
                                            .insert({
                                                Certification_Id: dataId,
                                                File_Name: args.File_Name,
                                                Document_Name: args.Document_Name,
                                                Sub_Type_Id: args.Sub_Type_Id
                                            })
                                            .transacting(trc)
                                            .then((documentId) => {
                                                return {
                                                    message: "Certificate Added"
                                                }
                                            })
                                    }
                                })
                        }
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                return knexconfig.select('candidate_certifications.*',
                    'candidate_certifications_documents.File_Name',
                    'candidate_certifications_documents.Document_Name',
                    'candidate_certifications_documents.Sub_Type_Id')
                    .from('candidate_certifications')
                    .leftJoin('candidate_certifications_documents', 'candidate_certifications.Certification_Id', 'candidate_certifications_documents.Certification_Id')
                    .where('candidate_certifications.Candidate_Id', Candidate_Id)
                    .then((certificates) => {
                        if (!certificates[0]) {
                            return {
                                certificates: null
                            }
                        } else {
                            var newCertificates = certificates.map((arr) => {
                                arr['Received_Date'] = dateFormat(new Date(arr['Received_Date']), "yyyy-mm-dd");
                                return arr;
                            });
                            return {
                                certificates: newCertificates
                            }
                        }
                    })
            }).catch(function (err) {
                console.log('Error in addCertification', err);
                if (err.message == "REO0101") {
                    console.log('URL not found');
                    throw new ApolloError("URL not found", "REO0101")
                } else if (err.message == "Certification Name should not be empty") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Certification Name should be less than or equal to 100 characters") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Received Date should not be empty") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Certificate Received From should not be empty") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Certificate Received From should be less than or equal to 100 characters") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Invalid Date Format") {
                    throw new UserInputError(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;