//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');
let moment = require('moment');
const { updateVendorStatusLevelValidation } = require('../common/inputValidation')

//function to update vendor status
module.exports.updateVendorStatusLevel = async (parent, args, context, info) => {
    console.log('Inside updateVendorStatusLevel function');
    let organizationDbConnection;
    let validationError = {};
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        validationError = await updateVendorStatusLevelValidation(args.vendorData);
        if (Object.keys(validationError).length === 0) {
            if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
                //Get the vendor ids from the array of object args.vendorData
                let updateData = args.vendorData
                for (let i = 0; i < updateData.length; i++) {
                        await organizationDbConnection(ehrTables.serviceProvider)
                        .update({
                            Status_Level: updateData[i].statusLevel,
                            Updated_By: loginEmployeeId,
                            Updated_On: moment.utc().format("YYYY-MM-DD HH:mm:ss"),
                        })
                        .where("Service_Provider_Id", updateData[i].vendorId)
                        .then(()=>{
                            console.log(`Vendor Status level has been updated to ${updateData[i].statusLevel} for the vendor id ${updateData[i].vendorId}`);
                        })
                        .catch((err)=>{
                            console.log('Error in updateVendorStatusLevel .catch() block', err);
                            let errResult = commonLib.func.getError(err, 'VO0133');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                    }
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Vendor staus level has been updated successfully"};

            } else {
                console.log('No rights to update the vendor');
                throw '_DB0111';
            }
        } else {
            throw 'IVE0000';
        }
    } catch (mainCatchError) {
        console.log('Error in updateVendorStatusLevel function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in updateVendorStatusLevel function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(mainCatchError, 'VO0019');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}