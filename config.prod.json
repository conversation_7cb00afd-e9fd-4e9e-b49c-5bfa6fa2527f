{"securityGroupIds": ["sg-0d3854ad69d6e7e09", "sg-0a9a621d864c23783"], "subnetIds": ["subnet-075680669427eff9d", "subnet-0e2510550b4a177a5"], "KmsKeyId": "arn:aws:kms:ap-south-1:484056187456:key/f006b620-e257-40c6-ba7d-709224d11e71", "role": "arn:aws:iam::484056187456:role/LambdaMicroservice", "secretName": "prod/hrapp/pgaccess", "imageBucket": "s3.images.hrapp.co", "documentBucket": "s3.taxdocs.hrapp.co", "baseUrlUI": "https://onboard.hrapp.co?companyName=", "emailFrom": "<EMAIL>", "emailReplyTo": "<EMAIL>", "sesRegion": "us-west-2", "logoBucket": "s3.logos.hrapp.co", "domainName": "hrapp", "fullDomainName": ".hrapp.co", "customDomainName": "onboardapi.hrapp.co", "commonAPIDomainName": "api.hrapp.co", "dbPrefix": "hrapp_", "documentsBucket": "s3.taxdocs.hrapp.co", "baseAUUrlUI": "https://onboarding.hrapp.co?companyName=", "vendorBucketName": "supplier.hrapp.co", "authorizerARN": "arn:aws:lambda:ap-south-1:484056187456:function:ATS-prod-firebaseauthorizer", "resendInviteVendorStepFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prodResendInviteVendorStepFunction", "resendInviteCandidateStepFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prodResendInviteCandidateStepFunction", "asyncSyntrumAPIStepFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-asyncSyntrumAPIFunction", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:484056187456:function:HRAPPOnboard-prod", "snsRegion": "ap-southeast-1", "snsEndpoint": "sns.ap-southeast-1.amazonaws.com"}