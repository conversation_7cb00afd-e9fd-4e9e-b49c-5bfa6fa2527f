// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const knex = require('knex')

const { ehrTables } = require('../common/tableAlias')
const { getDataFromCandidateUrlAccordingToUrlHash } = require('../common/commonFunctions')

module.exports.retrieveAccreditationDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError = {};
    try {
        console.log("Inside retrieveAccreditationDetails() function.")
        let urlHash = args.urlHash;
        if (!urlHash) {
            validationError['IVE0232'] = commonLib.func.getError('', 'IVE0232').message;
        }
        if (Object.keys(validationError).length === 0) {
            // get the organization database connection
            organizationDbConnection = knex(context.connection.OrganizationDb);
            let candidateUrlData = await getDataFromCandidateUrlAccordingToUrlHash(organizationDbConnection, urlHash);
            if (!candidateUrlData) {
                throw ('EO0101');
            }
            if (candidateUrlData && candidateUrlData.length > 0) {
                let candidateId = candidateUrlData[0].Candidate_Id;
                return (
                    organizationDbConnection(ehrTables.candidateAccreditationDetails)
                        .select('CAD.Accreditation_Detail_Id as accreditationDetailId', 'CAD.Accreditation_Category_And_Type_Id as accreditationCategoryAndTypeId', 'CAD.File_Name as fileName', 'CAD.Received_Date as receivedDate', 'CAD.Expiry_Date as expiryDate', 'CAD.Identifier as identifier',
                            'CAD.Exam_Rating as examRating', 'CAD.Exam_Date_Year as examDateYear', 'CAD.Exam_Date_Month as examDateMonth',
                            'ACAT.Accreditation_Category as accreditationCategory', 'ACAT.Accreditation_Type as accreditationType', 'ACAT.Mandatory', 'ACAT.Instruction',
                            "CD.Dependent_Id as dependentId", "CD.Dependent_First_Name as dependentFirstName", "CD.Dependent_Last_Name as dependentLastName",
                            "CD.Gender as gender", "CD.Relationship as relationship", "CD.Dependent_DOB as dependentDob"
                        )
                        .from(ehrTables.candidateAccreditationDetails + ' as CAD')
                        .leftJoin(ehrTables.accreditationCategoryAndType + ' as ACAT', 'ACAT.Accreditation_Category_And_Type_Id', 'CAD.Accreditation_Category_And_Type_Id')
                        .leftJoin(ehrTables.candidateDependent + " as CD", "CD.Dependent_Id", "CAD.Dependent_Id")
                        .where('CAD.Candidate_Id', candidateId)
                        .then(data => {
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: '', message: 'Candidate accreditation details retrieved successfully.', accreditationDetails: data };
                        })
                        .catch(e => {
                            console.log("Error in retrieveAccreditationDetails() function .catch block", e);
                            throw ("EO0105")
                        })
                )
            }
            else {
                throw ('EO0102')
            }
        }
        else {
            throw ('IVE0000')
        }
    }
    catch (e) {
        console.log("Error in retrieveAccreditationDetails() function main catch block", e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (e == 'IVE0000') {
            let errResult = commonLib.func.getError(e, 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        let errResult = commonLib.func.getError(e, 'EO0105');
        throw new ApolloError(errResult.message, errResult.code);
    }
}