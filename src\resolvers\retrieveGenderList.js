const resolvers = {
    Query : {
        retrieveGenderList : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig("gender")
            .select('Gender_Id as genderId', 'Gender as gender')
            .orderBy('Gender', 'asc')
            .then((gender)=>{
                if(!gender[0]){
                    return {
                        genderData: null
                    }
                } else{
                    return {
                        genderData: gender
                    }
                }
            })
            .catch(function(err){
                console.log('Error in retrieveGenderList',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;