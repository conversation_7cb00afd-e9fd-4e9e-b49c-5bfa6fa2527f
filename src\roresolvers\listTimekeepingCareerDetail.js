//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');


module.exports.listTimekeepingCareerDetail = async (parent, args, context, info) => {
    console.log('Inside listTimekeepingCareerDetail function', args);
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

       const [timekeepingList, careerList, taxDetails] =  await Promise.all([
            organizationDbConnection(ehrTables.timekeepingPic).select('Timekeeping_Id', 'Timekeeping_Name'),
            organizationDbConnection(ehrTables.careerPic).select('Career_Id', 'Career_Name'),
            organizationDbConnection(ehrTables.taxDetails).select('Tax_Code', 'Tax_Description', 'Exemption_Amount')
        ]);

        organizationDbConnection ? organizationDbConnection.destroy() : null;
        
        return {errorCode: '', timeKeeping: timekeepingList, career: careerList, taxDetail: taxDetails, message: 'Retrieve timekeeping, career, tax details list of records'}
     
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listTimekeepingCareerDetail function main catch block.', e);
        let errResult = commonLib.func.getError(e, '_UH0001');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
