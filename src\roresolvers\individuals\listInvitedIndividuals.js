// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../common/appConstants');
const { getIndividualsRollAccess, fetchJobRoleDetails } = require('../../common/commonFunctions');

module.exports.listInvitedIndividuals = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside listInvitedIndividuals function.");
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, null, '', 'UI', false, formIds.individuals);
        if (!(Object.keys(checkRights).length > 0 && checkRights.Role_View === 1)) {
            throw '_DB0100'
        }

        let spResult = await getIndividualsRollAccess(organizationDbConnection, checkRights, context.Employee_Id)
                
        return (
            organizationDbConnection(ehrTables.candidateUrl + " as CU")
                .select("CU.Name as Candidate_Name", "CU.Email as Candidate_Email", "CU.Candidate_Id", "CU.Designation_Id",
                    organizationDbConnection.raw("CASE WHEN DES.Designation_Code IS NOT NULL THEN CONCAT(DES.Designation_Code,' - ',DES.Designation_Name) ELSE DES.Designation_Name END AS Designation_Name"),
                    "CU.Department_Id",
                    "CU.Status", "CU.Location_Id", "CU.Date_Of_Join", "CU.Probation_Date",
                    "CU.EmpType_Id",
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN ET.Employee_Type_Code IS NOT NULL 
                            THEN CONCAT(ET.Employee_Type_Code, ' - ', ET.Employee_Type) 
                            ELSE ET.Employee_Type 
                        END AS Employee_Type
                    `), 
                    "CU.Manager_Id", "CU.Work_Schedule", "CU.Service_Provider_Id", "CU.Business_Unit_Id",
                    "CU.Created_At", "CU.Created_By", "CU.Updated_At", "CU.Updated_By", "CU.Url", "CU.Url_Hash", "CU.Pin",
                    "CU.Job_Code", "CU.Expire_Time","WS.Title as WorkSchedule_Name",
                    organizationDbConnection.raw('CONCAT_WS(" ",EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as Created_By_Name'),
                    organizationDbConnection.raw('CONCAT_WS(" ",EP1.Emp_First_Name, EP1.Emp_Middle_Name, EP1.Emp_Last_Name) as Updated_By_Name'),
                    organizationDbConnection.raw(
                        "CASE WHEN BU.Business_Unit_Code IS NOT NULL THEN CONCAT(BU.Business_Unit_Code, ' - ', BU.Business_Unit) ELSE BU.Business_Unit END AS Business_Unit"
                    ),
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN DEP.Department_Code IS NOT NULL 
                            THEN CONCAT(DEP.Department_Code, ' - ', DEP.Department_Name) 
                            ELSE DEP.Department_Name 
                        END AS Department_Name
                    `),
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN SP.Service_Provider_Code IS NOT NULL 
                            THEN CONCAT(SP.Service_Provider_Code, ' - ', SP.Service_Provider_Name) 
                            ELSE SP.Service_Provider_Name 
                        END AS Service_Provider_Name
                    `),
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN L.Location_Code IS NOT NULL 
                            THEN CONCAT(L.Location_Code, ' - ', L.Location_Name) 
                            ELSE L.Location_Name 
                        END AS Location_Name
                    `),
                    organizationDbConnection.raw('CONCAT_WS(" ",EP2.Emp_First_Name, EP2.Emp_Middle_Name, EP2.Emp_Last_Name) as Manager_Name'),
                    'OG.Organization_Group_Id',
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN OG.Organization_Group_Code IS NOT NULL 
                            THEN CONCAT(OG.Organization_Group_Code, ' - ', OG.Organization_Group) 
                            ELSE OG.Organization_Group 
                        END AS Organization_Group
                    `),
                    organizationDbConnection.raw(
                        "GROUP_CONCAT(DEG.Group_Name ORDER BY CDEG.Group_Id SEPARATOR ',') as Group_Names"
                    ),
                    organizationDbConnection.raw(
                        "GROUP_CONCAT(ACETG.Group_Name ORDER BY ACETG.Group_Id SEPARATOR ',') as Accreditation_Group_Names"
                    ),
                    'CU.Job_Role_Ids',
                    organizationDbConnection.raw(
                        "GROUP_CONCAT(CDEG.Group_Id ORDER BY CDEG.Group_Id SEPARATOR ',') as Group_Ids"
                    ),
                    organizationDbConnection.raw(
                        "GROUP_CONCAT(ACETG.Group_Id ORDER BY ACETG.Group_Id SEPARATOR ',') as Accreditation_Group_Ids"
                    ))
                    .groupBy('CU.Candidate_Id')
                .leftJoin(ehrTables.empPersonalInfo + " as EP", "EP.Employee_Id", "CU.Created_By")
                .leftJoin(ehrTables.empPersonalInfo + " as EP1", "EP1.Employee_Id", "CU.Updated_By")
                .leftJoin(ehrTables.empPersonalInfo + " as EP2", "EP2.Employee_Id", "CU.Manager_Id")
                .leftJoin(ehrTables.designation + " as DES", "DES.Designation_Id", "CU.Designation_Id")
                .leftJoin(ehrTables.department + " as DEP", "DEP.Department_Id", "CU.Department_Id")
                .leftJoin(ehrTables.location + " as L", "L.Location_Id", "CU.Location_Id")
                .leftJoin(ehrTables.employeeType + " as ET", "ET.EmpType_Id", "CU.EmpType_Id")
                .leftJoin(ehrTables.businessUnit + " as BU", "BU.Business_Unit_Id", "CU.Business_Unit_Id")
                .leftJoin(ehrTables.serviceProvider + " as SP", "SP.Service_Provider_Id", "CU.Service_Provider_Id")
                .leftJoin(ehrTables.workSchedule + " as WS", "WS.WorkSchedule_Id", "CU.Work_Schedule")
                .leftJoin(ehrTables.organizationGroup + " as OG", "OG.Organization_Group_Id", "CU.Organization_Group_Id")
                .leftJoin(ehrTables.candidateDocumentEnforcementGroups + ' as CDEG', "CDEG.Candidate_Id", "CU.Candidate_Id")
                .leftJoin(ehrTables.documentEnforcementGroup + ' as DEG', 'DEG.Group_Id', 'CDEG.Group_Id')
                .leftJoin(ehrTables.candidateAccreditationGroups + ' as CAG', 'CAG.Candidate_Id', 'CU.Candidate_Id')
                .leftJoin(ehrTables.accreditationEnforcementGroups + ' as ACETG', 'ACETG.Group_Id', 'CAG.Group_Id')
                .modify(function(){
                    if(args.offset){
                        this.offset(args.offset)
                    }
                    if(args.limit){
                        this.limit(args.limit)
                    }
                    if(spResult && spResult.isOrgUnitFlag)
                        this.whereIn('CU.Service_Provider_Id', spResult.serviceProviderids)
                    
                })
                .then(async(individuals) => {
                    individuals = await fetchJobRoleDetails(individuals, organizationDbConnection);
                    if (individuals) {
                        individuals = JSON.stringify(individuals)
                    }
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Invited individuals retrieved successfully", listIndividuals: individuals }
                })
                .catch((e) => {
                    // Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    console.log('Error in listInvitedIndividuals function .catch block.', e);
                    const errResult = commonLib.func.getError(e, 'IO0101');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listInvitedIndividuals function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'IO00001');
        throw new ApolloError(errResult.message, errResult.code);
    }
};
