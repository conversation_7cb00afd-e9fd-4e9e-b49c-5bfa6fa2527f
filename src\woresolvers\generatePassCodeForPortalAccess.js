const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const dbConnection = require('../resolvers/dbConnection');
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');
const ehrTables = require('../common/tableAlias').ehrTables;
const randomize = require('randomatic');
const { sendEmail, sendCustomEmail } = require('../common/commonFunctions');
const getS3Path = require('../resolvers/formS3ObjectUrl');
const moment = require('moment-timezone');

// Input validation schema
const validateInput = (emailId) => {
    if (!emailId || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailId)) throw 'IVE0529'; //The email address provided seems to be invalid.
};

// Database operations wrapper
const getCandidateData = async (organizationDbConnection, emailId, blacklistedCandidatePortalAccess, archivedCandidateAccess) => {
    const candidateInfo = await organizationDbConnection(ehrTables.candidatePersonalInfo + ' as CPI')
        .select('CPI.Candidate_Id', 'CPI.Personal_Email', 'ASS.Stage',
            organizationDbConnection.raw('CONCAT_WS(" ", CPI.Emp_First_Name, CPI.Emp_Middle_Name, CPI.Emp_Last_Name) as candidateName')
        )
        .innerJoin(ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
        .leftJoin(ehrTables.atsStatusTable + ' as ATS', 'ATS.Id', 'CRI.Candidate_Status')
        .leftJoin(ehrTables.atsStatusStage + ' as ASS', 'ASS.Stage_Id', 'ATS.Stage_Id')
        .where({
            'CPI.Personal_Email': emailId,
            'CRI.Portal_Access_Enabeld': 'Yes'
        }).modify(function (queryBuilder) {
            if (blacklistedCandidatePortalAccess.toLowerCase() === 'no') {
                queryBuilder.whereNot('CRI.Blacklisted', 'Yes');
            }
            if (archivedCandidateAccess.toLowerCase() === 'no') {
                queryBuilder.where(function () {
                    this.whereNot('CRI.Archived', 'Yes')
                        .whereNot('ASS.Stage', 'Archived');
                });
            }
        })
        .orderBy([
            { column: 'ASS.Stage_Id', order: 'asc' },
            { column: 'CPI.Candidate_Id', order: 'desc' }
        ])

    return candidateInfo;
};

module.exports.generatePassCodeForPortalAccess = async (parent, args, context, info) => {
    let organizationDbConnection;
    console.log("Inside generatePassCodeForPortalAccess function.")
    try {
        // Input validation
        validateInput(args.emailId);

        // Database connection
        const connection = await dbConnection.getConnection(context.Org_Code);
        organizationDbConnection = knex(connection);

        // Check if email exists
        const emailExists = await organizationDbConnection(ehrTables.candidatePersonalInfo)
            .where('Personal_Email', args.emailId).first();

        if (!emailExists) throw 'IVE0571'; //The email address entered does not exist in the system. Please contact system administrator.

        const settings = await organizationDbConnection('recruitment_settings')
        .select('Exp_portal_Expiry_Time_In_Mins', 'Blacklisted_Candidate_Portal_Access', 'Archived_Candidate_Portal_Access')
        .first()

        if (!settings) throw 'EO0113'; //Sorry! The portal access settings are not configured. Please contact the system administrator for assistance.

        // Parallel database operations
        const candidateData = await getCandidateData(organizationDbConnection, args.emailId, settings?.Blacklisted_Candidate_Portal_Access || 'No', settings?.Archived_Candidate_Portal_Access || 'No');
 

        if (!candidateData?.length) throw 'EO0114'; //Sorry, your account does not have portal access. Please contact the system administrator for assistance.

        // Get candidate details
        const candidateDetail = candidateData.find(candidate => 
            candidate.Stage?.toLowerCase() !== 'preboarding') || candidateData[0];
        
        // Generate and save passcode
        const pin = randomize('?', 6, { chars: '*********' });
        const expiryTimeInMins = Number(settings?.Exp_portal_Expiry_Time_In_Mins || 60);
        const expiryTime = moment.utc().add(expiryTimeInMins, 'minutes').format('YYYY-MM-DD HH:mm:ss');

        await organizationDbConnection(ehrTables.candidateRecruitmentInfo)
            .where('Candidate_Id', candidateDetail.Candidate_Id)
            .update({
                Portal_Access_Passcode: pin,
                Passcode_Expire_Time: expiryTime
            });

        // Try sending email with default template
        const defaultTemplate = await commonLib.func.getDefaultTemplate(
            { categoryId: 18, formId: 311 },
            organizationDbConnection
        );

        const [orgDetails, reportLogoFileUrl, notificationParams] = await Promise.all([
            commonLib.func.getOrgDetails(context.Org_Code, organizationDbConnection, 1),
            getS3Path.getReportLogo(context.Org_Code, organizationDbConnection),
            organizationDbConnection('email_notification_setting').select('Add_Logo', 'Sender_Name').first()
        ]);

        if (defaultTemplate?.Template_Id) {

            const eventVal = {
                Source: process.env.emailFrom,
                ReplyToAddresses: [process.env.emailReplyTo],
                bucketName: process.env.documentsBucket,
                region: process.env.region
            };
        
            const { emailResult, ses, event } = await commonLib.func.listEmailTemplatePlaceHolderValues(
                { templateId: defaultTemplate.Template_Id, candidateId: candidateDetail.Candidate_Id },
                organizationDbConnection,
                context,
                eventVal,
                [],
                { portalAccessExpiredTime: expiryTimeInMins }
            );
        
            const emailResponse = await sendCustomEmail(emailResult, ses, event, notificationParams?.Sender_Name);
            if (!emailResponse) throw 'CET00010';  
            
            return { message: 'Candidate portal access passcode sent successfully', errorCode: '' };
    
        }

       
    
        const htmlTable =`<body>
            <p>Dear ${candidateDetail.candidateName || "Candidate"},</p>
            <p>As part of the process for accessing the portal, we have generated a secure passcode for your use.</p>
            <p><strong>Passcode:</strong> ${pin} <br><br> 
            <strong>Expiry Time:</strong> ${expiryTimeInMins} minutes from the time this email was sent.</p>
            <p>Please use this passcode to log in to your account. For your security reasons, do not share this passcode with anyone.</p>
            <p>Best regards,</p>
            <p>${orgDetails.orgName}</p>
        </body>`;
        
        const params = {
            Source: notificationParams?.Sender_Name ? `${notificationParams?.Sender_Name} <${process.env.emailFrom}>` : process.env.emailFrom,
            Template: "RecruitmentEmail",
            Destination: {
                ToAddresses: [candidateDetail.Personal_Email]
            },
            ReplyToAddresses: [],
            TemplateData: JSON.stringify({
                emailSubject: 'Your Secure Passcode for Portal Access',
                orgLogo: notificationParams?.Add_Logo?.toLowerCase() === 'yes' && reportLogoFileUrl ? reportLogoFileUrl : '',
                htmlTable,
                hrappSupportEmail: orgDetails.hrAdminEmailAddress || ''
            })
        };
    
        const emailResponse = await sendEmail(params);
        if (!emailResponse) throw 'CET00010'; //Email sending failed. Please try again later.   
        return { message: 'Candidate portal access passcode sent successfully', errorCode: '' };

    } catch (error) {
        console.error('Error in generatePassCodeForPortalAccess main catch block:', error);
        const errResult = commonLib.func.getError(error, 'EO0113'); //Error while processing the request to verify your details. Please try after some time.
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
};
