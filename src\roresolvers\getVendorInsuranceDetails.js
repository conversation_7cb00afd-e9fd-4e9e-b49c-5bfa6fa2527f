//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');

//fuction to get vendor details
let organizationDbConnection;
module.exports.getVendorInsuranceDetails = async (parent, args, context, info) => {
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');

        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1 && checkRights.Employee_Role && checkRights.Employee_Role.toLowerCase()=== 'admin') {
            return(
                organizationDbConnection(ehrTables.documentCompliance + " as DC")
                .select(
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN OV.Service_Provider_Code IS NOT NULL 
                            THEN CONCAT(OV.Service_Provider_Code, ' - ', OV.Service_Provider_Name) 
                            ELSE OV.Service_Provider_Name 
                        END AS vendorName
                    `),
                    "OV.Vendor_Type as vendorType","DC.Vendor_Id as vendorId","DC.Insurance_Id as insuranceId", "DC.Type_Of_Insurance as typeOfInsurance","DC.Name_Of_Insurance as nameOfInsurance", "DC.Policy_Number as policyNumber",
                    "DC.Name_Of_Insurer as nameOfInsurer", "DC.Name_Of_Insured as nameOfInsured", "DC.Sum_Insured as sumInsured", "DC.Description as description",
                    "DC.Expiry_Date as expiryDate","DC.Document_File as documentFile", "DC.Verification as verification", "DC.Verified_Date as verifiedDate", "DC.Last_Reminder_Date as lastReminderDate")
                .leftJoin(ehrTables.serviceProvider + " as OV", "OV.Service_Provider_Id", "DC.Vendor_Id")
                .where(function(){
                    if(args.vendorId){
                        this.where('Vendor_Id', args.vendorId) 
                    }
                })
                .then((data) => {
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Vendor insurance details retrieved successfully.", insuranceDetails: data};
                })
                .catch(e=>{
                    console.log("Error in getVendorInsuranceDetails function .catch block.",e);
                    throw('VO0125')
                })
            )
        }
        else{
            console.log('No rights to view Vendors Insurance Details.');
            throw '_DB0100';
        }
    }
    catch(e)
    {
        console.log("Error in getVendorInsuranceDetails function main catch block.",e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(e, 'VO0125');
        throw new ApolloError(errResult.message, errResult.code)
    }
}