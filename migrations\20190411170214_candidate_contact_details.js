
exports.up = function(knex, Promise) {
  return knex.schema.createTable('candidate_contact_details',function(table){
    table.integer('Candidate_Id',11).primary();
    table.string('pApartment_Name',100);
    table.string('pStreet_Name',100);
    table.string('pCity',50);
    table.string('pState',50);
    table.string('pCountry',50);
    table.string('pPincode',15);
    table.string('cApartment_Name',100);
    table.string('cStreet_Name',100);
    table.string('cCity',50);
    table.string('cState',50);
    table.string('cCountry',50);
    table.string('cPincode',15);
    table.integer('Use_Location_Address',4);
    table.string('oApartment_Name',100);
    table.string('oStreet_Name',100);
    table.string('oCity',50);
    table.string('oState',50);
    table.string('oCountry',50);
    table.string('oPincode',15);
    table.string('Land_Line_No',15);
    table.string('Mobile_No',15);
    table.string('Fax_No',15);
  })
};

exports.down = function(knex, Promise) {
    return knex.schema.dropTable('candidate_contact_details');
};
