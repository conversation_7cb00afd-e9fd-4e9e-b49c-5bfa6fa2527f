const resolvers = {
    Mutation: {
        deleteExperience: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');
            var dateFormat = require('dateformat');

            var Candidate_Id;
            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_url')
                    .where('Url_Hash', args.Url_Hash)
                    .then((url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } else {
                            Candidate_Id = url[0].Candidate_Id;
                            return knexconfig('candidate_experience')
                                .where('Experience_Id', args.Experience_Id)
                                .andWhere('Candidate_Id', Candidate_Id)
                                .then((experience) => {
                                    if (!experience[0]) {
                                        throw new Error("Experience Detail not found");
                                    } else {
                                        return knexconfig('candidate_experience')
                                            .where('Experience_Id', args.Experience_Id)
                                            .del()
                                            .transacting(trc)
                                            .then(async () => {
                                                await knexconfig('candidate_experience_reference')
                                                    .where('Experience_Id', args.Experience_Id)
                                                    .del()
                                                    .transacting(trc);
                                                return knexconfig('candidate_experience_documents')
                                                    .where('Experience_Id', args.Experience_Id)
                                                    .then((document_exist) => {
                                                        if (!document_exist[0]) {
                                                            return {
                                                                message: "Experience details deleted"
                                                            }
                                                        } else {
                                                            return knexconfig('candidate_experience_documents')
                                                                .where('Experience_Id', args.Experience_Id)
                                                                .del()
                                                                .transacting(trc)
                                                                .then(() => {
                                                                    return {
                                                                        message: "Experience details deleted"
                                                                    }
                                                                })
                                                        }
                                                    })
                                            })
                                    }
                                })
                        }
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                return knexconfig.select('candidate_experience.*', 'candidate_experience_documents.File_Name')
                    .from('candidate_experience')
                    .leftJoin('candidate_experience_documents', 'candidate_experience.Experience_Id', 'candidate_experience_documents.Experience_Id')
                    .where('candidate_experience.Candidate_Id', Candidate_Id)
                    .orderBy('candidate_experience.Experience_Id')
                    .then((experience) => {
                        if (!experience[0]) {
                            return {
                                experience: null
                            }
                        } else {
                            var newExperience = experience.map((arr) => {
                                arr['Start_Date'] = dateFormat(new Date(arr['Start_Date']), "yyyy-mm-dd");
                                arr['End_Date'] = dateFormat(new Date(arr['End_Date']), "yyyy-mm-dd");
                                return arr;
                            });
                            return {
                                experience: newExperience
                            }
                        }
                    })
            }).catch(function (err) {
                console.log('Error in deleteExperience', err);
                if (err.message == "REO0101") {
                    console.log('URL not found')
                    throw new ApolloError("URL not found", "REO0101")
                } else if (err.message == "Experience Detail not found") {
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;