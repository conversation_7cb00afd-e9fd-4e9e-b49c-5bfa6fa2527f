
exports.up = function(knex, Promise) {
  return knex.schema.createTable("candidate_bankdetails",function(table){
    table.increments('Bank_Id',11).primary();
    table.integer('Candidate_Id',11).unique().notNullable();
    table.string('Bank_Account_Number',30).notNullable();
    table.string('Bank_Name',50).notNullable();
    table.integer('Emp_Bank_Id',11).notNullable();
    table.string('Branch_Name',50).notNullable();
    table.string('IFSC_Code',30).notNullable();
    table.string('Street',100).notNullable();
    table.string('City',50).notNullable();
    table.string('State',50).notNullable();
    table.string('Zip',15).notNullable();
    table.integer('Account_Type_Id',11).notNullable();
    table.string('Credit_Account',50).notNullable();
    table.string('Beneficiary_Id',50);
    table.string('Status',150).notNullable().defaultTo('Active');;
    table.boolean('Lock_Flag',11).notNullable().defaultTo(false);
  })
};

exports.down = function(knex, Promise) {
    return knex.schema.dropTable('candidate_bankdetails');
};
