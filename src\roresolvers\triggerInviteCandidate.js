'use strict';
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const {resendCandidateInvite} =require('../stepFunction/resendCandidateInvite');
//Require table alias
// Function to initiate triggerInviteCandidate step function
module.exports.triggerInviteCandidate  = async(parent,args, context) =>{
    let organizationDbConnection;
    try{
        console.log('Inside triggerInviteCandidate function',args);
        let orgCode = context.Org_Code
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, args.formId);
        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Add === 1 || checkRights.Role_Update === 1)) {
            // We will be triggering the step function to run resend invite candidate in background process
            let inputParams={'orgCode':orgCode,'logInEmpId':loginEmployeeId,candidates:args.candidates,organizationDbConnection:organizationDbConnection};
            let triggerInviteCandidateResponse= await commonLib.stepFunctions.triggerStepFunction(process.env.resendInviteCandidateStepFunction,'triggerInviteCandidate','',inputParams);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return {errorCode:'',message: 'ResendInviteCandidate initiated successfully.'};
        }
        else{
            console.log('No rights to resend invite to the candidate');
            throw '_DB0111';
        }
        
    }
    catch(mainCatchError){
        console.log('Error in triggerInviteCandidate function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'CCH0015');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
};

