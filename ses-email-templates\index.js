// require npm packages to read html contents
const fs = require('fs');
const path = require('path');
// Define AWS SES templates
const templateList = [  
    {
        templateId: 'selfOnboardSendPin',
        templateSubject: 'Reminder: Invitation to join {{companyName}}',
        templateHtml: fs.readFileSync(path.join(__dirname, 'templates/selfOnboardSendPin.html')).toString('utf8'),
        templateText: 'The pin changed to {{pin}}.',
    },
    {
        templateId: 'selfOnboardSendUrl',
        templateSubject: 'ACTION REQUIRED:Request from {{companyName}} for your Onboarding process',
        templateHtml: fs.readFileSync(path.join(__dirname, 'templates/selfOnboardSendUrl.html')).toString('utf8'),
        templateText: 'You have requested to fill your details.',
    }
];
templateList.reduce((acc, templateInfo) => {
    const { templateId } = templateInfo;
    if (acc[templateId] === 1) {
        throw new Error(`Error: Duplicate SES template id "${templateId}", they should be unique`);
    }
    acc[templateId] = 1;
    return acc;
}, {});

/**
 * @param {Object} serverless - Serverless instance
 * @param {Object} _options - runtime options
 * @returns {Promise<{name: string, subject: string, html: string, text}[]>}
 */
module.exports = async (serverless, _options) => {
    // You can load template configuration from filesystem using serverless object + runtime options
    // or from any other source like database or API

    const sesEmailTemplates = templateList.map((templateInfo) => {
        const { templateId, templateSubject, templateHtml, templateText } = templateInfo;
        return {
            name: templateId,
            subject: templateSubject,
            html: templateHtml,
            text: templateText
        };
    });
    return sesEmailTemplates;
};
