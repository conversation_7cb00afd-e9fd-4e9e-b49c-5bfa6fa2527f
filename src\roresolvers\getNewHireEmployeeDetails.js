//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formIds } = require('../common/appConstants');

module.exports.getNewHireEmployeeDetails = async (parent, args, context) => {
    let organizationDbConnection;
    try {
        console.log("Inside getNewHireEmployeeDetails function.");
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check employee access rights
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, 
            context.Employee_Id, 
            null, 
            '', 
            'UI', 
            false, 
            formIds.individuals
        );

        if (!(checkRights?.Role_Update === 1)) {
            throw '_DB0102'; // Access denied error
        }

        // Retrieve hiring manager details
        const hiringManagerDetails = await organizationDbConnection(ehrTables.externalApiIntegrationLog + ' as SAIL')
            .select(
                "SAIL.Candidate_Id", 
                "EJ.Designation_Id",
                "DES.Designation_Code",
                "EP.Employee_Id",
                "EJ.Organization_Group_Id",
                "OG.Organization_Group_Code",
                organizationDbConnection.raw(`
                    CASE 
                        WHEN OG.Organization_Group_Code IS NOT NULL 
                        THEN CONCAT(OG.Organization_Group_Code, ' - ', OG.Organization_Group) 
                        ELSE OG.Organization_Group 
                    END AS Organization_Group
                `),
                organizationDbConnection.raw(`
                    CASE 
                        WHEN SP.Service_Provider_Code IS NOT NULL 
                        THEN CONCAT(SP.Service_Provider_Code, ' - ', SP.Service_Provider_Name) 
                        ELSE SP.Service_Provider_Name 
                    END AS Organization_Unit_Name
                `),
                'EP.Emp_Last_Name',
                'EP.Emp_First_Name',
                'EP.Emp_Middle_Name',
                "EJ2.Emp_Email as Manager_Email",
                organizationDbConnection.raw("CASE WHEN DES.Designation_Code IS NOT NULL THEN CONCAT(DES.Designation_Code,' - ',DES.Designation_Name) ELSE DES.Designation_Name END AS Designation_Name"),
                organizationDbConnection.raw('CONCAT_WS(" ",EP.Emp_Last_Name,EP.Emp_First_Name, EP.Emp_Middle_Name) as Employee_Name'),
                organizationDbConnection.raw('CONCAT_WS(" ",  EP2.Emp_Last_Name,EP2.Emp_First_Name,EP2.Emp_Middle_Name) as Manager_Name'),
                organizationDbConnection.raw("(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EP.Employee_Id END) as userDefinedEmpId")
            )
            .groupBy('SAIL.Candidate_Id')
            .leftJoin(ehrTables.empPersonalInfo + " as EP", "EP.Employee_Id", "SAIL.Employee_Id")
            .leftJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "EP.Employee_Id")
            .leftJoin(ehrTables.serviceProvider + " as SP", "SP.Service_Provider_Id", "EJ.Service_Provider_Id")
            .leftJoin(ehrTables.organizationGroup + " as OG", "OG.Organization_Group_Id", "EJ.Organization_Group_Id")
            .leftJoin(ehrTables.designation + " as DES", "DES.Designation_Id", "EJ.Designation_Id")
            .leftJoin(ehrTables.empPersonalInfo + " as EP2", "EP2.Employee_Id", "EJ.Manager_Id")
            .leftJoin(ehrTables.empJob + " as EJ2", "EJ2.Employee_Id", "EJ.Manager_Id")
            .where("SAIL.Candidate_Id", args.candidateId);

            let resultData={}
        if (hiringManagerDetails?.length && hiringManagerDetails[0].Employee_Id) {
        // Fetch second-line manager ID 
        const managerId = await commonLib.func.getSecondLineManager(organizationDbConnection,  hiringManagerDetails[0].Employee_Id,0);
        const firstLineManagerDetails= managerId?await getfirstLineManagerDetails(managerId,organizationDbConnection):'';
        let parentStructureDetails;
        if (hiringManagerDetails[0].Designation_Code) {
            parentStructureDetails = await organizationDbConnection(ehrTables.SFWPOrganizationStructure)
                .select('Pos_Code', 'Pos_Name', 'Org_Level', 'Parent_Path')
                .from(ehrTables.SFWPOrganizationStructure)
                .where('Pos_Code', hiringManagerDetails[0].Designation_Code);
        }
        let positionDetailsObject={};
        if (parentStructureDetails && parentStructureDetails.length > 0) {
            let parentStructureIds = parentStructureDetails[0].Parent_Path
                ? parentStructureDetails[0].Parent_Path.split(',')
                : [];
        
            // Only proceed if parentStructureIds is not empty
            let parentStructureDetailsArray = [];
            if (parentStructureIds.length > 0) {
                parentStructureDetailsArray = await organizationDbConnection(ehrTables.SFWPOrganizationStructure)
                    .select('Pos_Code', 'Pos_Name', 'Org_Level')
                    .whereIn('Originalpos_Id', parentStructureIds);
            }
            if (parentStructureDetailsArray && parentStructureDetailsArray.length > 0) {
                positionDetailsObject.groupCode = parentStructureDetailsArray.filter(
                    (grp) => grp.Org_Level === 'GRP'
                )[0]?.Pos_Code;
                positionDetailsObject.groupName = parentStructureDetailsArray.filter(
                    (grp) => grp.Org_Level === 'GRP'
                )[0]?.Pos_Name;
                positionDetailsObject.divisionCode = parentStructureDetailsArray.filter(
                    (div) => div.Org_Level === 'DIV'
                )[0]?.Pos_Code;
                positionDetailsObject.divisionName = parentStructureDetailsArray.filter(
                    (div) => div.Org_Level === 'DIV'
                )[0]?.Pos_Name;
                positionDetailsObject.sectionCode = parentStructureDetailsArray.filter(
                    (sec) => sec.Org_Level === 'SEC'
                )[0]?.Pos_Code;
                positionDetailsObject.sectionName = parentStructureDetailsArray.filter(
                    (sec) => sec.Org_Level === 'SEC'
                )[0]?.Pos_Name;
                positionDetailsObject.deptCode = parentStructureDetailsArray.filter(
                    (dept) => dept.Org_Level === 'DEPT'
                )[0]?.Pos_Code;
                positionDetailsObject.deptName = parentStructureDetailsArray.filter(
                    (dept) => dept.Org_Level === 'DEPT'
                )[0]?.Pos_Name;
            }
        }
        
        // Add direct manager ID to the position details
        positionDetailsObject.directManagerDetails = firstLineManagerDetails || null;
        
        // Combine the result data
        resultData = {
            ...hiringManagerDetails[0],
            ...positionDetailsObject
        };
        
    }
    else{
        throw 'CCH0021'
    }

        // Clean up the database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        const message = hiringManagerDetails?.length 
        ? "Hiring manager data retrieved successfully" 
        : "Employee details not found";
        return {
            errorCode: "",
            message: message,
            hiringManagerDetails: JSON.stringify(resultData)
        };

    } catch (e) {
        // Clean up the database connection in case of an error
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.error('Error in getNewHireEmployeeDetails:', e);
        const errResult = commonLib.func.getError(e, 'CCH0020');
        throw new ApolloError(errResult.message, errResult.code);
    }
};


async function getfirstLineManagerDetails(managerId, organizationDbConnection) {
    if (!managerId) return null;

    const managerDetails = await organizationDbConnection(ehrTables.empPersonalInfo + " as EP")
        .select(
            organizationDbConnection.raw('CONCAT_WS(" ", EP.Emp_Last_Name, EP.Emp_Middle_Name, EP.Emp_First_Name) as Manager_Name'),
            "EJ.Emp_Email as Manager_Email"
        )
        .leftJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "EP.Employee_Id")
        .where("EP.Employee_Id", managerId)
        .first();

    let secondlineManagerDetails={}
    secondlineManagerDetails.Manager_Name=managerDetails?.Manager_Name;
    secondlineManagerDetails.Manager_Email=managerDetails?.Manager_Email;
    return secondlineManagerDetails;
}