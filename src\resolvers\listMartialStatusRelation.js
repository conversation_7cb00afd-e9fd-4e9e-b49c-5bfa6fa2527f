const resolvers = {
    Query: {
        listMartialStatusRelation: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_url')
                    .where('Url_Hash', args.Url_Hash)
                    .then((url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } else {
                            return knexconfig("marital_status_relationship")
                                .distinct('Dependent_Relationship')
                                .then((relation) => {
                                    if (!relation[0]) {
                                        return {
                                            marital_status_relation: null
                                        }
                                    } else {
                                        return {
                                            marital_status_relation: relation
                                        }
                                    }
                                })
                        }
                    })
                    .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                console.log('return successful response from listMartialStatusRelation');
                return result;
            }).catch(function (err) {
                console.log('Error in listMartialStatusRelation', err);
                if (err.message == "REO0101") {
                    console.log('URL not found')
                    throw new ApolloError("URL not found", "REO0101")
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;