
module.exports.graphqlHandler = (event, context, callback) => {
  context.callbackWaitsForEmptyEventLoop = false;
  var { ApolloServer, gql } = require('apollo-server-lambda');
  var { resolvers } = require('./resolvers');
  const fs = require('fs');
  const typeDefs = gql(fs.readFileSync(__dirname.concat('/schema.graphql'), 'utf8'));
  const server = new ApolloServer({
    typeDefs,
    resolvers
  });
  const handler = server.createHandler();
  function callbackFilter(error, output) {
      output.headers['Access-Control-Allow-Origin'] = '*';
      callback(error, output);
  }
  return handler(event, context, callbackFilter);
};

module.exports.tenantScript = (event, context, callback) => {
  const AWS = require('aws-sdk');
  
  var client = new AWS.SecretsManager({
    region: 'ap-south-1'
  });
  client.getSecretValue({SecretId: process.env.secretName}, function (err, data) {
    if(err){
      console.log(err);
    }else{
      var secret = data.SecretString;
      secret = JSON.parse(secret);
  
      var config = {
        client: 'mysql',
        connection: {
            host: process.env.dbHost,
            user: process.env.stage != 'local'? secret.username : "root",
            password: process.env.stage != 'local'? secret.password : "welcome2caprice",
            database: process.env.domainName + '_managerdb',
            charset: "utf8"
        }
      }
      const knex = require('knex')(config);
  
      knex.transaction(function(trc){
        knex.select('Org_Code as code').from('hrapp_registeruser')
        .then(async(data)=>{
            if(!data[0]){
                throw new Error('No Record Found')
            } else {
           
              const kms = new AWS.KMS({
                  region: 'ap-south-1'
              });
              
              const KmsJson = require('kms-json');
              const kmsJson = new KmsJson({
                awsKmsSettings: {
                  region: 'ap-south-1'
                },
                keyId: process.env.kmsKey
              });
              const encrypted = await kmsJson.encrypt({
                password: secret.password
              });
              console.log(encrypted);
  
              var newData = data.map((arr)=>{
  
                arr['host'] = process.env.dbHost,
                arr['username'] = secret.username;
                arr['password'] = encrypted;
                arr['db'] = process.env.domainName + '_' + arr['code'];
                arr['Privilege_Type'] = 'write';
  
                return arr;
              });
              return knex('tenant')
              .del()
              .transacting(trc)
              .then(()=>{
                return knex('tenant')
                  .insert(newData)
                  .transacting(trc)
                  .then((res)=>{
                    return "success";
                  })
              })
            }
        })
        .then(trc.commit)
            .catch(trc.rollback);
        }).then(function(result){
            const response = {
              statusCode: 200,
              body: JSON.stringify({
                message: "success"
              }),
            };
            callback(null, response);
        }).catch(function(err){
            throw new Error(err.message)
        }).finally(() => {
            knex.destroy();
        })
    }
  });
  };