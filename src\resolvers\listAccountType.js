const resolvers = {
    Query : {
        listAccountType : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0] && !args.Vendor_Based){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig("account_type")
                        .then((accountType)=>{
                            if(!accountType[0]){
                                return {
                                    accountType: null
                                }
                            } else{
                                return {
                                    accountType: accountType
                                }
                            }
                        })
                    }
                })
                .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listAccountType')
                return result;
            }).catch(function(err){
                console.log('Error in listAccountType',err);
                if (err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;