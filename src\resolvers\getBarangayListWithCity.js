//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

//function to list barangay with city
let organizationDbConnection;
module.exports.getBarangayListWithCity = async (parent, args, context, info) => {
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
            organizationDbConnection.select('barangay.Barangay_Id', 'barangay.Barangay_Name', 'barangay.Barangay_Code',
                'city.City_Id', 'city.City_Name', 'state.State_Id', 'state.State_Name',
                'region.Region_Id', 'region.Region_Name', 'region.Region_Code',
                'country.Country_Name', 'country.Country_Code',
                organizationDbConnection.raw(
                    'CONCAT_WS(", ", barangay.Barangay_Name, city.City_Name, state.State_Name, region.Region_Name, country.Country_Name) AS barangayDetails'
                  ))  
            .from('barangay')
            .orderBy('Barangay_Name', 'asc')
            .leftJoin('city', 'city.City_Id', 'barangay.City_Id')
            .leftJoin('state', 'state.State_Id', 'city.State_Id')
            .leftJoin('region', 'region.Region_Id', 'state.Region_Id')
            .leftJoin('country', 'country.Country_Code', 'region.Country_Code')
            .modify(function(queryBuilder) {
                if (args.searchString && args.searchString.length >= 3) {
                    queryBuilder.where('barangay.Barangay_Name', 'like', `%${args.searchString}%`);
                }
                if(args.cityId){
                    queryBuilder.where('barangay.City_Id', args.cityId);
                }
            })
            .then((data) => {

                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Barangay list has been fetched successfully.", barangayDetails: data ? data : [] };
                
            })
            .catch((catchError) => {
                console.log('Error in getBarangayListWithCity .catch() block', catchError);
                throw catchError;
            })
        )
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getBarangayListWithCity function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0025');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
