// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex')

const { ehrTables } = require('../common/tableAlias')

module.exports.deleteVendorDocument = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside deleteVendorDocument() function.")
        let documentId = args.documentId;
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights;
        if (args.checkAccess) {
            checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        }
        if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_Delete === 1)) {
            return (
                organizationDbConnection
                    .transaction(function (trx) {
                        return (
                            organizationDbConnection(ehrTables.vendorDocumentCategory)
                                .del()
                                .transacting(trx)
                                .where('Document_Id', documentId)
                                .then(deleteVendorDocumentCategory => {
                                    if (deleteVendorDocumentCategory) {
                                        return(
                                            organizationDbConnection(ehrTables.vendorDocuments)
                                            .del()
                                            .transacting(trx)
                                            .where('Document_Id', documentId)
                                            .then((deleteVendorDocument)=>{
                                                if(deleteVendorDocument){
                                                    return "success"
                                                }else{
                                                    console.log('Error while deleting vendor document', deleteVendorDocument)
                                                    throw 'VO0138'
                                                }
                                            })
                                        )
                                    } else {
                                        console.log(`Error while deleting the vendor document category for document id ${documentId}`, deleteVendorDocumentCategory)
                                        throw 'VO0138'
                                    }
                                })
                        )
                    })
                    .then((response) => {
                        if (response) {
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Vendor document deleted successfully" };
                        } else {
                            throw 'VO0138'
                        }
                    })
                    .catch(catchError => {
                        console.log('Error while deleting the vendor document', catchError);
                        let errResult = commonLib.func.getError(catchError, 'VO0135');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return error response
                        throw new ApolloError(errResult.message, errResult.code);
                    })
                    )
        }
        else {
            console.log('Employee do not have access to delete the vendor document')
            throw ('_DB0103')
        }
    }
    catch (mainCatchError) {
        console.log('Error in deleteVendorDocument function main block', mainCatchError);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'VO0024');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}