const resolvers = {
    Mutation : {
        deleteTraining : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');
            var dateFormat = require('dateformat');

            var Candidate_Id;
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        Candidate_Id = url[0].Candidate_Id;
                        return knexconfig('candidate_training')
                        .where('Training_Id',args.Training_Id)
                        .andWhere('Candidate_Id',Candidate_Id)
                        .then((training)=>{
                        if(!training[0]){
                            throw new Error("Training detail not found");
                        } else {
                                return knexconfig('candidate_training')
                                .where('Training_Id',args.Training_Id)
                                .del()
                                .transacting(trc)
                                .then(()=>{
                                    return knexconfig('candidate_training_documents')
                                    .where('Training_Id',args.Training_Id)
                                    .then((document_exist)=>{
                                        if(!document_exist[0]){
                                            return {
                                                message:"Training details deleted"
                                            }
                                        } else {
                                            return knexconfig('candidate_training_documents')
                                            .where('Training_Id',args.Training_Id)
                                            .del()
                                            .transacting(trc)
                                            .then(()=>{
                                                return {
                                                    message:"Training details deleted"
                                                }
                                            })
                                        }
                                    })
                                })
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return knexconfig.select('candidate_training.*',
                                        'candidate_training_documents.File_Name',
                                        'candidate_training_documents.Document_Name',
                                        'candidate_training_documents.Sub_Type_Id')
                .from('candidate_training')
                .leftJoin('candidate_training_documents','candidate_training.Training_Id','candidate_training_documents.Training_Id')
                .where('candidate_training.Candidate_Id',Candidate_Id)
                .orderBy('candidate_training.Training_Id')
                .then((training)=>{
                    if(!training[0]){
                        return {
                            training: null
                        }
                    } else {
                        var newTraining = training.map((arr)=>{ 
                            arr['Training_Start_Date']  = dateFormat(new Date(arr['Training_Start_Date']), "yyyy-mm-dd");
                            arr['Training_End_Date']  = dateFormat(new Date(arr['Training_End_Date']), "yyyy-mm-dd");
                            return arr;
                        });
                        return {
                            training: newTraining
                        }
                    }
                })
            }).catch(function(err){
                console.log('Error in deleteTraining',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Training detail not found"){
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;