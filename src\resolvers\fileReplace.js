const resolvers = {
    Query: {
        fileReplace: async (root, args) => {
            const { ehrTables } = require('../common/tableAlias');
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');
            const { s3FileUpload } = require('../common/imageConstants');
            const checkFileExtension = require('./checkFileExtension');
            let category = args.Category;
            let tableName = "";
            if (category == "trusteeAuthorizationDocuments" || category == "projectResources" || category == "insurance" || category == "vendorDocuments") {
                tableName = ehrTables.invitedVendors;
            } else {
                tableName = ehrTables.candidateUrl;
            }
            return knexconfig.transaction(function (trc) {
                return knexconfig(tableName)
                    .where('Url_Hash', args.Url_Hash)
                    .then(async (url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } else {
                            let fileExtension = (args.New_File_Name.substr(args.New_File_Name.lastIndexOf('.') + 1)).toLowerCase();
                            let path;
                            let bucketName;
                            let contentType;
                            let new_file_hash;

                            if (category == "profileImage") {
                                if (!checkFileExtension.isValid(category, fileExtension)) {
                                    throw new Error("REO0201");
                                } else {
                                    path = "hrapp_upload/" + args.Org_Code + "_tmp/images/";
                                    new_file_hash = "candidate_" + url[0].Candidate_Id + '.' + fileExtension;
                                    bucketName = process.env.imageBucket;
                                }
                            } else if (category == "document") {
                                if (!checkFileExtension.isValid(category, fileExtension)) {
                                    throw new Error("REO0202");
                                } else {
                                    path = process.env.domainName + "/" + args.Org_Code + "/" + "Employees Document Upload/";
                                    new_file_hash = url[0].Candidate_Id + '?' + (Date.now()).toString() + '?1?' + args.New_File_Name;
                                    bucketName = process.env.documentBucket;
                                }
                            } else if (category == "experience") {
                                if (!checkFileExtension.isValid(category, fileExtension)) {
                                    throw new Error("REO0202");
                                } else {
                                    path = process.env.domainName + "/" + args.Org_Code + "/" + "Employee Experience/";
                                    new_file_hash = url[0].Candidate_Id + '?' + (Date.now()).toString() + '?1?' + args.New_File_Name;
                                    bucketName = process.env.documentBucket;
                                }
                            } else if (category == "trusteeAuthorizationDocuments") {
                                if (!checkFileExtension.isValid(category, fileExtension)) {
                                    throw new Error("REO0202");
                                } else {
                                    path = process.env.domainName + "/" + args.Org_Code + "/" + url[0].Vendor_Id + "/" + "Authorization Documents/";
                                    new_file_hash = url[0].Vendor_Id + '?' + (Date.now()).toString() + '?1?' + args.New_File_Name;
                                    bucketName = process.env.vendorBucketName;
                                }
                            } else if (category == "projectResources") {
                                if (!checkFileExtension.isValid(category, fileExtension)) {
                                    throw new Error("REO0202");
                                } else {
                                    path = process.env.domainName + "/" + args.Org_Code + "/" + url[0].Vendor_Id + "/" + "Project Resources/";
                                    new_file_hash = url[0].Vendor_Id + '?' + (Date.now()).toString() + '?1?' + args.New_File_Name;
                                    bucketName = process.env.vendorBucketName;
                                }
                            } else if (category == "insurance") {
                                if (!checkFileExtension.isValid(category, fileExtension)) {
                                    throw new Error("REO0202");
                                } else {
                                    path = process.env.domainName + "/" + args.Org_Code + "/" + url[0].Vendor_Id + "/" + "Insurance/";
                                    new_file_hash = url[0].Vendor_Id + '?' + (Date.now()).toString() + '?1?' + args.New_File_Name;
                                    bucketName = process.env.vendorBucketName;
                                }
                            } else if (category == "vendorDocuments") {
                                if (!checkFileExtension.isValid(category, fileExtension)) {
                                    throw new Error("REO0202");
                                } else {
                                    file_hash = url[0].Vendor_Id + '?' + (Date.now()).toString() + '?1?' + args.File_Name;
                                    path = process.env.domainName + "/" + args.Org_Code + "/" + url[0].Vendor_Id + "/" + "Vendor Documents/" + args.New_File_Name;
                                    bucketName = process.env.vendorBucketName;
                                }
                            }
                            else if (category == "accreditation") {
                                if (!checkFileExtension.isValid(category, fileExtension)) {
                                    throw new Error("REO0202");
                                } else {
                                    path = process.env.domainName + "/" + args.Org_Code + "/" + "Employee Accreditation/";
                                    new_file_hash = url[0].Candidate_Id + '?' + (Date.now()).toString() + '?1?' + args.New_File_Name;
                                    bucketName = process.env.documentBucket;
                                }
                            }
                            else if (category == "bank") {
                                if (!checkFileExtension.isValid(category, fileExtension)) {
                                    throw new Error("REO0202");
                                } else {
                                    path = process.env.domainName + "/" + args.Org_Code + "/" + "Employee Bank Details/";
                                    new_file_hash = url[0].Candidate_Id + '?' + (Date.now()).toString() + '?1?' + args.New_File_Name;
                                    bucketName = process.env.documentBucket;
                                }
                            }
                            else {
                                throw new Error("Invalid document category")
                            }
                            switch (fileExtension) {
                                case 'jpg':
                                case 'jpeg': contentType = 'image/jpeg'; break;
                                case 'tif':
                                case 'tiff': contentType = 'image/tiff'; break;
                                case 'png': contentType = 'image/png'; break;
                                case 'pdf': contentType = 'application/pdf'; break;
                                case 'doc': contentType = 'application/doc'; break;
                                case 'docx': contentType = 'application/docx'; break;
                            }
                            try {
                                let deleteParams = {
                                    Bucket: bucketName,
                                    Key: path + args.File_Name
                                };

                                const AWS = require("aws-sdk");

                                const s3 = new AWS.S3();

                                let deleteResponse = await s3.deleteObject(deleteParams).promise();

                                let buffer = Buffer.from(args.New_File_Content, 'base64');

                                let uploadParams = {
                                    Bucket: bucketName,
                                    Key: path + new_file_hash,
                                    Body: buffer,
                                    ContentType: contentType,
                                    ContentEncoding: 'base64',
                                    ServerSideEncryption: s3FileUpload.defaultEncryption
                                }

                                let uploadResponse = await s3.upload(uploadParams).promise();

                                return {
                                    fileName: new_file_hash
                                }
                            }
                            catch (err) {
                                console.log("Err", err);
                                throw new Error("Something went wrong")
                            }
                        }
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                return result;
            }).catch(function (err) {
                console.log('Error in fileReplace', err);
                if (err.message == "REO0101") {
                    console.log('URL not found')
                    throw new ApolloError("URL not found", "REO0101")
                } else if (err.message == "REO0201") {
                    throw new UserInputError("Unsupported file format.Supported file formats are jpg,jpeg,png")
                } else if (err.message == "REO0202") {
                    throw new UserInputError("Unsupported file format.Supported file formats are jpg,jpeg,png,pdf,tiff,tif")
                } else if (err.message == "Invalid document category") {
                    throw new UserInputError(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;