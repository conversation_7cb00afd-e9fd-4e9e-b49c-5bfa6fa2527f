//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { formIds } = require('../../common/appConstants');

//function to add / update skill details
module.exports.updateProfilePhoto = async (parent, args, context, info) => {
    console.log('Inside updateProfilePhoto function');
    let organizationDbConnection;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            return(
                organizationDbConnection(ehrTables.candidatePersonalInfo)
                .update({
                    'Photo_Path': args.photoPath
                })
                .where('Candidate_Id', args.candidateId)
                .then((data)=>{
                    if(data){
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Candidate Profile photo was updated successfully" };
                    }else{
                        throw 'IO0133'
                    }
                })
                .catch((catchError) => {
                    console.log('Error in updateProfilePhoto .catch() block', catchError);
                    let errResult = commonLib.func.getError(catchError, 'IO0133');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return error response
                    throw new ApolloError(errResult.message, errResult.code);
                })
            )

        } else {
            console.log('No rights to add / update the profile photo details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in updateProfilePhoto function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'IO0031');
        // return response
        throw new ApolloError(errResult.message, errResult.code);

    }
}