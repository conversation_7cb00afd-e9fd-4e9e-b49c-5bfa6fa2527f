// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
var dbConnection = require('../resolvers/dbConnection');
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
var ehrTables = require('../common/tableAlias').ehrTables;

module.exports.retrieveOnboardingSettings = async (parent, args, context, info) => {
    let organizationDbConnection;
    console.log("Inside retrieveOnboardingSettings function.")
    try {
        
        organizationDbConnection = knex(context.connection.OrganizationDb);
        
         const  onboardingSettingsResult =  await organizationDbConnection(ehrTables.onboardingSettings)
         .select('Integration_Type', 'Status', 'Allow_Deployment_Notification_OnFailure', 'Enable_Location_Auto_Prefill')
         .modify(function (queryBuilder) {
             if (args.integrationType) {
                queryBuilder.where('Integration_Type', args.integrationType);
             }
         })
         .first();
        //destroy the connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        // Push the details into JSON
        return { 
            errorCode: '',  
            status:  onboardingSettingsResult?.Status || '', 
            Allow_Deployment_Notification_OnFailure:  onboardingSettingsResult?.Allow_Deployment_Notification_OnFailure || 'No', 
            Integration_Type:  onboardingSettingsResult?.Integration_Type || '',
            Enable_Location_Auto_Prefill:  onboardingSettingsResult?.Enable_Location_Auto_Prefill || 'No', 
            message: 'Onboarded Settings retrieved successfully.'}
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveOnboardingSettings function main catch block.', e);
        let errResult = commonLib.func.getError(e, '_UH0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}