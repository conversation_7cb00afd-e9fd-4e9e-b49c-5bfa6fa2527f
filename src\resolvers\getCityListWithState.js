//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');

//fuction to list candidate document
let organizationDbConnection;
module.exports.getCityListWithState = async (parent, args, context, info) => {
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
            organizationDbConnection.select('city.City_Name', 'city.City_Id','country.Locale','state.State_Id','state.State_Name','country.Country_Name','country.Country_Code',organizationDbConnection.raw('CONCAT(city.City_Name, ", ", state.State_Name, ", ", country.Country_Name) AS cityStateDetails'))
            .from('city')
            .orderBy('City_Name', 'asc')
            .leftJoin('state', 'state.State_Id', 'city.State_Id')
            .leftJoin('country', 'country.Country_Code', 'state.Country_Code')
            .then((data) => {
                    if (data && data.length > 0) {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "city list has been fetched successfully.", cityDetails: data };
                    }
                    else {
                        throw 'CCH0016';
                    }
                })
                .catch((catchError) => {
                    console.log('Error in getCityListWithState .catch() block', catchError);
                   throw catchError;
                })
        )
       
    }
    
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getCityListWithState function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0017');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
