//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')

//function to add / update insurance details
module.exports.addUpdateInsuranceDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateInsuranceDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const fieldValidations = {
                policyNo: "IVE0381",
            }
            validationError = validateCandidateInputs(args, fieldValidations);
            //Validate Already Exists
            validationError = await validatePolicyNumber(organizationDbConnection, args, validationError)

            if (Object.keys(validationError).length == 0) {
                let insuranceData = {
                    Candidate_Id: args.candidateId,
                    InsuranceType_Id: args.insuranceTypeId,
                    Policy_No: args.policyNo,
                    Insurance_Type: args.insuranceType,
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (args.policyId) {
                                return (
                                    organizationDbConnection(ehrTables.candidateInsurance)
                                        .update(insuranceData)
                                        .transacting(trx)
                                        .where('Policy_Id', args.policyId)
                                        .then(async (updateData) => {
                                            if (updateData) {
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the insurance details', insuranceData)
                                                throw 'IO0128'
                                            }
                                        })
                                )
                            } else {
                                return (
                                    organizationDbConnection(ehrTables.candidateInsurance)
                                        .insert(insuranceData)
                                        .transacting(trx)
                                        .then(async (insertData) => {
                                            if (insertData) {
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the insurance details', insuranceData)
                                                throw 'IO0128'
                                            }
                                        })
                                )
                            }

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The candidate insurance details has been ${args.policyId ? 'updated' : 'added'}.`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);

                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Candidate Insurance details has been added/updated successfully." };
                            } else {
                                throw 'IO0128'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateInsuranceDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0128');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )

            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the insurance details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateInsuranceDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateInsuranceDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0025');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

async function validatePolicyNumber(organizationDbConnection, args, validationError) {
    try {
        return (
            organizationDbConnection(ehrTables.empInsurance + " as EI")
                .select("EJ.User_Defined_EmpId")
                .leftJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "EI.Employee_Id")
                .where('EI.Policy_No', args.policyNo)
                .where('EI.InsuranceType_Id', args.insuranceTypeId)
                .where('EJ.Emp_Status', 'Active')
                .then((data) => {
                    if (data && data.length) {
                        let employeeIds = data.map((el) => el.User_Defined_EmpId)
                        validationError['IVE0427'] = commonLib.func.getError('', 'IVE0427').message + ' ' + employeeIds.join(',');
                    }
                    return validationError
                })
                .catch((err) => {
                    console.log('Error in validatePolicyNumber .catch', err)
                    throw err
                })
        )
    } catch (err) {
        console.log('Error in validatePolicyNumber main catch', err)
        throw err
    }
}