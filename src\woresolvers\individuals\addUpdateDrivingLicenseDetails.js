//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
//Require constants
const { formName, systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const {validateCandidateInputs} = require('../../common/commonFunctions')

//function to add driving license details
module.exports.addUpdateDrivingLicenseDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateDrivingLicenseDetails function');
    let organizationDbConnection;
    let validationError = {};
    let isUpdate = 0;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const fieldValidations = {
                drivingLicenseNo: "IVE0329",
                issuingAuthority: "IVE0330",
                issuingState: "IVE0331",
                vehicleType: "IVE0332",
            }
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let drivingLicenseData = {
                    Candidate_Id: args.candidateId,
                    Driving_License_No: args.drivingLicenseNo,
                    License_Issue_Date: args.licenseIssueDate,
                    License_Expiry_Date: args.licenseExpiryDate,
                    Issuing_Authority: args.issuingAuthority,
                    Issuing_Country: args.issuingCountry,
                    Issuing_State: args.issuingState,
                    Vehicle_Type: args.vehicleType,
                    File_Name: args.fileName
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            return (
                                organizationDbConnection(ehrTables.candidateDrivingLicense)
                                    .select('Candidate_Id')
                                    .where('Candidate_Id', drivingLicenseData.Candidate_Id)
                                    .transacting(trx)
                                    .then((data) => {
                                        if (data && data.length) {
                                            isUpdate = 1
                                            return (
                                                organizationDbConnection(ehrTables.candidateDrivingLicense)
                                                    .update(drivingLicenseData)
                                                    .where('Candidate_Id', drivingLicenseData.Candidate_Id)
                                                    .transacting(trx)
                                                    .then((updateDrivingLicense) => {
                                                        if (updateDrivingLicense) {
                                                            return 'success'
                                                        } else {
                                                            console.log('Error while updating the driving license details', drivingLicenseData)
                                                            throw 'IO0104'
                                                        }
                                                    })
                                            )
                                        } else {
                                            return (
                                                organizationDbConnection(ehrTables.candidateDrivingLicense)
                                                    .insert(drivingLicenseData)
                                                    .transacting(trx)
                                                    .then((insertDrivingLicense) => {
                                                        if (insertDrivingLicense) {
                                                            return 'success'
                                                        } else {
                                                            console.log('Error while inserting the driving license details', drivingLicenseData)
                                                            throw 'IO0104'
                                                        }
                                                    })
                                            )
                                        }
                                    })
                            )

                        })
                        .then(async(response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The candidate license details has been ${isUpdate ? 'updated': 'added'}`
                                };
    
                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);

                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Driving license details has been added/updated successfully" };
                            } else {
                                throw 'IO0104'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateDrivingLicenseDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0104');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )

            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the driving license details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateDrivingLicenseDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateDrivingLicenseDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0005');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}