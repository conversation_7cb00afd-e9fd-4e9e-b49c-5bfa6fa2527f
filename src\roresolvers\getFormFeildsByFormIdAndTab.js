//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');


//fuction to list holidays based on location
let organizationDbConnection;
module.exports.getFormFeildsByFormIdAndTab = async (parent, args, context, info) => {
    console.log('Inside getFormFeildsByFormIdAndTab function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
            return (
                await organizationDbConnection(ehrTables.fields + " as FLD")
                    .select('FLD.Field_Id', 'FLD.Field_Name','FLD.Tab_Name','FLD.Form_Id', 'CF.Field_Alias', 'CF.Field_Visiblity', 'CF.Mandatory_Field' ,'CF.Predefined')
                    .innerJoin(ehrTables.customizationFields + " as CF", "FLD.Field_Id", "CF.Field_Id")
                    .where(function () {
                        if (args.form_Id) {
                            this.where('FLD.Form_Id', args.form_Id)
                        }
                    })
                    .then(async (data) => {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Field data has been fetched successfully.", formFields: data };
                    })
                    .catch((catchError) => {
                        console.log('Error in getFormFeildsByFormIdAndTab .catch() block', catchError);
                        let errResult = commonLib.func.getError(catchError, 'PF0101');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getFormFeildsByFormIdAndTab function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'PF0001');
        throw new ApolloError(errResult.message, errResult.code)

    }
}