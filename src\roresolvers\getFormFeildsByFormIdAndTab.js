//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');


//fuction to list holidays based on location
let organizationDbConnection;
module.exports.getFormFeildsByFormIdAndTab = async (parent, args, context, info) => {
    console.log('Inside getFormFeildsByFormIdAndTab function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
            return (
                await organizationDbConnection(ehrTables.fields + " as FLD")
                    .select('FLD.Field_Id', 'FLD.Field_Name','FLD.Tab_Name','FLD.Form_Id', 'CF.Field_Alias', 'CF.Field_Visiblity', 'CF.Mandatory_Field' ,'CF.Predefined', 'CF.Min_Validation','CF.Validation_Type','CF.Max_Validation', 'CF.Validation_Id', 'V.Validation_Name', 'V.Regular_Expression', 'V.Description')
                    .innerJoin(ehrTables.customizationFields + " as CF", "FLD.Field_Id", "CF.Field_Id")
                    .leftJoin(ehrTables.validation + " as V", "CF.Validation_Id", "V.Validation_Id")
                    .where(function () {
                        if (args.form_Id) {
                            this.where('FLD.Form_Id', args.form_Id)
                        }
                    })
                    .then(async (data) => {
                        // Transform data to include nested validation details
                        const formattedData = data.map(field => ({
                            Field_Id: field.Field_Id,
                            Form_Id: field.Form_Id,
                            Field_Name: field.Field_Name,
                            Field_Alias: field.Field_Alias,
                            Field_Visiblity: field.Field_Visiblity,
                            Mandatory_Field: field.Mandatory_Field,
                            Predefined: field.Predefined,
                            Tab_Name: field.Tab_Name,
                            Min_Validation: field.Min_Validation,
                            Max_Validation: field.Max_Validation,
                            Validation_Id: field.Validation_Id,
                            validationDetails: field.Validation_Id ? {
                                Validation_Id: field.Validation_Id,
                                Validation_Name: field.Validation_Name,
                                Regular_Expression: field.Regular_Expression,
                                Description: field.Description
                            } : null
                        }));

                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Field data has been fetched successfully.", formFields: formattedData };
                    })
                    .catch((catchError) => {
                        console.log('Error in getFormFeildsByFormIdAndTab .catch() block', catchError);
                        let errResult = commonLib.func.getError(catchError, 'PF0101');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getFormFeildsByFormIdAndTab function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'PF0001');
        throw new ApolloError(errResult.message, errResult.code)

    }
}