const resolvers = {
    Mutation : {
        deleteDependent : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');
            var dateFormat = require('dateformat');

            var Candidate_Id;
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        Candidate_Id = url[0].Candidate_Id;
                        return knexconfig('candidate_dependent')
                        .where('Dependent_Id',args.Dependent_Id)
                        .andWhere('Candidate_Id',Candidate_Id)
                        .then((dependent)=>{
                            if(!dependent[0]){
                                throw new Error("Dependent not found");
                            } else {
                                return knexconfig('candidate_dependent')
                                .where('Dependent_Id',args.Dependent_Id)
                                .del()
                                .transacting(trc)
                                .then(()=>{
                                    return {
                                        message:"Dependent details deleted"
                                    }
                                })
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return knexconfig('candidate_dependent')
                .where('Candidate_Id',Candidate_Id)
                .then((dependent)=>{
                    if(!dependent[0]){
                        return {
                            dependent: null
                        }
                    } else {
                        var newDependent = dependent.map((arr)=>{ 
                            arr['Dependent_DOB']  = dateFormat(new Date(arr['Dependent_DOB']), "yyyy-mm-dd");
                            return arr;
                        });
                        return {
                            dependent: newDependent
                        }
                    }
                })
            }).catch(function(err){
                console.log('Error in deleteDependent',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Dependent not found"){
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;