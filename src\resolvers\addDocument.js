const resolvers = {
    Mutation : {
        addDocument : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        Candidate_Id = url[0].Candidate_Id;
                        return knexconfig("candidate_document_category")
                        .insert({
                            Candidate_Id: Candidate_Id,
                            Category_Id: args.Category_Id,
                            Document_Type_Id: args.Document_Type_Id,
                            Sub_Document_Type_Id: args.Sub_Document_Type_Id,
                            Document_Name: args.Document_Name
                        })
                        .transacting(trc)
                        .then((documentId)=>{
                            return knexconfig("candidate_documents")
                            .insert({
                                Document_Id: documentId,
                                File_Name: args.File_Name,
                                File_Size: ''
                            })
                            .transacting(trc)
                            .then(()=>{
                                return {
                                    message:"Document added"
                                }
                            })
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return knexconfig.select('candidate_document_category.Document_Id',
                    'candidate_document_category.Candidate_Id',
                    'candidate_document_category.Document_Name',
                    'document_category.Category_Id',
                    'document_category.Category_Fields as Category_Name',
                    'document_type.Document_Type_Id',
                    'document_type.Document_Type',
                    'document_sub_type.Document_Sub_Type_Id',
                    'document_sub_type.Document_Sub_Type',
                    'candidate_documents.File_Name'
                )
                .from('candidate_document_category')
                .leftJoin('document_category','candidate_document_category.Category_Id','document_category.Category_Id')
                .leftJoin('document_type','candidate_document_category.Document_Type_Id','document_type.Document_Type_Id')
                .leftJoin('document_sub_type','candidate_document_category.Sub_Document_Type_Id','document_sub_type.Document_Sub_Type_Id')
                .leftJoin('candidate_documents','candidate_document_category.Document_Id','candidate_documents.Document_Id')
                .where('candidate_document_category.Candidate_Id',Candidate_Id)
                .then((documents)=>{
                    if(!documents[0]){
                        return {
                            documents : null
                        }
                    } else {
                        return {
                            documents : documents
                        }
                    }
                })
            }).catch(function(err){
                console.log('Error in addDocument',err);
                if (err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;