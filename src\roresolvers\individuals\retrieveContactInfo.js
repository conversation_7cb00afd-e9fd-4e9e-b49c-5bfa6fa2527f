// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appConstants');

module.exports.retrieveContactInfo = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveContactInfo function.");
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
            organizationDbConnection(ehrTables.candidateContactDetails + " as CD")
                .select("CD.*", "LO.*", "CI.City_Name", "ST.State_Name", "CO1.Country_Name as Country_Name", "CO2.Country_Name as pCountry_Name","CO3.Country_Name as cCountry_Name", "CO4.Country_Name as oCountry_Name",
                        "pCity.City_Name as pCity_Id_Name", "cCity.City_Name as cCity_Id_Name", "oCity.City_Name as oCity_Id_Name",
                        'BG.Barangay_Name','BG.Barangay_Id',
                        "pBG.Barangay_Name as pBarangay_Id_Name", "cBG.Barangay_Name as cBarangay_Id_Name", "oBG.Barangay_Name as oBarangay_Id_Name")
                .leftJoin(ehrTables.candidateJob + " as EJ", "EJ.Candidate_Id", "CD.Candidate_Id")
                .leftJoin(ehrTables.location + " as LO", "LO.Location_Id", "EJ.Location_Id")
                .leftJoin(ehrTables.city + " as CI", "CI.City_Id", "LO.City_Id")
                .leftJoin(ehrTables.barangay + " as BG", "BG.Barangay_Id", "LO.Barangay_Id")
                .leftJoin(ehrTables.state + " as ST", "ST.State_Id", "LO.State_Id")
                .leftJoin(ehrTables.country + " as CO1", "CO1.Country_Code", "LO.Country_Code")
                .leftJoin(ehrTables.country + " as CO2", "CO2.Country_Code", "CD.pCountry")
                .leftJoin(ehrTables.country + " as CO3", "CO3.Country_Code", "CD.cCountry")
                .leftJoin(ehrTables.country + " as CO4", "CO4.Country_Code", "CD.oCountry")
                .leftJoin(ehrTables.city + " as pCity", "pCity.City_Id", "CD.pCity_Id")
                .leftJoin(ehrTables.city + " as cCity", "cCity.City_Id", "CD.cCity_Id")
                .leftJoin(ehrTables.city + " as oCity", "oCity.City_Id", "CD.oCity_Id")
                .leftJoin(ehrTables.barangay + " as pBG", "pBG.Barangay_Id", "CD.pBarangay_Id")
                .leftJoin(ehrTables.barangay + " as cBG", "cBG.Barangay_Id", "CD.cBarangay_Id")
                .leftJoin(ehrTables.barangay + " as oBG", "oBG.Barangay_Id", "CD.oBarangay_Id")
                .where('CD.Candidate_Id', args.candidateId)
                .then(async(contactDetails) => {
                    if (contactDetails) {
                        if(!contactDetails.length){
                            contactDetails = await getLocationDetails(organizationDbConnection, args.candidateId)
                            if(!contactDetails){
                                throw 'IO0112'
                            }
                        }
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Candidate contact details retrieved successfully", contactDetails: JSON.stringify(contactDetails) }
                    } else {
                        throw 'IO0112'
                    }
                })
                .catch((err)=>{
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    console.log('Error in retrieveContactInfo function .catch block.', err);
                    const errResult = commonLib.func.getError(err, 'IO0112');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveContactInfo function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'IO0012');
        throw new ApolloError(errResult.message, errResult.code);
    }
};

async function getLocationDetails(organizationDbConnection, candidateId){
    try{
        return(
            organizationDbConnection(ehrTables.candidateJob + " as EJ")
            .select( "CD.*", "EJ.Candidate_Id", "LO.*", "EJ.Location_Id as locationId", "CI.City_Name", "ST.State_Name", "CO1.Country_Name as Country_Name", "CO2.Country_Name as pCountry_Name","CO3.Country_Name as cCountry_Name", "CO4.Country_Name as oCountry_Name",
                    "pCity.City_Name as pCity_Id_Name", "cCity.City_Name as cCity_Id_Name", "oCity.City_Name as oCity_Id_Name",
                    "pBarangay.Barangay_Name as pBarangay_Id_Name", "cBarangay.Barangay_Name as cBarangay_Id_Name", "oBarangay.Barangay_Name as oBarangay_Id_Name")
            .leftJoin(ehrTables.candidateContactDetails + " as CD", "CD.Candidate_Id", "EJ.Candidate_Id")
            .leftJoin(ehrTables.location + " as LO", "LO.Location_Id", "EJ.Location_Id")
            .leftJoin(ehrTables.city + " as CI", "CI.City_Id", "LO.City_Id")
            .leftJoin(ehrTables.state + " as ST", "ST.State_Id", "LO.State_Id")
            .leftJoin(ehrTables.country + " as CO1", "CO1.Country_Code", "LO.Country_Code")
            .leftJoin(ehrTables.country + " as CO2", "CO2.Country_Code", "CD.pCountry")
            .leftJoin(ehrTables.country + " as CO3", "CO3.Country_Code", "CD.cCountry")
            .leftJoin(ehrTables.country + " as CO4", "CO4.Country_Code", "CD.oCountry")
            .leftJoin(ehrTables.city + " as pCity", "pCity.City_Id", "CD.pCity_Id")
            .leftJoin(ehrTables.city + " as cCity", "cCity.City_Id", "CD.cCity_Id")
            .leftJoin(ehrTables.city + " as oCity", "oCity.City_Id", "CD.oCity_Id")
            .leftJoin("barangay as pBarangay", "pBarangay.Barangay_Id", "CD.pBarangay_Id")
            .leftJoin("barangay as cBarangay", "cBarangay.Barangay_Id", "CD.cBarangay_Id")
            .leftJoin("barangay as oBarangay", "oBarangay.Barangay_Id", "CD.oBarangay_Id")
            .where("EJ.Candidate_Id", candidateId)
            .then((data)=>{
                return data
            })
            .catch((err)=>{
                console.log('Error while getting location details', err)
                throw err
            })
        )
    }catch(err){
        console.log('Error while getting the location details',err)
        throw err
    }
}
