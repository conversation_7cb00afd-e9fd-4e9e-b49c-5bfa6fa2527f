// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

async function validateCommonSuperannuationInput(args,validationError)
{
    try{
        if(!args.taxFileNumber || args.taxFileNumber.length<9 || args.taxFileNumber.length>15)
        {
            validationError['IVE0237']=commonLib.func.getError('', 'IVE0237').message;
        }
        if(args.superannuationType != 'Super Fund' && args.superannuationType != 'SMSF' && args.superannuationType != 'Employer Nominated')
        {
            validationError['IVE0238']=commonLib.func.getError('', 'IVE0238').message;
        }
        return validationError;
    }
    catch(e)
    {
        console.log('Error in validateCommonSuperannuationInput() main catch block',e);
        throw(e);
    }
}

async function validateSuperannuationSuperFundAndSMSFInput(args,validationError)
{
    try{
        if(!args.fundABN)
        {
            validationError['IVE0239']=commonLib.func.getError('', 'IVE0239').message;
        }
        if(!args.fundName)
        {
            validationError['IVE0240']=commonLib.func.getError('', 'IVE0240').message;
        }
        if(!args.fundAddress)
        {
            validationError['IVE0241']=commonLib.func.getError('', 'IVE0241').message;
        }
        if(!args.suburbOrTown)
        {
            validationError['IVE0242']=commonLib.func.getError('', 'IVE0242').message;
        }
        if(!args.stateOrTerritory)
        {
            validationError['IVE0243']=commonLib.func.getError('', 'IVE0243').message;
        }
        if(!args.postCode)
        {
            validationError['IVE0244']=commonLib.func.getError('', 'IVE0244').message;
        }
        if(!args.fundPhone)
        {
            validationError['IVE0245']=commonLib.func.getError('', 'IVE0245').message;
        }
        return validationError;
    }
    catch(e)
    {
        console.log('Error in validateSuperannuationSuperFundAndSMSFInput() main catch block',e);
        throw(e);
    }
}

async function validateSuperFundInput(args,validationError)
{
    try{
        if(!args.uniqueSuperannuationIdentifier)
        {
            validationError['IVE0246']=commonLib.func.getError('', 'IVE0246').message;
        }
        return validationError;
    }
    catch(e)
    {
        console.log('Error in validateSuperFundInput() main catch block.',e);
        throw(e);
    }
}

async function validateSMSFInput(args,validationError)
{
    try{
        if(!args.fundElectronicServiceAddress)
        {
            validationError['IVE0247']=commonLib.func.getError('', 'IVE0247').message;
        }
        return validationError;
    }
    catch(e)
    {
        console.log('Error in validateSMSFInput() main catch block.',e);
        throw(e);
    }
}

async function updatePerformanceRatingsValidation(args)
{
    let validationError = {}
    try{
        if (!args.vendorId) {
            validationError['IVE0262'] = commonLib.func.getError('', 'IVE0262').message;
        }
        
        if(!args.performanceRatings){
            validationError['IVE0261'] = commonLib.func.getError('', 'IVE0261').message;
        }

        return validationError;
    }
    catch(e)
    {
        console.log('Error in updatePerformanceRatingsValidation() main catch block.',e);
        throw(e);
    }
}

async function updateVendorStatusLevelValidation(updateData)
{
    let validationError = {}
    try{

        for (let i = 0; i < updateData.length; i++) {
            if (!updateData[i].vendorId) {
                validationError['IVE0262'] = commonLib.func.getError('', 'IVE0262').message;
            }
        }

        return validationError;
    }
    catch(e)
    {
        console.log('Error in updateVendorStatusValidation() main catch block.',e);
        throw(e);
    }
}

module.exports={
    validateCommonSuperannuationInput,
    validateSuperannuationSuperFundAndSMSFInput,
    validateSuperFundInput,
    validateSMSFInput,
    updatePerformanceRatingsValidation,
    updateVendorStatusLevelValidation
}