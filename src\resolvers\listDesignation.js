const resolvers = {
    Query : {
        listDesignation : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig("designation")
                .where('Designation_Status','Active')
                .then((designation)=>{
                    if(!designation[0]){
                        return {
                            designation: null
                        }
                    } else{
                        return {
                            designation: designation
                        }
                    }
                })
                .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listDesignation')
                return result;
            }).catch(function(err){
                console.log('Error in listDesignation',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;