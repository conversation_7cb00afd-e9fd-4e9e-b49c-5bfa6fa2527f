const resolvers = {
    Query : {
        listWorkSchedule : async (root,args) =>{

            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);

            return knexconfig.transaction(function(trc){
                return knexconfig("work_schedule")
                .where('WorkSchedule_Status','Active')
                .then((schedule)=>{
                    if(!schedule[0]){
                        return {
                            work_schedule:null
                        }
                    } else{
                        return {
                            work_schedule: schedule
                        }
                    }
                })
                .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listWorkSchedule')
                return result;
            }).catch(function(err){
                console.log('Error in listWorkSchedule',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;