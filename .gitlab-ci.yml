# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/#customizing-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence
image: node:18
stages:
  - Code_Quality
  - SAST
  - develop
  - release
  - production

sast:
  stage: SAST
  variables:
    SAST_EXCLUDED_PATHS: spec, test, tests, tmp, dist

code_quality:
  stage: Code_Quality

code_quality_html:
  extends: code_quality
  variables:
    REPORT_FORMAT: html
  artifacts:
    paths: [gl-code-quality-report.html]

include:
  - template: Code-Quality.gitlab-ci.yml
  - template: Security/SAST.gitlab-ci.yml

deploy to develop:
  only:
    - develop
  stage: develop
  script:
    - npm install -g serverless@3.38.0
    - npm install -g knex
    - npm install
    - serverless deploy --stage dev --region ap-south-1 --verbose
  environment: dev
deploy to release:
  only:
    - release
  when: manual
  stage: release
  script:
    - npm install -g serverless@3.38.0
    - npm install -g knex
    - npm install
    - serverless deploy --stage release --region ap-south-1 --verbose
  environment: release
deploy to production:
  only:
    - master
  stage: production
  script:
    - npm install -g serverless@3.38.0
    - npm install -g knex
    - npm install
    - serverless deploy --stage prod --region ap-south-1 --verbose
  environment: prod
deploy to cannyhr:
  only:
    - master
  stage: production
  script:
    - npm install -g serverless@3.38.0
    - npm install -g knex
    - npm install
    - serverless deploy --stage cannyhr --region ap-south-1 --verbose
  environment: cannyhr
