const resolvers = {
    Query : {
        pinAuth : async (root,args)=>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .select('CU.Pin as Pin','CU.Expire_Time as Expire_Time','CU.Status as Status','CU.Service_Provider_Id','L.Country_Code as Country_Code')
                .from('candidate_url as CU')
                .leftJoin('location as L','L.Location_Id','CU.Location_Id')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    console.log(url)
                    if(!url[0]){
                        throw new Error("REO0101")
                    } else if(url[0].Pin != args.Pin) {
                        throw new Error("REO0107")
                    } else if(url[0].Expire_Time < Date.now() ){
                        throw new Error("REO0102")
                    } else if((url[0].Status).toLowerCase() == "finished" ){
                        throw new Error("REO0103")
                    } else if((url[0].Status).toLowerCase() == "confirmed" ){
                        throw new Error("REO0104")
                    } else if((url[0].Status).toLowerCase() == "rejected" ){
                        throw new Error("REO0105")
                    } else if((url[0].Status).toLowerCase() == "revoked" ){
                        throw new Error("REO0106")
                    } else {
                        return {
                            message:"Pin Verified",
                            countryCode:url[0]['Country_Code']?url[0]['Country_Code']:null,
                            serviceProviderId:url[0]['Service_Provider_Id']?url[0]['Service_Provider_Id']:null
                        }
                    }
                })
                .then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from pinAuth');
                return result;
            }).catch(function(err){
                console.log('Error in pinAuth',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "REO0107"){
                    console.log("Pin doesn't match")
                    throw new ApolloError("Pin doesn't match","REO0107")
                } else if (err.message == "REO0102"){
                    console.log("URL Expired")
                    throw new ApolloError("URL Expired","REO0102")
                } else if (err.message == "REO0103"){
                    console.log("You already submitted the form")
                    throw new ApolloError("You already submitted the form","REO0103")
                } else if (err.message == "REO0104"){
                    console.log("You have already confirmed as employee")
                    throw new ApolloError("You have already confirmed as employee","REO0104")
                } else if (err.message == "REO0105"){
                    console.log("Rejected URL")
                    throw new ApolloError("Request denied","REO0105")
                } else if (err.message == "REO0106"){
                    console.log("URL Disabled")
                    throw new ApolloError("URL Disabled","REO0106")
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}

exports.resolvers = resolvers;

