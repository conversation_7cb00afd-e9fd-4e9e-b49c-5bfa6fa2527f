const resolvers = {
    Query : {
        listLocation : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig("location")
                .where('Location_Status','Active')
                .then((location)=>{
                    if(!location[0]){
                        return {
                            location: null
                        }
                    } else{
                        return {
                            location: location
                        }
                    }
                })
                .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listLocation')
                return result;
            }).catch(function(err){
                console.log('Error in listLocation',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;