scalar Date
type Query {
  listGeneratedUrl(
    Org_Code: String!
    Employee_Id: Int!
    Filter_Manager_Id: Int
    Filter_Date_Of_Join_From: String
    Filter_Date_Of_Join_To: String
    Filter_Url_Validity_From: String
    Filter_Url_Validity_To: String
    Filter_Designation: Int
    Filter_Department: Int
    Filter_Location: Int
    Filter_EmployeeType: Int
    Filter_ServiceProvider: Int
    Filter_EmployeeStatus: String
  ): AllUrl
  pinAuth(Org_Code: String!, Url_Hash: String!, Pin: Int!): pinAuthResponse
  checkUrlStatus(Org_Code: String!, Url_Hash: String!): GeneralMessage
  listLanguage(Org_Code: String!, Url_Hash: String!): Languages
  listDesignation(Org_Code: String!): AllDesignation
  listDepartment(Org_Code: String!): AllDepartment
  listWorkSchedule(Org_Code: String!): AllWorkSchedule
  listLocation(Org_Code: String!): AllLocation
  listManager(Org_Code: String!): AllManagers
  listEmployeeType(Org_Code: String!): AllEmployeeType
  listMartialStatus(Org_Code: String!, Url_Hash: String!): AllMartialStatus
  listMartialStatusRelation(
    Org_Code: String!
    Url_Hash: String!
  ): AllMartialStatusRelation
  listCountry(Org_Code: String!, Url_Hash: String!): AllCountry
  listValidityType(Org_Code: String!): AllValidityType
  listCourseDetails(Org_Code: String!, Url_Hash: String!): AllCourseDetails
  listAccountType(Org_Code: String!, Url_Hash: String!, Vendor_Based: Int): AllAccountType
  listDocumentCategory(
    Org_Code: String!
    Url_Hash: String!
    Vendor_Based: Int
  ): AllDocumentCategory
  listDocumentType(
    Org_Code: String!
    Url_Hash: String!
    Vendor_Based: Int
    Category_Id: Int!
  ): AllDocumentType
  listDocumentSubType(
    Org_Code: String!
    Url_Hash: String!
    Vendor_Based: Int
    type_id: Int!
    isDefault: Boolean
  ): AllDocumentSubType
  listBankName(Org_Code: String!, Url_Hash: String!, Vendor_Based: Int): AllBankName
  getPersonalInfo(Org_Code: String!, Url_Hash: String!): PersonalInfo
  getCertification(Org_Code: String!, Url_Hash: String!): AllCertification
  getExperience(Org_Code: String!, Url_Hash: String!): getExperienceResponse
  getDependent(Org_Code: String!, Url_Hash: String!): AllDependent
  getEducation(Org_Code: String!, Url_Hash: String!): AllEducation
  getTraining(Org_Code: String!, Url_Hash: String!): AllTraining
  getBankDetails(Org_Code: String!, Url_Hash: String!): BankDetails
  sendUrlToCandidate(
    Org_Code: String!
    Url_Hash: String!
    Name: String!
    MailTo: String!
  ): GeneralMessage
  fileUpload(
    Org_Code: String!
    Url_Hash: String!
    File_Name: String!
    File_Content: String!
    Category: String!
  ): FileUploadMessage
  fileRetrieve(
    Org_Code: String!
    Url_Hash: String!
    File_Name: String!
    Category: String!
  ): FileRetrieveMessage
  fileDelete(
    Org_Code: String!
    Url_Hash: String!
    File_Name: String!
    Category: String!
  ): GeneralMessage
  fileReplace(
    Org_Code: String!
    Url_Hash: String!
    File_Name: String
    New_File_Name: String
    New_File_Content: String
    Category: String
  ): FileReplaceMessage
  getAllDocuments(Org_Code: String!, Url_Hash: String!): AllDocuments

  fileUploadHr(
    Org_Code: String!
    Candidate_Id: Int!
    File_Name: String!
    File_Content: String!
    Category: String!
  ): FileUploadMessage
  fileRetrieveHr(
    Org_Code: String!
    Candidate_Id: Int!
    File_Name: String!
    Category: String!
  ): FileRetrieveMessage
  fileDeleteHr(
    Org_Code: String!
    Candidate_Id: Int!
    File_Name: String!
    Category: String!
  ): GeneralMessage
  fileReplaceHr(
    Org_Code: String!
    Candidate_Id: Int!
    File_Name: String
    New_File_Name: String
    New_File_Content: String
    Category: String
  ): FileReplaceMessage
  retrieveNationalityList(Org_Code: String!):retrieveNationalityListResponse
  retrieveGenderList(Org_Code: String!):retrieveGenderListResponse
  retrieveReligionList(Org_Code: String!):retrieveReligionListResponse
  retrieveListEduInstitutionAndSpecialization(Org_Code: String!): listEduInstitutionAndSpecializationResponse
  getCompanyPartner(Org_Code: String!): retrieveCompanyDetailResponse
}

type Mutation {
  generateUrl(
    Org_Code: String!
    Expire_Value: Int!
    Expire_Type: Int!
    Department_Id: Int!
    Designation_Id: Int!
    Location_Id: Int!
    Date_Of_Join: String!
    Job_Code: String!
    Probation_Date: String
    EmpType_Id: Int!
    Manager_Id: Int!
    Work_Schedule: Int!
    Employee_Id: Int!
    Service_Provider_Id: Int!
    Business_Unit_Id: Int!
  ): Url

  migrateDocuments(
    Org_Code: String!
    Candidate_Id: Int!
    Employee_Id: Int!
  ): GeneralMessage

  updatePersonalInfo(
    Org_Code: String!
    Url_Hash: String!
    Salutation: String
    Emp_First_Name: String
    Emp_Middle_Name: String
    Emp_Last_Name: String
    Emp_Pref_First_Name: String
    Gender: String
    Gender_Id: Int
    Gender_Expression_Id: Int
    Gender_Identity_Id: Int
    DOB: String
    Place_Of_Birth: String
    Marital_Status: Int
    Blood_Group: String
    Lang_Known: [Candidate_Language]
    hobbies: String
    Nationality: String
    Nationality_Id: Int
    Religion: String
    Religion_Id: Int
    Appellation: String
    Caste: String
    Is_Manager: Boolean
    Photo_Path: String
    Personal_Email: String
    Physically_Challenged: Boolean
    Smoker: Boolean
    Smokerasof: String
    PAN: String
    Aadhaar_Card_Number: String
    Military_Service: Boolean
    No_Dependent: Boolean
    Relative_In_Company: Boolean
    Driving_License_No: String
    License_Issue_Date: String
    License_Expiry_Date: String
    Issuing_Authority: String
    Issuing_Country: String
    Issuing_State: String
    Vehicle_Type: String
    License_File_Name: String
    Passport_No: String
    Issue_Date: String
    Passport_Issuing_Country: String
    Passport_Issuing_Authority: String
    Expiry_Date: String
    Passport_File_Name: String
    pApartment_Name: String
    pStreet_Name: String
    pCity: String
    pCity_Id: Int
    pState: String
    pCountry: String
    pPincode: String
    cApartment_Name: String
    cStreet_Name: String
    cCity: String
    cCity_Id: Int
    cState: String
    cCountry: String
    cPincode: String
    oApartment_Name: String
    oStreet_Name: String
    oCity: String
    oCity_Id: Int
    oState: String
    oCountry: String
    oPincode: String
    pBarangay: String
    pBarangay_Id: Int
    pRegion: String
    cBarangay: String
    cBarangay_Id: Int
    cRegion: String
    oBarangay: String
    oBarangay_Id: Int
    oRegion: String
    Use_Location_Address: Int
    Land_Line_No: String
    Mobile_No: String
    Mobile_No_Country_Code: String
    Emergency_Contact_No: String
    Emergency_Contact_Name:String
    Emergency_Contact_Relation: String
    Section1_Progress: Boolean
    Section2_Progress: Boolean
    Section3_Progress: Boolean
    Section4_Progress: Boolean
    Ethnic_Race: String
    Gender_Orientations: String
    Pronoun: String
    Statutory_Insurance_Number: String
    PRAN_No: String
    UAN: String
    Tax_Code: String
  ): GeneralMessage

  updatePhotoPath(
    Org_Code: String!
    Url_Hash: String!
    Photo_Path: String!
  ): GeneralMessage!

  addExperience(
    Org_Code: String!
    Url_Hash: String!
    Prev_Company_Name: String!
    Prev_Company_Location: String!
    Designation: String!
    Start_Date: String
    End_Date: String
    File_Name: String
    Reference_Details: [Reference_Details]
  ): AllExperience

  updateExperience(
    Org_Code: String!
    Url_Hash: String!
    Experience_Id: Int!
    Prev_Company_Name: String!
    Prev_Company_Location: String!
    Designation: String!
    Start_Date: String
    End_Date: String
    File_Name: String
    Reference_Details: [Reference_Details]
  ): GeneralMessage

  deleteExperience(
    Experience_Id: Int!
    Org_Code: String!
    Url_Hash: String!
  ): AllExperience

  addDependent(
    Org_Code: String!
    Url_Hash: String!
    Dependent_First_Name: String!
    Dependent_Last_Name: String!
    Gender: String
    Gender_Id: Int
    Relationship: String!
    Dependent_DOB: String!
  ): AllDependent

  updateDependent(
    Org_Code: String!
    Url_Hash: String!
    Dependent_Id: Int!
    Dependent_First_Name: String!
    Dependent_Last_Name: String!
    Gender: String!
    Gender_Id: Int
    Relationship: String!
    Dependent_DOB: String!
  ): GeneralMessage

  deleteDependent(
    Dependent_Id: Int!
    Org_Code: String!
    Url_Hash: String!
  ): AllDependent

  addCandidateRelation(
    Org_Code: String!
    Url_Hash: String!
    Is_Update: Int!
    relationDetails: [candidateRelation]
  ): AllCandidateRelation

  deleteCandidateRelation(
    Relation_Id: Int!
    Org_Code: String!
    Url_Hash: String!
  ): AllCandidateRelation

  addCertification(
    Org_Code: String!
    Url_Hash: String!
    Certification_Name: String!
    Received_Date: String!
    Certificate_Received_From: String!
    File_Name: String
    Ranking: String
    Document_Name: String
    Sub_Type_Id: Int
  ): AllCertification

  updateCertification(
    Org_Code: String!
    Url_Hash: String!
    Certification_Id: Int!
    Certification_Name: String!
    Received_Date: String!
    Certificate_Received_From: String!
    File_Hash: String
    File_Name: String
    Document_Name: String
    Ranking: String
    Sub_Type_Id: Int
  ): GeneralMessage

  deleteCertification(
    Org_Code: String!
    Url_Hash: String!
    Certification_Id: Int!
  ): AllCertification

  addBankDetails(
    Org_Code: String!
    Url_Hash: String!
    Bank_Account_Name: String
    Bank_Account_Number: String!
    Emp_Bank_Id: Int!
    Bank_Name: String!
    Branch_Name: String!
    IFSC_Code: String
    Street: String
    City: String
    State: String
    Zip: String
    Account_Type_Id: Int
    BSB_Code: String
    isAustralian: Int!
    File_Name: String
  ): GeneralMessage

  updateBankDetails(
    Org_Code: String!
    Url_Hash: String!
    Bank_Account_Name: String
    Bank_Account_Number: String!
    Emp_Bank_Id: Int!
    Bank_Name: String!
    Branch_Name: String!
    IFSC_Code: String
    Street: String
    City: String
    State: String
    Zip: String
    Account_Type_Id: Int
    BSB_Code: String
    isAustralian: Int!
    File_Name: String
  ): GeneralMessage

  addEducation(
    Org_Code: String!
    Url_Hash: String!
    Education_Type: Int
    Specialisation: String
    Institute_Name: String
    Specialization_Id: Int
    Institution_Id: Int
    University: String
    Year_Of_Start: Int
    Year_Of_Passing: Int
    Percentage: Float
    Grade: String
    File_Hash: String
    File_Name: String
    Document_Name: String
    Sub_Type_Id: Int
    Start_Date: Date
    End_Date: Date
    City: String
    State: String
    Country: String
  ): AllEducation

  updateEducation(
    Org_Code: String!
    Url_Hash: String!
    Education_Id: Int!
    Education_Type: Int
    Specialisation: String
    Institute_Name: String
    Specialization_Id: Int
    Institution_Id: Int
    University: String
    Year_Of_Start: Int
    Year_Of_Passing: Int
    Percentage: Float
    Grade: String
    File_Hash: String
    File_Name: String
    Document_Name: String
    Sub_Type_Id: Int
    Start_Date: Date
    End_Date: Date
    City: String
    State: String
    Country: String
  ): GeneralMessage

  deleteEducation(
    Education_Id: Int!
    Org_Code: String!
    Url_Hash: String!
  ): AllEducation

  addTraining(
    Org_Code: String!
    Url_Hash: String!
    Training_Name: String!
    Training_Start_Date: String
    Training_End_Date: String
    Trainer: String!
    Center: String!
    File_Name: String
    Document_Name: String
    Sub_Type_Id: Int
  ): AllTraining

  updateTraining(
    Org_Code: String!
    Url_Hash: String!
    Training_Id: Int!
    Training_Name: String!
    Training_Start_Date: String
    Training_End_Date: String
    Trainer: String!
    Center: String!
    File_Hash: String
    File_Name: String
    Document_Name: String
    Sub_Type_Id: Int
  ): GeneralMessage

  deleteTraining(
    Training_Id: Int!
    Org_Code: String!
    Url_Hash: String!
  ): AllTraining

  statusUpdate(
    Org_Code: String!
    Url_Hash: String!
    Status: String
  ): GeneralMessage

  regeneratePin(Org_Code: String!, Url_Hash: String, Employee_Id: Int!): Url

  addDocument(
    Org_Code: String!
    Url_Hash: String!
    File_Name: String!
    Document_Name: String
    Category_Id: Int!
    Document_Type_Id: Int!
    Sub_Document_Type_Id: Int
  ): AllDocuments

  updateDocument(
    Org_Code: String!
    Url_Hash: String!
    Document_Id: Int
    Document_Name: String
    File_Name: String
    Category_Id: Int
    Document_Type_Id: Int
    Sub_Document_Type_Id: Int
  ): GeneralMessage

  addDocumentSubType(
    Org_Code: String!
    Document_Type_Id: Int!
    Sub_Document_Type_Name: String!
    Employee_Id: Int!
  ): GeneralMessage

  deleteDocument(
    Org_Code: String!
    Url_Hash: String!
    Document_Id: Int!
  ): GeneralMessage

  updateEmployeePreviousExperience(
    Url_Hash: String!
    Org_Code: String!
    Previous_Experience_Month: Int!
    Previous_Experience_Year: Int!
  ): GeneralMessage
}

type FileUploadMessage {
  fileName: String
}

type FileRetrieveMessage {
  fileContent: String
}

type FileReplaceMessage {
  fileName: String
}

type PersonalInfo {
  Candidate_Id : Int
  Salutation: String
  Emp_First_Name: String
  Emp_Middle_Name: String
  Emp_Last_Name: String
  Emp_Pref_First_Name: String
  Gender: String
  Gender_Expression_Id: Int
  Gender_Expression: String
  Gender_Identity_Id: Int
  Gender_Identity: String
  Gender_Id: Int
  DOB: Date
  Place_Of_Birth: String
  Photo_Path: String
  Marital_Status: Int
  Blood_Group: String
  Lang_Known: [Candidate_Languages]
  hobbies: String
  Nationality: String
  Religion: String
  Nationality_Id: Int
  Religion_Id: Int
  Appellation: String
  Caste: String
  Is_Manager: Boolean
  Personal_Email: String
  Physically_Challenged: Boolean
  Smoker: Boolean
  Smokerasof: Date
  PAN: String
  UAN: String
  Aadhaar_Card_Number: String
  Military_Service: Boolean
  No_Dependent: Boolean
  Relative_In_Company: Boolean
  Driving_License_No: String
  License_Issue_Date: Date
  License_Expiry_Date: Date
  Issuing_Authority: String
  Issuing_Country: String
  Issuing_State: String
  Vehicle_Type: String
  License_File_Name: String
  Passport_File_Name: String
  Passport_No: String
  Issue_Date: Date
  Passport_Issuing_Country: String
  Passport_Issuing_Authority: String
  Expiry_Date: Date
  pApartment_Name: String
  pStreet_Name: String
  pCity: String
  pCity_Id: Int
  pCity_Id_Name: String
  pState: String
  pBarangay: String
  pBarangay_Id: Int
  pBarangay_Id_Name: String
  pRegion: String
  cBarangay: String
  cBarangay_Id: Int
  cBarangay_Id_Name: String
  cRegion: String
  oBarangay: String
  oBarangay_Id: Int
  oBarangay_Id_Name: String
  oRegion: String
  oCity: String
  oCity_Id: Int
  oCity_Id_Name: String
  oState: String
  oCountry: String
  oPincode: String
  pCountry: String
  pPincode: String
  cApartment_Name: String
  cStreet_Name: String
  cCity: String
  cCity_Id: Int
  cCity_Id_Name: String
  cState: String
  cCountry: String
  cPincode: String
  Use_Location_Address: Int
  Land_Line_No: String
  Mobile_No: String
  Mobile_No_Country_Code: String
  Fax_No: String
  Emergency_Contact_Name:String
  Emergency_Contact_Relation: String
  Section1_Progress: Boolean
  Section2_Progress: Boolean
  Section3_Progress: Boolean
  Section4_Progress: Boolean
  Ethnic_Race: String
  Gender_Orientations: String
  Pronoun: String
  Statutory_Insurance_Number: String
  PRAN_No: String
  Tax_Code: String
}

type BankDetails {
  Candidate_Id: String
  Bank_Account_Name: String
  Bank_Account_Number: String
  Bank_Name: String
  Emp_Bank_Id: Int
  Branch_Name: String
  IFSC_Code: String
  Street: String
  City: String
  State: String
  Zip: String
  Account_Type: String
  Credit_Account: String
  Beneficiary_Id: String
  Status: String
  BSB_Code: String
  File_Name: String
}

type DocumentCategory {
  Category_Id: Int
  Category_Name: String
}

type AllDocumentCategory {
  category: [DocumentCategory]
}

type DocumentType {
  Document_Type_Id: Int
  Document_Type: String
}

type AllDocumentType {
  documentType: [DocumentType]
}

type DocumentSubType {
  Document_Sub_Type_Id: Int
  Document_Sub_Type: String
  Mandatory: String
  Instruction: String
  File_Name: String
  Is_Default: Boolean
}

type AllDocumentSubType {
  documentSubType: [DocumentSubType]
}

type Documents {
  Document_Id: Int
  Candidate_Id: String
  Category_Id: Int
  Category_Name: String
  Document_Type_Id: Int
  Document_Type: String
  Document_Sub_Type_Id: Int
  Document_Sub_Type: String
  Mandatory: String
  Instruction: String
  Is_Default: Boolean
  Document_Name: String
  File_Name: String
}

type AllDocuments {
  documents: [Documents]
}

type AllUrl {
  url: [Url]
}

type Url {
  Url: String
  Url_Hash: String
  Pin: Int
  Designation: String
  Department: String
  Location: String
  Expire_Time: Date
  Candidate_Id: String
  Job_Code: String
  Probation_Date: Date
  Date_Of_Join: Date
  Manager_Name: String
  Status: String
  Name: String
  Email: String
  Employee_Type: String
  Work_Schedule: String
  Service_Provider_Id: Int
  Service_Provider_Name: String
  Business_Unit_Id: Int
  Business_Unit: String
  Created_At: Date
  Created_By: String
  Updated_At: Date
  Updated_By: String
  Offset: String
}

type AllCertification {
  certificates: [Certification]
}

type Certification {
  Certification_Id: Int
  Certification_Name: String
  Received_Date: Date
  Certificate_Received_From: String
  Ranking: String
  File_Name: String
  Document_Name: String
  Sub_Type_Id: Int
  Document_Sub_Type: String
}

type AllEmployeeType {
  employeeType: [EmployeeType]
}

type EmployeeType {
  EmpType_Id: Int
  Employee_Type: String
}

type AllDependent {
  dependent: [Dependent]
  relation: [candidateRelationList]
}

type Dependent {
  Dependent_Id: Int
  Dependent_First_Name: String
  Dependent_Last_Name: String
  Gender: String
  Gender_Id: Int
  Relationship: String
  Dependent_DOB: Date
}

type BankName {
  Bank_Id: Int
  Bank_Name: String
}

type AllBankName {
  bankName: [BankName]
}

type AllEducation {
  education: [Education]
}

type Education {
  Education_Id: Int
  Education_Type: Int
  Education_Type_Name: String
  Specialisation: String
  Institute_Name: String
  Specialization_Id: Int
  Institution_Id: Int
  Specialization_Name: String
  Specialization_Code: String
  Institution_Code:String
  Institution_Name:String
  University: String
  Year_Of_Start: Int
  Year_Of_Passing: String
  Percentage: String
  Grade: String
  File_Name: String
  Document_Name: String
  Sub_Type_Id: Int
  Document_Sub_Type: String
  Start_Date: Date
  End_Date: Date
  City: String
  State: String
  Country: String
}

type AllTraining {
  training: [Training]
}

type Training {
  Training_Id: Int
  Training_Name: String
  Training_Start_Date: Date
  Training_End_Date: Date
  Trainer: String
  Center: String
  File_Name: String
  Document_Name: String
  Sub_Type_Id: Int
  Document_Sub_Type: String
}

type AllManagers {
  managers: [Manager]
}

type Manager {
  Employee_Id: Int
  Emp_First_Name: String
  Emp_Pref_First_Name: String
  Emp_Middle_Name: String
  Emp_Last_Name: String
}

type AllExperience {
  experience: [Experience]
}

type Experience {
  Experience_Id: Int
  Candidate_Id: String
  Prev_Company_Name: String
  Prev_Company_Location: String
  Designation: String
  Start_Date: Date
  End_Date: Date
  Duration: String
  File_Name: String
  Experience_Reference: [Experience_References]
}

type AllCourseDetails {
  courseDetails: [CourseDetails]
}

type CourseDetails {
  Course_Id: Int
  Course_Name: String
  Course_Code: String
  Document_Sub_Type_Id: Int
}

type AllAccountType {
  accountType: [AccountType]
}

type AccountType {
  Account_Type_Id: Int
  Account_Type: String
}

type ValidityType {
  id: Int
  Type: String
}

type AllValidityType {
  validity: [ValidityType]
}

type MartialStatusRelation {
  Dependent_Relationship: String
}

type AllMartialStatusRelation {
  marital_status_relation: [MartialStatusRelation]
}

type Location {
  Location_Id: String
  Location_Name: String
  Location_Type: String
}

type AllLocation {
  location: [Location]
}

type Country {
  Country_Code: String
  Country_Name: String
}

type AllCountry {
  country: [Country]
}

type Department {
  Department_Id: Int
  Department_Name: String
}

type AllDepartment {
  department: [Department]
}

type Designation {
  Designation_Id: Int
  Designation_Name: String
}

type AllDesignation {
  designation: [Designation]
}

type WorlSchedule {
  WorkSchedule_Id: Int
  Title: String
}

type AllWorkSchedule {
  work_schedule: [WorlSchedule]
}

type MartialStatus {
  Marital_Status_Id: Int
  Marital_Status: String
}

type AllMartialStatus {
  marital_status: [MartialStatus]
}

type Language {
  Lang_Id: Int
  Language_Name: String
}

type Languages {
  languages: [Language]
}

type GeneralMessage {
  message: String
}

type getExperienceResponse {
  experience: [Experience]
  previousExperienceYear: Int
  previousExperienceMonth: Int
}
type pinAuthResponse {
  message: String
  countryCode: String
  serviceProviderId: Int
}

type retrieveNationalityListResponse {
  nationalityData:[nationalityDetails]
}

type nationalityDetails {
  nationalityId: Int
  nationalityCode: String
  nationality: String
}

type retrieveReligionListResponse {
  religionData:[religionDetails]
}

type retrieveGenderListResponse {
  genderData: [genderDetails]
}

type genderDetails {
  genderId: Int
  gender: String
}

type religionDetails {
  religionId: Int
  religionCode: String
  religion: String
}


type listEduInstitutionAndSpecializationResponse {
  errorCode: String
  message: String
  institution: [institution]
  specialization: [specialization]
}


type institution {
  Institution_Id: Int
  Institution_Code: String
  Institution: String
}

type specialization {
  Specialization_Id: Int
  Specialization_Code: String
  Specialization: String
}

type retrieveCompanyDetailResponse {
  errorCode: String
  message: String
  partnerIntegration: String
}

input candidateRelation {
  Relation_Name: String
  Position_Code: String
  Relation_Id: Int
}

type candidateRelationList {
  Relation_Name: String
  Position_Code: String
  Relation_Id: Int
}

type AllCandidateRelation {
  relation: [candidateRelationList]
}

input Candidate_Language {
  Lang_Known: Int
  Lang_Spoken: Int
  Lang_Read_Write: Int
  Lang_Proficiency: String
}

type Candidate_Languages {
  Language_Name: String
  Lang_Known: Int
  Lang_Spoken: Int
  Lang_Read_Write: Int
  Lang_Proficiency: String
}

input Reference_Details{
  Reference_Name: String
  Reference_Email: String
  Reference_Number: String
}

type Experience_References{
  Reference_Name: String
  Reference_Email: String
  Reference_Number: String
}

schema {
  query: Query
  mutation: Mutation
}
