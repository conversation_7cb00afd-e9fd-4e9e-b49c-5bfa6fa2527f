const resolvers = {
    Mutation: {
        updateCertification: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var dateFormat = require('dateformat');
            var { ApolloError, UserInputError } = require('apollo-server-lambda');

            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_url')
                    .where('Url_Hash', args.Url_Hash)
                    .then((url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } else {
                            if (!args.Certification_Name) {
                                throw new Error("Certification Name should not be empty");
                            } else if (args.Certification_Name.length > 100) {
                                throw new Error("Certification Name should be less than or equal to 100 characters");
                            } else if (!args.Received_Date) {
                                throw new Error("Received Date should not be empty");
                            } else if (!args.Certificate_Received_From) {
                                throw new Error("Certificate Received From should not be empty");
                            } else if (args.Certificate_Received_From.length > 100) {
                                throw new Error("Certificate Received From should be less than or equal to 100 characters");
                            }
                            return knexconfig('candidate_certifications')
                                .where('Certification_Id', args.Certification_Id)
                                .andWhere('Candidate_Id', url[0].Candidate_Id)
                                .then((details) => {
                                    if (!details[0]) {
                                        throw new Error("Certificate Detail not found");
                                    } else {
                                        try {
                                            var Received_Date = (!args.Received_Date || isNaN(Date.parse(args.Received_Date))) ? (null) : ((isNaN(Number(args.Received_Date))) ? dateFormat(new Date((args.Received_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Received_Date)), "yyyy-mm-dd"));
                                        }
                                        catch (err) {
                                            throw new Error("Invalid Date Format")
                                        }
                                        return knexconfig('candidate_certifications')
                                            .where('Certification_Id', args.Certification_Id)
                                            .andWhere('Candidate_Id', url[0].Candidate_Id)
                                            .update({
                                                Certification_Name: args.Certification_Name,
                                                Received_Date: Received_Date,
                                                Ranking: args.Ranking ? args.Ranking : null,
                                                Certificate_Received_From: args.Certificate_Received_From,
                                            })
                                            .transacting(trc)
                                            .then(() => {
                                                return knexconfig('candidate_certifications_documents')
                                                    .where('Certification_Id', args.Certification_Id)
                                                    .then((document_exist) => {
                                                        if (!document_exist[0]) {
                                                            if (!args.File_Name) {
                                                                return {
                                                                    message: "Certificate updated"
                                                                }
                                                            } else {
                                                                return knexconfig('candidate_certifications_documents')
                                                                    .insert({
                                                                        Certification_Id: args.Certification_Id,
                                                                        File_Name: args.File_Name,
                                                                        Document_Name: args.Document_Name,
                                                                        Sub_Type_Id: args.Sub_Type_Id
                                                                    })
                                                                    .transacting(trc)
                                                                    .then(() => {
                                                                        return {
                                                                            message: "Certificate updated"
                                                                        }
                                                                    })
                                                            }
                                                        } else {
                                                            if (!args.File_Name) {
                                                                return knexconfig('candidate_certifications_documents')
                                                                    .where('Certification_Id', args.Certification_Id)
                                                                    .del()
                                                                    .transacting(trc)
                                                                    .then(() => {
                                                                        return {
                                                                            message: "Certificate updated"
                                                                        }
                                                                    })
                                                            } else {
                                                                return knexconfig('candidate_certifications_documents')
                                                                    .where('Certification_Id', args.Certification_Id)
                                                                    .update({
                                                                        File_Name: args.File_Name,
                                                                        Document_Name: args.Document_Name,
                                                                        Sub_Type_Id: args.Sub_Type_Id
                                                                    })
                                                                    .transacting(trc)
                                                                    .then(() => {
                                                                        return {
                                                                            message: "Certificate updated"
                                                                        }
                                                                    })
                                                            }
                                                        }
                                                    })
                                            })
                                    }
                                })
                        }
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                console.log('return successful response from updateCertification');
                return result;
            }).catch(function (err) {
                console.log('Error in updateCertification', err);
                if (err.message == "REO0101") {
                    console.log('URL not found')
                    throw new ApolloError("URL not found", "REO0101")
                } else if (err.message == "Certification Name should not be empty") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Certification Name should be less than or equal to 100 characters") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Received Date should not be empty") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Certificate Received From should not be empty") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Certificate Received From should be less than or equal to 100 characters") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Invalid Date Format") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Certificate Detail not found") {
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;