const resolvers = {
    Query : {
        listMartialStatus : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

                        return knexconfig("marital_status")
                        .orderBy('Marital_Status', 'asc')
                        .then((status)=>{
                            if(!status[0]){
                                return {
                                    marital_status:null
                                }
                            } else{
                                return {
                                    marital_status: status
                                }
                            }
                        }).catch(function(err){
                console.log('Error in listMartialStatus',err);
                if (err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            })
        }
    },
}
exports.resolvers = resolvers;