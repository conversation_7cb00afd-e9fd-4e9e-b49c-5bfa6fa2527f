//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')

//function to add / update  training details
module.exports.addUpdateTrainingDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateTrainingDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const fieldValidations = {
                trainingName: "IVE0373",
                trainingStartDate: "IVE0374",
                trainingEndDate: "IVE0375",
                trainingDuration: "IVE0376",
                trainer: "IVE0377",
                center: "IVE0378"
            }
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let trainingData = {
                    Candidate_Id: args.candidateId,
                    Training_Name: args.trainingName,
                    Training_Start_Date: args.trainingStartDate,
                    Training_End_Date: args.trainingEndDate,
                    Training_Duration: args.trainingDuration,
                    Trainer: args.trainer,
                    Center: args.center
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (args.trainingId) {
                                return (
                                    organizationDbConnection(ehrTables.candidateTraining)
                                        .update(trainingData)
                                        .transacting(trx)
                                        .where('Training_Id', args.trainingId)
                                        .then(async (updateData) => {
                                            if (updateData) {
                                                await organizationDbConnection(ehrTables.candidateTrainingDocuments)
                                                .update({'File_Name': args.fileName, 'Sub_Type_Id': args.documentSubTypeId, 'Document_Name': args.fileName})
                                                .where('Training_Id', args.trainingId)
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the training details', trainingData)
                                                throw 'IO0119'
                                            }
                                        })
                                )
                            } else {
                                return (
                                    organizationDbConnection(ehrTables.candidateTraining)
                                        .insert(trainingData)
                                        .transacting(trx)
                                        .then(async (insertData) => {
                                            if (insertData) {
                                                await organizationDbConnection(ehrTables.candidateTrainingDocuments)
                                                .insert({'Training_Id': insertData, 'File_Name': args.fileName, 'Sub_Type_Id': args.documentSubTypeId, 'Document_Name': args.fileName})

                                                return 'success'
                                            } else {
                                                console.log('Error while updating the training details', trainingData)
                                                throw 'IO0119'
                                            }
                                        })
                                        .catch((err)=>{
                                            console.log('Error while updating the training details', err)
                                            throw 'IO0119'
                                        })
                                )
                            }

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The candidate ${args.trainingName} training details has been ${args.trainingId ? 'updated' : 'added'}.`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);

                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Candidate  training details has been added/updated successfully." };
                            } else {
                                throw 'IO0119'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateTrainingDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0119');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )

            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the training details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateTrainingDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateTrainingDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0018');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}