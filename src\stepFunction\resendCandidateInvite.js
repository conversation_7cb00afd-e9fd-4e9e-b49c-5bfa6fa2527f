//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require knex to make DB connection
const knex = require('knex');
const { ehrTables } = require('../common/tableAlias');
const { updateResendCandidate, getOrganizationName ,resendCandidateEmail, sendCustomEmail} = require('../common/commonFunctions');
const { listEmailTemplatePlaceHolderValues } = require('@cksiva09/hrapp-corelib/src/CommonFunctions');
const { formIds } = require('../common/appConstants');

module.exports.resendCandidateInvite = async (args, context) => {
    console.log('Inside resendCandidateInvite function');
    let organizationDbConnection,appmanagerDbConnection;
    try {
        let loginEmployeeId = args.logInEmpId;
        let candidates = args.candidates;
        let orgCode=args.orgCode;
        let context = {
          Employee_Id: loginEmployeeId,
          Org_Code: orgCode,
        };
        let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
         if(Object.keys(databaseConnection).length){
      // form app manager database connection
      appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;
        if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
          let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
          //Get database connection
          let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
          if(Object.keys(connection).length>0){
            organizationDbConnection = knex(connection.OrganizationDb);
            let invitedCandidateArray=[];

        return organizationDbConnection
        .transaction(async function (trx) {
            let value=await commonLib.func.getDefaultTemplate({ categoryId: 16, formId:178 }, organizationDbConnection);
            if(value && Object.keys(value).length){

              for(let i=0;i<candidates.length;i++){

                let argsObject={
                  templateId:value.Template_Id,
                  candidateId:candidates[i],
                }

                let eventVal={
                  Source: process.env.emailFrom,
                  ReplyToAddresses: [process.env.emailReplyTo],
                  bucketName : process.env.documentsBucket,
                  region:process.env.region
                }

                const { emailResult, ses, event } =await commonLib.func.listEmailTemplatePlaceHolderValues(argsObject,organizationDbConnection,context,eventVal,invitedCandidateArray); 
                let inviteStatus = await sendCustomEmail(emailResult, ses, event);

                if (inviteStatus && inviteStatus.toLowerCase() === "invited") {
                  invitedCandidateArray.push(candidates[i]);
                  await updateCandidateUrlStatus(organizationDbConnection,'Invited',invitedCandidateArray,trx);
                } 
              }

              let systemLogParam = candidates.map(candidateId => ({ 
                action: 'Invited', 
                userIp: '-', 
                employeeId: loginEmployeeId, 
                formId:  formIds.individuals,
                organizationDbConnection: trx,
                isEmployeeTimeZone: 0,
                changedData: args,
                uniqueId: candidateId, 
                message: `The onboarding invitation has been resent to the candidate.` 
              }));
              // Call the function to add the system log
              await commonLib.func.createMultiSystemLogActivities(systemLogParam);
              return; 
            }

            let orgDetails = await commonLib.func.getOrgDetails(args.orgCode, organizationDbConnection, 1);
            let organizationName = await getOrganizationName(organizationDbConnection, orgCode);
            orgDetails.orgCode = orgCode;
            //get the email data params in a form of array
            let emailData = await updateResendCandidate(organizationDbConnection,candidates,loginEmployeeId,organizationName.orgName, orgDetails,trx, orgRegionDetails);
            
            if(emailData){

                for(let email of emailData){
                  let candidateId=email.candidateId;
                  //method to resend email and update the status
                  let inviteStatus=await resendCandidateEmail(email)
                  if(inviteStatus==='Invited'){
                    invitedCandidateArray.push(candidateId);
                  }
                }

                if(invitedCandidateArray.length>0) {

                  await updateCandidateUrlStatus(organizationDbConnection,'Invited',invitedCandidateArray,trx);
                  
                  let systemLogParam = candidates.map(candidateId => ({ 
                    action: 'Invited', 
                    userIp: '-', 
                    employeeId: loginEmployeeId, 
                    formId:  formIds.individuals,
                    organizationDbConnection: trx,
                    isEmployeeTimeZone: 0,
                    changedData: args,
                    uniqueId: candidateId, 
                    message: `The onboarding invitation has been resent to the candidate.` 
                  }));
                  // Call the function to add the system log
                  await commonLib.func.createMultiSystemLogActivities(systemLogParam);
                }
            }
            else{
                console.log("candidates details not found")
                throw 'CCH0011'
            }
                
                 
        }).then(()=>{

            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Invite to a candidate has been mail sent successfully" };

        }).catch((err)=> {
            console.error('Error in resendCandidateInvite function block', err);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            let errResult = commonLib.func.getError(err, 'CCH0012');
            throw new ApolloError(errResult.message, errResult.code);
        });
      }
      else{
        throw("Error while getting connection for org db.")
      }
      }else{
        throw("Error while getting database connection.")
      }

    }
  } catch(err){
        
        console.error('Error in inviteCandidateOnboard function main block', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, 'CCH0014');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }

}

async function updateCandidateUrlStatus(organizationDbConnection,status,statusArray,trx)
{
  try{
    console.log("Inside updateCandidateUrlStatus function.")
    //update the status according to the status of sending email
    return(
        organizationDbConnection(ehrTables.candidateUrl)
        .transacting(trx)
        .whereIn("id", statusArray)
      .update("Email_Resend_Status", status)
      .then(() => {
          console.log('candidate status updated successfully.')
          return true;
      })
      .catch(e=>{
        console.log('Error in updateCandidateUrlStatus .catch block',e);
        return false;
      })
    )
  }
  catch(e)
  {
    console.log('Error in updateCandidateUrlStatus main catch block',e);
    return false;
  }
}


