// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../common/appConstants');

let organizationDbConnection;
module.exports.retrieveDesignationAccreditationCategoryDetails = async (parent, args, context, info) => {
    try {
        console.log("Inside retrieveDesignationAccreditationCategoryDetails function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let formId = args.formId ? args.formId : formIds.accreditation;
        // get the access rights of the login employee for the accreditation form
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, formId);

        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            if (checkRights.Employee_Role == "admin") {
                return (
                    organizationDbConnection(ehrTables.designation)
                        .select('D.Designation_Id',
                            organizationDbConnection.raw("CASE WHEN D.Designation_Code IS NOT NULL THEN CONCAT(D.Designation_Code,' - ',D.Designation_Name) ELSE D.Designation_Name END AS Designation_Name"),
                            'DM.Accreditation_Category_And_Type_Id', "AC.Accreditation_Category", "AC.Accreditation_Type", "D.Designation_Status")
                        .from(ehrTables.designation + " as D")
                        .innerJoin(ehrTables.designationAccreditationCategoryTypeMapping + " as DM", "D.Designation_Id", "DM.Designation_Id")
                        .innerJoin(ehrTables.accreditationCategoryAndType + " as AC", "DM.Accreditation_Category_And_Type_Id", "AC.Accreditation_Category_And_Type_Id")
                        .then((data) => {
                            //destroy the connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Designation and Accreditation category mapping details retrieved successfully.", designationMappingDetails: data };
                        })
                        .catch((err) => {
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            console.log('Error in retrieveDesignationAccreditationCategoryDetails function .catch block.', err);
                            let errResult = commonLib.func.getError(err, 'EO0112');
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                throw '_DB0109';
            }
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveDesignationAccreditationCategoryDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'EO0112');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
