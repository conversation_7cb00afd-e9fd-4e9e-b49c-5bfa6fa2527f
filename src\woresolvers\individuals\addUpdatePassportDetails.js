//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { formName, systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const {validateCandidateInputs} = require('../../common/commonFunctions')

//function to add / update passport details
module.exports.addUpdatePassportDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdatePassportDetails function');
    let organizationDbConnection;
    let validationError = {};
    let isUpdate = 0;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const fieldValidations = {
                passportNo: "IVE0333"
            };
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let passportData = {
                    Candidate_Id: args.candidateId,
                    Passport_No: args.passportNo,
                    Issue_Date: args.passportIssueDate,
                    Issuing_Authority: args.issuingAuthority,
                    Issuing_Country: args.issuingCountry,
                    Expiry_Date: args.passportExpiryDate,
                    Visa: '',
                    File_Name: args.fileName
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            return (
                                organizationDbConnection(ehrTables.candidatePassport)
                                .select('Candidate_Id')
                                .where('Candidate_Id', passportData.Candidate_Id)
                                .transacting(trx)
                                .then((data)=>{
                                    if(data && data.length){
                                        isUpdate = 1
                                        return(
                                        organizationDbConnection(ehrTables.candidatePassport)
                                            .update(passportData)
                                            .where('Candidate_Id', passportData.Candidate_Id)
                                            .transacting(trx)
                                            .then((updatePassportDetails) => {
                                                if (updatePassportDetails) {
                                                    return 'success'
                                                } else {
                                                    console.log('Error while updating the passport details', passportData)
                                                    throw 'IO0105'
                                                }
                                            })
                                        )
                                    }else{
                                        return(
                                        organizationDbConnection(ehrTables.candidatePassport)
                                            .insert(passportData)
                                            .transacting(trx)
                                            .then((insertPassport) => {
                                                if (insertPassport) {
                                                    return 'success'
                                                } else {
                                                    console.log('Error while inserting the passport details', passportData)
                                                    throw 'IO0105'
                                                }
                                            })
                                        )
                                    }
                                })
                            )

                        })
                        .then(async(response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The candidate passport details has been ${isUpdate ? 'updated': 'added'}.`
                                };
    
                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);
                                organizationDbConnection ? organizationDbConnection.destroy() : null;

                                return { errorCode: "", message: "Candidate Passport details has been added/updated successfully" };
                            } else {
                                throw 'IO0105'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdatePassportDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0105');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the passport details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdatePassportDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdatePassportDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0006');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}