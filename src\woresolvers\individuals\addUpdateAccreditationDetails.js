//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')
const moment = require('moment-timezone');

//function to add / update accreditation details
module.exports.addUpdateAccreditationDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateAccreditationDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const fieldValidations = {
                identifier: "IVE0380",
            }
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let accreditationData = {
                    Candidate_Id: args.candidateId,
                    Accreditation_Category_And_Type_Id: args.accreditationCategoryAndType,
                    Received_Date: args.receivedDate,
                    Expiry_Date: args.expiryDate,
                    Identifier: args.identifier,
                    File_Name: args.fileName,
                    Exam_Rating: args.examRating,
                    Exam_Date_Year: args.examDateYear ? args.examDateYear : null,
                    Exam_Date_Month: args.examDateMonth ? args.examDateMonth : null,
                    Dependent_Id: args.dependentId ? args.dependentId : null
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (args.accreditationDetailId) {
                                accreditationData.Updated_On = moment.utc().format("YYYY-MM-DD HH:mm:ss"),
                                    accreditationData.Updated_By = loginEmployeeId
                                return (
                                    organizationDbConnection(ehrTables.candidateAccreditationDetails)
                                        .update(accreditationData)
                                        .transacting(trx)
                                        .where('Accreditation_Detail_Id', args.accreditationDetailId)
                                        .then(async (updateData) => {
                                            if (updateData) {
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the accreditation details', accreditationData)
                                                throw 'IO0123'
                                            }
                                        })
                                )
                            } else {
                                accreditationData.Added_On = moment.utc().format("YYYY-MM-DD HH:mm:ss"),
                                    accreditationData.Added_By = loginEmployeeId
                                return (
                                    organizationDbConnection(ehrTables.candidateAccreditationDetails)
                                        .insert(accreditationData)
                                        .transacting(trx)
                                        .then(async (insertData) => {
                                            if (insertData) {
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the accreditation details', accreditationData)
                                                throw 'IO0123'
                                            }
                                        })
                                )
                            }

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The candidate accreditation details has been ${args.accreditationDetailId ? 'updated' : 'added'}.`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);

                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Candidate Accreditation details has been added/updated successfully." };
                            } else {
                                throw 'IO0123'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateAccreditationDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0123');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )

            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the accreditation details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateAccreditationDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateAccreditationDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0022');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}