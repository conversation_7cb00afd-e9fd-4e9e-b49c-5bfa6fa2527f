{"securityGroupIds": [""], "subnetIds": [""], "KmsKeyId": "d7612adb-0171-4165-a705-dd5728706d1d", "role": "", "secretName": "hrapp-stage", "imageBucket": "s3.hrapp-dev-public-asset", "documentBucket": "caprice-dev-stage", "baseUrlUI": "http://localhost:3000?companyName=", "emailFrom": "<EMAIL>", "emailReplyTo": "<EMAIL>", "sesRegion": "us-east-1", "logoBucket": "s3.hrapp-dev-public-images", "domainName": "hrapp", "fullDomainName": ".hrapp.co.in", "customDomainName": "onboard.com", "documentsBucket": "caprice-dev-stage", "commonAPIDomainName": "api.hrapp.co.in", "dbPrefix": "hrapp_", "baseAUUrlUI": "http://localhost:3000?companyName=", "vendorBucketName": "supplier.hrapp.co.in", "authorizerARN": "arn:aws:lambda:ap-south-1:692647644057:function:ATS-dev-firebaseauthorizer", "resendInviteVendorStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:devResendInviteVendorStepFunction", "resendInviteCandidateStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:devResendInviteCandidateStepFunction", "asyncSyntrumAPIStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-asyncSyntrumAPIFunction", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:692647644057:function:HRAPPOnboard-dev", "snsRegion": "ap-southeast-1", "snsEndpoint": "sns.ap-southeast-1.amazonaws.com"}