
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const getConnection = async (orgCode) => {
    let connection = await commonLib.func.getDataBaseConnection(
        {
            stageName: process.env.stageName, dbPrefix: process.env.dbPrefix, dbSecretName: process.env.secretName,
        region: process.env.region, orgCode: orgCode, secretRequired: 1
        });
        return connection.OrganizationDb;
}
async function getCompanyName(Org_Code) {
    
    var { ApolloError } = require('apollo-server-lambda');
    const AWS = require('aws-sdk');
    var client = new AWS.SecretsManager({
        region: process.env.region
    });

    if(process.env.stage != 'local'){

        var secretName = process.env.secretName ;

        var appManagerDb = await client.getSecretValue({ SecretId: secretName }).promise();

        appManagerDb = JSON.parse(appManagerDb.SecretString);
    }

    var appManagerConnection = {
        client: 'mysql',
        connection: {
            host: (process.env.stage != 'local') ? appManagerDb.hostname : "localhost",
            user: (process.env.stage != 'local')? appManagerDb.username : "root",
            password: (process.env.stage != 'local')? appManagerDb.password : "welcome2caprice",
            database: process.env.domainName +'_managerdb',
            charset: "utf8"
        },
        pool: { min: 0, max: 10 },
        acquireConnectionTimeout: 10000
    }

    const appManagerKnex = require('knex')(appManagerConnection);

    return appManagerKnex.transaction(function(trc){
        return appManagerKnex('hrapp_registeruser')
        .where('Org_Code',Org_Code)
        .then(async(data)=>{
            if(!data[0]){
                throw new Error('REO0001')
            } else {
                return data[0].Org_Name;
            }
        })
        .then(trc.commit)
            .catch(trc.rollback);
        }).then(function(result){
            return result;
        }).catch(function(err){
            console.log("Error in db connection",err);
            if (err.message == "REO0001"){
                console.log("Org Code not found")
                throw new ApolloError("Organization doesn't exists","REO0001")
            } else if (err.message == "REO0002"){
                console.log("DB is not in an active state")
                throw new ApolloError("You are no longer to fill this form","REO0002")
            } else {
                throw new Error('Something went wrong');
            }
        }).finally(() => {
            appManagerKnex.destroy();
        })
}

async function getTimeZone(Org_Code,Employee_Id) {
    var moment = require('moment-timezone');
    var connection = await getConnection(Org_Code);
    const knexconfig = require('knex')(connection);

    return knexconfig.transaction(function(trc){
        return knexconfig.select('timezone.TimeZone_Id')
        .from('emp_job')
        .leftJoin('location','emp_job.Location_Id','location.Location_Id')
        .leftJoin('timezone','location.Zone_Id','timezone.Zone_Id')
        .where('Employee_Id',Employee_Id)
        .then(async(data)=>{
            if(!data[0]){
                throw new Error('REO0001')
            } else {
                let offset = moment().tz(data[0].TimeZone_Id).format('Z')
                return {
                    TimeZone_Id : data[0].TimeZone_Id,
                    offset : offset
                }
            }
        })
        .then(trc.commit)
            .catch(trc.rollback);
        }).then(function(result){
            return result;
        }).catch(function(err){
            console.log("Error in db connection",err);
            if (err.message == "REO0001"){
                console.log("Org Code not found")
                throw new ApolloError("Organization doesn't exists","REO0001")
            } else if (err.message == "REO0002"){
                console.log("DB is not in an active state")
                throw new ApolloError("You are no longer to fill this form","REO0002")
            } else {
                throw new Error('Something went wrong');
            }
        }).finally(() => {
            knexconfig.destroy();
        })
}

module.exports.getConnection = getConnection;
module.exports.getCompanyName = getCompanyName;
module.exports.getTimeZone = getTimeZone;
