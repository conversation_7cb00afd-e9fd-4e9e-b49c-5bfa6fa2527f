//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { formName, systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')
const moment = require('moment-timezone');

//function to add / update document details
module.exports.addUpdateCandidateDocuments = async (parent, args, context, info) => {
    console.log('Inside addUpdateCandidateDocuments function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const fieldValidations = {
                documentName: "IVE0379",
            }
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let documentData = {
                    Candidate_Id: args.candidateId,
                    Category_Id: args.documentCategory,
                    Document_Type_Id: args.documentType,
                    Sub_Document_Type_Id: args.documentSubType,
                    Document_Name: args.documentName,
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (args.documentId) {
                                return (
                                    organizationDbConnection(ehrTables.candidateDocumentCategory)
                                        .update(documentData)
                                        .transacting(trx)
                                        .where('Document_Id', args.documentId)
                                        .then(async (updateData) => {
                                            if (updateData) {
                                                    let document = await insertUpdateDocuments(organizationDbConnection, args.fileName, args.fileSize, args.documentId)
                                                    if(document){
                                                        return 'success'
                                                    }else{
                                                        throw 'IO0132'
                                                    }
                                            } else {
                                                console.log('Error while updating the document details', documentData)
                                                throw 'IO0132'
                                            }
                                        })
                                )
                            } else {
                                documentData.Added_On = moment.utc().format("YYYY-MM-DD HH:mm:ss")
                                return (
                                    organizationDbConnection(ehrTables.candidateDocumentCategory)
                                        .insert(documentData)
                                        .transacting(trx)
                                        .then(async(insertData) => {
                                            if (insertData) {
                                                    let document = await insertUpdateDocuments(organizationDbConnection, args.fileName, args.fileSize, insertData[0])
                                                    if(document){
                                                        return 'success'
                                                    }else{
                                                        throw 'IO0132'
                                                    }
                                            } else {
                                                console.log('Error while updating the document details', documentData)
                                                throw 'IO0132'
                                            }
                                        })
                                )
                            }

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: args.documentId ? systemLogs.roleUpdate : systemLogs.roleAdd,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formName: formName.candidate,
                                    trackingColumn: '',
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.employeeId,
                                    message: `Candidate Document details ${args.documentId ? 'updated' : 'added'} for ${args.candidateId}`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);

                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Candidate Document details has been added/updated successfully." };
                            } else {
                                throw 'IO0132'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateCandidateDocuments .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0132');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )

            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the document details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateCandidateDocuments function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateCandidateDocuments function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0030');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

async function insertUpdateDocuments(organizationDbConnection, fileName, fileSize, documentId) {
    try {
        return (
            organizationDbConnection(ehrTables.candidateDocument)
                .delete()
                .where('Document_Id', documentId)
                .then(() => {
                    if(fileName){
                        return (
                            organizationDbConnection(ehrTables.candidateDocument)
                                .insert({
                                    'Document_Id': documentId,
                                    'File_Name': fileName,
                                    'File_Size': fileSize
                                })
                                .then(() => {
                                    return true
                                })
                                .catch((err)=>{
                                    console.log('Error while adding documents .catch', err)
                                    throw err
                                })
                        )
                    }
                })
        )
    } catch (err) {
        console.log('Error while adding documents main catch', err)
        throw err
    }
}