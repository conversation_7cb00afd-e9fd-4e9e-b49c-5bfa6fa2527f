// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../common/appConstants');
const { fetchJobRoleDetails, getOnboardSpecialistRollAccess} = require('../../common/commonFunctions');

module.exports.listCandidateInvitedIndividuals = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside listCandidateInvitedIndividuals function.");
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, null, '', 'UI', false, formIds.individuals);
        if (!(Object.keys(checkRights).length > 0 && checkRights.Role_View === 1)) {
            throw '_DB0100'
        }

        let candidateAccessResult = await getOnboardSpecialistRollAccess(organizationDbConnection, checkRights,  context.Employee_Id, "onboarding");
        
        
        // Candidate retrieve List API for status is onboarded, Offer letter accepted, Onboarding inprogress 
        return (
            organizationDbConnection(ehrTables.candidatePersonalInfo + " as CPI")
                .select('CPI.Candidate_Id', organizationDbConnection.raw('CONCAT_WS(" ",CPI.Emp_First_Name, CPI.Emp_Middle_Name, CPI.Emp_Last_Name) as Candidate_Name'),
                    'CPI.Personal_Email as Candidate_Email', 'CC.Mobile_No as Candidate_Mobile', 'JP.Job_Post_Id', 'JP.Job_Post_Name', 'DES.Designation_Id', organizationDbConnection.raw("CASE WHEN DES.Designation_Code IS NOT NULL THEN CONCAT(DES.Designation_Code,' - ',DES.Designation_Name) ELSE DES.Designation_Name END AS Designation_Name"), 
                    'DEP.Department_Id','ST.Id as Candidate_Status_Id',  'ST.Status as Candidate_Status', 'CU.Date_Of_Join', 'CU.Job_Code', 'CU.Expire_Time', 
                    'CU.Probation_Date', 'SP.Service_Provider_Id as Organization_Unit_Id','OG.Organization_Group_Id',
                    'BU.Business_Unit_Id', 'WS.WorkSchedule_Id as WorkSchedule_Id', 'WS.Title as WorkSchedule_Name', 'EP2.Employee_Id as Manager_Id',
                    organizationDbConnection.raw('CONCAT_WS(" ",EP2.Emp_First_Name, EP2.Emp_Middle_Name, EP2.Emp_Last_Name) as Manager_Name'), 'ET.EmpType_Id',
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN ET.Employee_Type_Code IS NOT NULL 
                            THEN CONCAT(ET.Employee_Type_Code, ' - ', ET.Employee_Type) 
                            ELSE ET.Employee_Type 
                        END AS Employee_Type
                    `), 
                    'LOC.Location_Id',
                    'CU.Job_Role_Ids',
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN DEP.Department_Code IS NOT NULL 
                            THEN CONCAT(DEP.Department_Code, ' - ', DEP.Department_Name) 
                            ELSE DEP.Department_Name 
                        END AS Department_Name
                    `),
                    organizationDbConnection.raw(
                        "CASE WHEN BU.Business_Unit_Code IS NOT NULL THEN CONCAT(BU.Business_Unit_Code, ' - ', BU.Business_Unit) ELSE BU.Business_Unit END AS Business_Unit"
                    ),
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN OG.Organization_Group_Code IS NOT NULL 
                            THEN CONCAT(OG.Organization_Group_Code, ' - ', OG.Organization_Group) 
                            ELSE OG.Organization_Group 
                        END AS Organization_Group
                    `),
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN LOC.Location_Code IS NOT NULL 
                            THEN CONCAT(LOC.Location_Code, ' - ', LOC.Location_Name) 
                            ELSE LOC.Location_Name 
                        END AS Location_Name
                    `),
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN SP.Service_Provider_Code IS NOT NULL 
                            THEN CONCAT(SP.Service_Provider_Code, ' - ', SP.Service_Provider_Name) 
                            ELSE SP.Service_Provider_Name 
                        END AS Organization_Unit_Name
                    `),

                    organizationDbConnection.raw(
                        "GROUP_CONCAT(DEG.Group_Name ORDER BY CDEG.Group_Id SEPARATOR ',') as Group_Names"
                    ),
                    organizationDbConnection.raw(
                        "GROUP_CONCAT(CDEG.Group_Id ORDER BY CDEG.Group_Id SEPARATOR ',') as Group_Ids"
                    ))
                    .groupBy('CDEG.Candidate_Id')
                .leftJoin(ehrTables.candidateUrl + " as CU", "CU.Candidate_Id", "CPI.Candidate_Id")
                .leftJoin(ehrTables.candidateContact + " as CC", "CC.Candidate_Id", "CPI.Candidate_Id")
                .leftJoin(ehrTables.candidateRecruitmentInfo + " as CRI", "CRI.Candidate_Id", "CPI.Candidate_Id")
                .leftJoin(ehrTables.jobPost + " as JP", "JP.Job_Post_Id", "CRI.Job_Post_Id")
                .innerJoin(ehrTables.atsStatusTable + " as ST", "ST.Id", "CRI.Candidate_Status")
                .leftJoin(ehrTables.businessUnit + " as BU", "BU.Business_Unit_Id", "CU.Business_Unit_Id")
                .leftJoin(ehrTables.empPersonalInfo + " as EP2", "EP2.Employee_Id", "CU.Manager_Id")
                .leftJoin(ehrTables.jobPostLocation + " as JPL", "JP.Job_Post_Id", "JPL.Job_Post_Id")
                .leftJoin(ehrTables.candidateDocumentEnforcementGroups + ' as CDEG', "CDEG.Candidate_Id", "CU.Candidate_Id")
                .leftJoin(ehrTables.documentEnforcementGroup + ' as DEG', 'DEG.Group_Id', 'CDEG.Group_Id')
                .leftJoin(ehrTables.location + ' as LOC', function() {
                    this.on('LOC.Location_Id', '=', organizationDbConnection.raw('COALESCE(CU.Location_Id, JPL.Location_Id)'))
                })
                .leftJoin(ehrTables.designation + ' as DES', function() {
                    this.on('DES.Designation_Id', '=', organizationDbConnection.raw('COALESCE(CU.Designation_Id, JP.Designation)'))
                })
                .leftJoin(ehrTables.department + ' as DEP', function() {
                    this.on('DEP.Department_Id', '=', organizationDbConnection.raw('COALESCE(CU.Department_Id, JP.Functional_Area)'))
                })
                .leftJoin(ehrTables.serviceProvider + ' as SP', function() {
                    this.on('SP.Service_Provider_Id', '=', organizationDbConnection.raw('COALESCE(CU.Service_Provider_Id, JP.Service_Provider_Id)'))
                })
                .leftJoin(ehrTables.organizationGroup + ' as OG', function() {
                    this.on('OG.Organization_Group_Id', '=', organizationDbConnection.raw('COALESCE(CU.Organization_Group_Id, JP.Organization_Group_Id)'))
                })
                .leftJoin(ehrTables.workSchedule + ' as WS', function() {
                    this.on('WS.WorkSchedule_Id', '=', organizationDbConnection.raw('COALESCE(CU.Work_Schedule, JP.Payment_Type)'))
                })
                .leftJoin(ehrTables.employeeType + ' as ET', function() {
                    this.on('ET.EmpType_Id', '=', organizationDbConnection.raw('COALESCE(CU.EmpType_Id, JP.Job_Type)'))
                })
                .whereIn('ST.Status', ["Offer letter Accepted", "Onboarding Inprogress", "Onboarded", "Self Onboarding Completed"])
                //.whereIn('CRI.Candidate_Status', [21, 23, 24,26])
                .groupBy('CPI.Candidate_Id')
               
                .modify(function(){
                    if(args.offset){
                        this.offset(args.offset)
                    }
                    if(args.limit){
                        this.limit(args.limit)
                    }
                    if(candidateAccessResult && !candidateAccessResult.isAdmin){
                        this.whereIn('CPI.Candidate_Id', candidateAccessResult.candidateIds)
                    }
                    // if(candidateAccessResult && candidateAccessResult.isOrgUnitFlag){
                    //     this.whereIn('SP.Service_Provider_Id', candidateAccessResult.serviceProviderids)
                    // }
                })
                .then(async(individuals) => {
                    individuals = await fetchJobRoleDetails(individuals, organizationDbConnection);
                    if (individuals) {
                        individuals = JSON.stringify(individuals)
                    }
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Candidate Invited individuals retrieved successfully", listIndividuals: individuals }
                })
                .catch((e) => {
                    // Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    console.log('Error in listCandidateInvitedIndividuals function .catch block.', e);
                    const errResult = commonLib.func.getError(e, 'IO0101');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listCandidateInvitedIndividuals function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'IO00001');
        throw new ApolloError(errResult.message, errResult.code);
    }
};
