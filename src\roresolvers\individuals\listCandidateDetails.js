// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../common/appConstants');
const { getOnboardSpecialistRollAccess } = require('../../common/commonFunctions');

module.exports.listCandidateDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside listCandidateDetails function..");
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, null, '', 'UI', false, formIds.individuals);
        if (!(Object.keys(checkRights).length > 0 && checkRights.Role_View === 1)) {
            throw '_DB0100'
        }
        let spResult =  await getOnboardSpecialistRollAccess(organizationDbConnection, checkRights, context.Employee_Id, "onboard-individuals");
        return (
            organizationDbConnection(ehrTables.candidatePersonalInfo + " as personal")
                .select(
                    'personal.Candidate_Id',
                    organizationDbConnection.raw('CONCAT_WS(" ",personal.Emp_First_Name, personal.Emp_Middle_Name, personal.Emp_Last_Name) as candidateName'),
                    'personal.Emp_First_Name as candidateFirstName',
                    'personal.Allow_User_Signin as allowUserSignin',
                    'personal.DOB as dob',
                    'job.Location_Id as locationId',
                    'job.Roles_Id as rolesId',
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN loc.Location_Code IS NOT NULL 
                            THEN CONCAT(loc.Location_Code, ' - ', loc.Location_Name) 
                            ELSE loc.Location_Name 
                        END AS locationName
                    `),
                    'personal.Candidate_Status as candidateStatus',
                    'job.Department_Id as departmentId',
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN dept.Department_Code IS NOT NULL 
                            THEN CONCAT(dept.Department_Code, ' - ', dept.Department_Name) 
                            ELSE dept.Department_Name 
                        END AS departmentName
                    `),
                    'job.Designation_Id as designationId',
                    organizationDbConnection.raw("CASE WHEN des.Designation_Code IS NOT NULL THEN CONCAT(des.Designation_Code,' - ',des.Designation_Name) ELSE des.Designation_Name END AS designationName"),
                    'personal.Is_Manager as isManager',
                    'job.Manager_Id as managerId',
                    organizationDbConnection.raw('CONCAT_WS(" ",emp.Emp_First_Name, emp.Emp_Middle_Name, emp.Emp_Last_Name) as managerName'),
                    'etype.EmpType_Id as empTypeId',
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN etype.Employee_Type_Code IS NOT NULL 
                            THEN CONCAT(etype.Employee_Type_Code, ' - ', etype.Employee_Type) 
                            ELSE etype.Employee_Type 
                        END AS employeeType
                    `),
                    'job.Emp_Status as empStatus',
                    'job.Date_Of_Join as doj',
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN sp.Service_Provider_Code IS NOT NULL 
                            THEN CONCAT(sp.Service_Provider_Code, ' - ', sp.Service_Provider_Name) 
                            ELSE sp.Service_Provider_Name 
                        END AS serviceProvider
                    `),
                    'sp.Service_Provider_Id',
                    'personal.Enable_Sign_In_With_Mobile_No as enableSignInWithMobileNo',
                    'job.Work_Schedule as workScheduleId',
                    'ws.Title as workSchedule',
                    'job.Emp_Email as empEmail', 'cu.Url as url', 'cu.Pin as pin', 'personal.Personal_Email as personalEmail',
                    'SAIL.Status as sfOverAllStatus', 'SAIL.Personal_Info_Sync_Status as sfPersonalInfoStatus', 'SAIL.Personal_Info_Failure_Reason as sfPersonalInfoFailureReason', 'SAIL.Additional_Info_Sync_Status as sfAdditionalInfoStatus',
                    'SAIL.Additional_Info_Failure_Reason as sfAdditionalInfoFailureReason', 'SAIL.Education_Info_Sync_Status as sfEducationInfoStatus', 'SAIL.Education_Info_Failure_Reason as sfEducationInfoFailureReason', 'SAIL.Attachment_Info_Sync_Status as sfAttachmentInfoStatus', 'SAIL.Attachment_Info_Failure_Reason as sfAttachmentInfoFailureReason', 
                    'SAIL.Employee_Info_Sync_Status as sfEmployeeInfoStatus', 'SAIL.Employee_Info_Failure_Reason as sfEmployeeInfoFailureReason', 'SAIL.Candidate_Email_Status as emailStatus',
                    organizationDbConnection.raw('CONCAT_WS(" ",EMP1.Emp_First_Name, EMP1.Emp_Middle_Name, EMP1.Emp_Last_Name) as createdBy'), 'EJ1.Emp_Email as createdByEmail',
                    organizationDbConnection.raw('GROUP_CONCAT(CONCAT_WS(" ",EMP2.Emp_First_Name, EMP2.Emp_Middle_Name, EMP2.Emp_Last_Name)) as recruiterName'), 
                    organizationDbConnection.raw('GROUP_CONCAT(EJ2.Emp_Email) as recruiterEmail'), 'SAIL.Hiring_Manager_Email_Status as hiringManagerEmailStatus', 'SAIL.Data_Sync_Result'

                )
                .distinct()
                .leftJoin(ehrTables.candidateJob + " as job", 'personal.Candidate_Id', 'job.Candidate_Id')
                .leftJoin(ehrTables.location + " as loc", 'loc.Location_Id', 'job.Location_Id')
                .leftJoin(ehrTables.department + " as dept", 'job.Department_Id', 'dept.Department_Id')
                .leftJoin(ehrTables.designation + " as des", 'job.Designation_Id', 'des.Designation_Id')
                .leftJoin(ehrTables.employeeType + " as etype", 'etype.EmpType_Id', 'job.EmpType_Id')
                .leftJoin(ehrTables.empPersonalInfo + " as emp", 'job.Manager_Id', 'emp.Employee_Id')
                .leftJoin(ehrTables.serviceProvider + " as sp", 'job.Service_Provider_Id', 'sp.Service_Provider_Id')
                .leftJoin(ehrTables.workSchedule + " as ws", 'job.Work_Schedule', 'ws.WorkSchedule_Id')
                .leftJoin(ehrTables.candidateUrl + " as cu", 'job.Candidate_Id', 'cu.Candidate_Id')
                .leftJoin(ehrTables.candidateRecruitmentInfo + " as CRI", "CRI.Candidate_Id", "personal.Candidate_Id")
                .leftJoin(ehrTables.jobPost+' as JP','CRI.Job_Post_Id','JP.Job_Post_Id')
                .leftJoin(ehrTables.externalApiIntegrationLog+ ' as SAIL', 'SAIL.Candidate_Id', 'personal.Candidate_Id')
                .leftJoin(ehrTables.empPersonalInfo + " as EMP1", 'EMP1.Employee_Id', 'cu.Created_By')
                .leftJoin(ehrTables.empJob + " as EJ1", 'EJ1.Employee_Id', 'EMP1.Employee_Id')
                .leftJoin(ehrTables.jobPostRecruiters + " as JPR", 'JPR.Job_Post_Id', 'JP.Job_Post_Id')
                .leftJoin(ehrTables.empPersonalInfo + " as EMP2", 'EMP2.Employee_Id', 'JPR.Recruiter_Id')
                .leftJoin(ehrTables.empJob + " as EJ2", 'EJ2.Employee_Id', 'EMP2.Employee_Id')
                .whereIn('cu.Status', ['Finished','Withdrawn','Rejected'])
                .groupBy('personal.Candidate_Id')
                .modify(function(){
                    if(spResult && !spResult.isAdmin){
                        this.whereIn('personal.Candidate_Id', spResult.candidateIds)
                    }
                })
                .then(async (candidates) => {
                    let adminEmails = [];
                    if (candidates) {
                        candidates = JSON.stringify(candidates)
                        
                        adminEmails = await organizationDbConnection(ehrTables.rolesBasedOnAccessControl+' as RC')
                        .pluck('EJ.Emp_Email').innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Roles_Id', 'RC.Roles_Id')
                        .where('RC.Form_Id', formIds.admin).andWhere('RC.Role_Update', 1)
                        .whereNotNull('EJ.Emp_Email').whereNot('EJ.Emp_Email', '').andWhere('EJ.Emp_Status', 'Active');
    
                    }

                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Candidate details retrieved successfully", listCandidates: candidates, adminEmail: adminEmails }
                })
                .catch((e) => {
                    // Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    console.log('Error in listCandidateDetails function .catch block.', e);
                    const errResult = commonLib.func.getError(e, 'IO0131');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listCandidateDetails function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'IO0029');
        throw new ApolloError(errResult.message, errResult.code);
    }
};
