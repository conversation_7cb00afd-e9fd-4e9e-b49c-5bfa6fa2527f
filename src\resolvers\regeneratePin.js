const resolvers = {
    Mutation : {
        regeneratePin : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var randomize = require('randomatic');
            var { ApolloError } = require('apollo-server-lambda');
            var cName = await dbConnection.getCompanyName(args.Org_Code);
            var moment = require('moment-timezone');
            // require retrieves3FileUrl to get report logo
            const getS3Path = require('./formS3ObjectUrl');
            // Get organization report logo from org_details
            var reportLogoFileUrl = await getS3Path.getReportLogo(args.Org_Code,knexconfig);
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url',args.Url_Hash)
                .then((newData)=>{
                    if(!newData[0]){
                        throw new Error('REO0101')
                    } else {
                        var pin = randomize('0', 6);
                        let expire_time;
                        if (newData[0].URL_Expiry_Duration_Measure == 'Minutes') {
                            expire_time = moment.utc().add(newData[0].URL_Expiry_Duration, 'minutes').format('YYYY-MM-DD HH:mm:ss');
                        } else if (newData[0].URL_Expiry_Duration_Measure == 'Hours') {
                            expire_time = moment.utc().add(newData[0].URL_Expiry_Duration, 'hours').format('YYYY-MM-DD HH:mm:ss');
                        } else {
                            expire_time = moment.utc().add(newData[0].URL_Expiry_Duration, 'days').format('YYYY-MM-DD HH:mm:ss');
                        }
                        return knexconfig('candidate_url')
                        .where('Url',args.Url_Hash)
                        .update({
                            pin: pin,
                            Expire_Time: expire_time,
                            Updated_At: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                            Updated_By: args.Employee_Id
                        })
                        .transacting(trc)
                        .then(()=>{
                            return knexconfig('candidate_sent_mails')
                            .where('Url_Hash',newData[0].Url)
                            .then(async(sentUrl)=>{
                                if(!sentUrl[0]) {
                                    return {
                                        message: "PIN Regenerated"
                                    }
                                } else {
                                    const AWS = require("aws-sdk");
                                    AWS.config.update({
                                    region: process.env.sesTemplatesRegion
                                    });
                                    const ses = new AWS.SES({
                                        apiVersion: "2010-12-01"
                                    });
                                    let buttonColors=await commonLib.func.getButtonColor(organizationDbConnection);
                                    const templateData = {
                                        companyName: cName,
                                        url: args.Url_Hash,
                                        pin: pin,
                                        orgLogo: reportLogoFileUrl,
                                        bgColors:buttonColors,
                                    }
                                        
                                    try{
                                        const params = {
                                            "Source": process.env.emailFrom,
                                            "Template": "selfOnboardSendPin",
                                            "Destination": {
                                                "ToAddresses": [sentUrl[0].Email]
                                            },
                                            "ReplyToAddresses": [process.env.emailReplyTo],
                                            "TemplateData": JSON.stringify(templateData)
                                        }

                                        let response = await ses.sendTemplatedEmail( params ).promise()

                                        console.log('response',response)
                                    }
                                    catch(err){
                                        console.log("Mail not sent",err)
                                    }
                                        
                                    return {
                                        message: "PIN Regenerated"
                                    }
                                }
                            })
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return knexconfig('candidate_url')
                .where('Url',args.Url_Hash)
                .then((data)=>{
                    console.log('return successful response from regeneratePin');
                    return data[0];
                })
            }).catch(function(err){
                console.log('Error in regeneratePin',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;