//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
let moment = require('moment-timezone');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');
const{updatePerformanceRatingsValidation}=require('../common/inputValidation')


//function to update performance ratings
module.exports.updatePerformanceRatings = async (parent, args, context, info) => {
    console.log('Inside updatePerformanceRatings function');
    let organizationDbConnection;
    let validationError={};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        validationError=await updatePerformanceRatingsValidation(args);
        if(Object.keys(validationError).length===0){
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            return (
                organizationDbConnection(ehrTables.serviceProvider)
                    .update('Performance_Ratings', args.performanceRatings)
                    .where('Service_Provider_Id', args.vendorId)
                    .then((vendorId) => {
                        if(vendorId){
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Performance ratings for the vendor has been updated successfully." };
                        }else{
                            throw 'VO0130';
                        }
                    })
                    .catch(catchError => {
                        console.log('Error while fetching workflow Id', catchError);
                        let errResult = commonLib.func.getError(catchError, 'EM0267');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return error response
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        } else {
            console.log('No rights to update the vendor performance ratings');
            throw '_DB0111';
        }
    }else{
        throw 'IVE0000';
    }
    } catch (mainCatchError) {
        console.log('Error in updatePerformanceRatings function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in checkVendorStatus function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }else{
            errResult = commonLib.func.getError(mainCatchError, 'VO0018');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

