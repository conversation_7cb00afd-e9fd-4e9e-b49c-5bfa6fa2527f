const resolvers = {
    Mutation : {
        migrateDocuments : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            let Candidate_Id = args.Candidate_Id;
            return knexconfig.transaction(function(trc){
                return knexconfig.select('candidate_document_category.Document_Id',
                'candidate_document_category.Candidate_Id as Employee_Id',
                // 'candidate_document_category.Category_Id',
                // 'candidate_document_category.Document_Type_Id',
                'candidate_document_category.Sub_Document_Type_Id',
                'candidate_document_category.Document_Name',
                'candidate_document_category.Added_On',
                'candidate_document_category.Lock_Flag')
                .from('candidate_document_category')
                .where('Candidate_Id',Candidate_Id)
                .then((documents)=>{
                    if(!documents[0]){
                        return {
                            message:"No documents to migrate"
                        }
                    } else {
                        return knexconfig.select('candidate_documents.Document_Id',
                        'candidate_documents.File_Name',
                        'candidate_documents.File_Size'
                        )
                        .from('candidate_documents')
                        .then(async (data)=>{
                            let documentsLength = documents.length;
                            for( var i = 0; i < documentsLength; i++){
                                //frame the 'documents' variable to insert into  'emp_document_category' table
                                documents[i].Employee_Id = args.Employee_Id;

                                documents[i].Added_By = args.Employee_Id;

                                documents[i].Document_Sub_Type_Id = documents[i].Sub_Document_Type_Id;
                                
                                let DocumentId = documents[i].Document_Id;
                                
                                console.log('DocumentId',DocumentId);
                                
                                delete documents[i].Document_Id;
                                delete documents[i].Sub_Document_Type_Id;

                                const recordId = await knexconfig.insert(documents[i]).into('emp_document_category');

                                //frame the 'documentDetails' variable to insert into 'emp_documents' table
                                var documentDetails = data.filter(details => (details.Document_Id == DocumentId));

                                let documentDetailsLength = documentDetails.length;

                                for( var j = 0; j < documentDetailsLength; j++){
                                    documentDetails[j].Document_Id = recordId;
                                }

                                const dataId = await knexconfig.insert(documentDetails).into('emp_documents');
                            }
                            return{
                                message: "Documents migrated"
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from migrateDocuments');
                return result;
            }).catch(function(err){
                console.log('Error in addDocument',err);
                if (err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;