'use strict';
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { formName } = require('../common/appConstants');

// Function to initiate triggerResendInviteVendor step function
module.exports.triggerResendInviteVendor  = async(parent,args, context) =>{
    let organizationDbConnection;
    try{
        console.log('Inside triggerResendInviteVendor function',args);
        let orgCode = context.Org_Code
        let loginEmployeeId = context.Employee_Id;
        // We will be triggering the step function to run resend invite vendor in background process.
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) 
        {
            let inputParams={'orgCode':orgCode,'logInEmpId':loginEmployeeId,vendorIds:args.vendorIds}
            let triggerResendInviteVendorResponse= await commonLib.stepFunctions.triggerStepFunction(process.env.resendInviteVendorStepFunction,'triggerResendInviteVendor','',inputParams);
            console.log('Response after triggering triggerResendInviteVendor step function',triggerResendInviteVendorResponse);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return {errorCode:'',message: 'ResendInviteVendor initiated successfully.'};
        }
        else{
            console.log('No rights to resend invite to the vendor');
            throw '_DB0111';
        }
    }
    catch(mainCatchError){
        console.log('Error in triggerResendInviteVendor function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'VO0015');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
};