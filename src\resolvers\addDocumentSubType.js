const resolvers = {
    Mutation : {
        addDocumentSubType : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);

            return knexconfig.transaction(function(trc){
                return knexconfig("document_sub_type")
                .insert({
                    Document_Type_Id: args.Document_Type_Id,
                    Document_Sub_Type: args.Sub_Document_Type_Name,
                    Added_By: args.Employee_Id,
                    Added_On: new Date()
                })
                .transacting(trc)
                .then(()=>{
                    return {
                        message: "Sub type added"
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return result;
            }).catch(function(err){
                console.log('Error in addDocumentSubType',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;