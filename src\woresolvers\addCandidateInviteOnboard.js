//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex')
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { getLocationDetails, encodeUsingBase64, sendEmail, sendCustomEmail, getSenderName } = require('../common/commonFunctions');
const { ehrTables } = require('../common/tableAlias');
const { formIds } = require('../common/appConstants');
const SHA3 = require('sha3');
var randomize = require('randomatic');
var moment = require('moment-timezone');
const getS3Path = require('../resolvers/formS3ObjectUrl');
var dbConnection = require('../resolvers/dbConnection');

module.exports.addCandidateInviteOnboard = async (parent, args, context, info) => {
    console.log('Inside addCandidateInviteOnboard function');
    let organizationDbConnection;
    try {

        let loginEmployeeId = context.Employee_Id;
        let orgCode = context.Org_Code;

        organizationDbConnection = knex(context.connection.OrganizationDb);

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);
        if ((Object.keys(checkRights).length === 0 || (checkRights.Role_Add === 0 && checkRights.Role_Update === 0))) {
            throw '_DB0111';
        }

        let appManagerDbConnection = knex(context.connection.AppManagerDb);
        let regionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection, orgCode);
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;

        if (Object.keys(regionDetails).length === 0) {
            throw '_DR0001';
        }
        
        let locationDetails = await getLocationDetails(organizationDbConnection, args.Location_Id);
        let countryCode;

        if (locationDetails && locationDetails.length > 0 && locationDetails[0]['Country_Code']) {
            countryCode = locationDetails[0]['Country_Code'];
        } else {
            throw 'SET0006';
        }

        var cName = await dbConnection.getCompanyName(orgCode);
        var companyName = cName ? encodeURIComponent(cName) : "";
        let hash,baseUrl,fullURL,index,pin;
        let loggedInEmployeeIdEmail = loginEmployeeId ? await organizationDbConnection(ehrTables.empJob).select('Emp_Email').where('Employee_Id', loginEmployeeId).first() : null;
        var candidateId;
        return organizationDbConnection
          .transaction(async function (trx) {
            if (!args.candidateId) {
              let personalId = await trx(
                ehrTables.candidatePersonalInfo
              ).insert({
                Emp_First_Name: args.Name,
                Personal_Email: args.MailTo,
                Source_Type: "EmployeeOnboard",
              });
              candidateId = personalId[0];
            } else {
              await trx(ehrTables.candidatePersonalInfo)
                .update({ Personal_Email: args.MailTo })
                .where("Candidate_Id", args.candidateId);
              candidateId = args.candidateId;
            }
            if (!candidateId) {
              console.log("candidate id not found");
              throw "_UH0001";
            }
            hash = new SHA3.SHA3Hash()
              .update(" " + Date.now() + candidateId)
              .digest("hex");
            baseUrl = process.env.baseAUUrlUI;

            fullURL = baseUrl + companyName + "&country=" + countryCode.toLowerCase() +
              "&code=" + orgCode + "&identifier=" + hash + "&d_code=" + regionDetails.Data_Region +
              "&b_code=" + regionDetails.Bucket_Region;

            index = fullURL.indexOf("?");
            fullURL = fullURL.substr(0, index) + "?" + (await encodeUsingBase64(fullURL.substr(index + 1)));
            pin = randomize("?", 6, { chars: "*********" });
            args.Job_Role_Ids = Array.isArray(args.Job_Role_Ids) && args.Job_Role_Ids.length ? JSON.stringify(args.Job_Role_Ids) : null;
            let urlData = {
              Url_Hash: hash,
              Url: fullURL,
              Pin: pin,
              Expire_Time: "",
              Candidate_Id: candidateId,
              Designation_Id: args.Designation_Id,
              Department_Id: args.Department_Id,
              Location_Id: args.Location_Id,
              Job_Code: args.Job_Code,
              Job_Role_Ids: args.Job_Role_Ids,
              Date_Of_Join: args.Date_Of_Join,
              Probation_Date: args.Probation_Date,
              EmpType_Id: args.EmpType_Id,
              Manager_Id: args.Manager_Id,
              Work_Schedule: args.Work_Schedule,
              Service_Provider_Id:
                args.Service_Provider_Id && args.Service_Provider_Id > 0
                  ? args.Service_Provider_Id
                  : null,
              Business_Unit_Id:
                args.Business_Unit_Id && args.Business_Unit_Id > 0
                  ? args.Business_Unit_Id
                  : null,
              URL_Expiry_Duration_Measure: "",
              URL_Expiry_Duration: args.Expire_Value,
              Created_At: moment.utc().format("YYYY-MM-DD HH:mm:ss"),
              Created_By: loginEmployeeId,
              Name: args.Name,
              Email: args.MailTo,
              Organization_Group_Id: args.organizationGroupId,
            };

            if (args.Expire_Type === 1) {
              urlData.URL_Expiry_Duration_Measure = "Minutes";
              urlData.Expire_Time = moment
                .utc()
                .add(args.Expire_Value, "minutes")
                .format("YYYY-MM-DD HH:mm:ss");
            } else if (args.Expire_Type === 2) {
              urlData.URL_Expiry_Duration_Measure = "Hours";
              urlData.Expire_Time = moment
                .utc()
                .add(args.Expire_Value, "hours")
                .format("YYYY-MM-DD HH:mm:ss");
            } else {
              urlData.URL_Expiry_Duration_Measure = "Days";
              urlData.Expire_Time = moment
                .utc()
                .add(args.Expire_Value, "days")
                .format("YYYY-MM-DD HH:mm:ss");
            }
            let candidateJobData = {
              Candidate_Id: candidateId,
              Designation_Id: args.Designation_Id,
              Department_Id: args.Department_Id,
              Location_Id: args.Location_Id,
              Date_Of_Join: args.Date_Of_Join,
              Job_Code: args.Job_Code,
              Job_Role_Ids: args.Job_Role_Ids,
              Probation_Date: args.Probation_Date,
              EmpType_Id: args.EmpType_Id,
              Manager_Id: args.Manager_Id,
              Work_Schedule: args.Work_Schedule,
              Service_Provider_Id:
                args.Service_Provider_Id && args.Service_Provider_Id > 0
                  ? args.Service_Provider_Id
                  : null,
              Business_Unit_Id:
                args.Business_Unit_Id && args.Business_Unit_Id > 0
                  ? args.Business_Unit_Id
                  : null,
              Organization_Group_Id: args.organizationGroupId,
            };

            let canidateQuery = [
              trx(ehrTables.candidateUrl).insert(urlData),
              args.candidateId
                ? null
                : trx(ehrTables.candidateContact).insert({
                    Candidate_Id: candidateId,
                  }),
              trx(ehrTables.candidateHobbies).insert({
                Candidate_Id: candidateId,
              }),
              trx(ehrTables.candidateJob).insert(candidateJobData),
              args.candidateId
                ? trx(ehrTables.candidateRecruitmentInfo)
                    .update({ Candidate_Status: 23 })
                    .where("Candidate_Id", candidateId)
                : null,
              args.groupIds && args.groupIds.length
                ? trx(ehrTables.candidateDocumentEnforcementGroups).insert(
                    args.groupIds.map((groupId) => {
                      return { Group_Id: groupId, Candidate_Id: candidateId };
                    })
                  )
                : null,
              args.accreditationGroupIds && args.accreditationGroupIds.length
                ? trx(ehrTables.candidateAccreditationGroups).insert(
                  args.accreditationGroupIds.map((groupId) => {
                    return { Group_Id: groupId, Candidate_Id: candidateId };
                  })
                )
                : null,
            ];

            let systemLogParam = { 
              action: 'Invited', 
              userIp: context.User_Ip, 
              employeeId: loginEmployeeId, 
              formId:  formIds.individuals,
              organizationDbConnection: trx,
              isEmployeeTimeZone: 0,
              changedData: args,
              uniqueId: candidateId, 
              message: `The candidate has been invited to the onboarding process.` 
          };
          // Call the function to add the system log
          await commonLib.func.createSystemLogActivities(systemLogParam);

            return Promise.all(canidateQuery)
              .then(async (results) => {
                return "success";
              })
              .catch((error) => {
                // This block executes if any promise rejects
                console.error(
                  "Error in addCandidateInviteOnboard function candidate Promise rejection error",
                  error
                );
                throw error;
              });
          })
          .then(async () => {
            const defaultTemplate = await commonLib.func.getDefaultTemplate(
              {
                categoryId: 16,
                formId:178
              },
              organizationDbConnection
            );
            if (defaultTemplate && Object.keys(defaultTemplate).length) {
              const templateId = defaultTemplate.Template_Id;
              let eventVal = {
                Source: process.env.emailFrom,
                ReplyToAddresses: [process.env.emailReplyTo],
                bucketName : process.env.documentsBucket,
                region:process.env.region
              };
              const { emailResult, ses, event } =
                await commonLib.func.listEmailTemplatePlaceHolderValues(
                  { templateId, candidateId },
                  organizationDbConnection,
                  context,
                  eventVal,
                  []
                );
              let inviteStatus = await sendCustomEmail(emailResult, ses, event, null, organizationDbConnection);
              organizationDbConnection
                ? organizationDbConnection.destroy()
                : null;

              if (inviteStatus && inviteStatus.toLowerCase() === "invited") {
                return {
                  errorCode: "",
                  message:
                    "Invite to a candidate has been mail sent successfully",
                };
              } else {
                console.log(
                  "Error in sendEmailStatus in convertCandidateToEmployee"
                );
                throw "PBP0105";
              }
            }
          var reportLogoFileUrl = await getS3Path.getReportLogo(orgCode, organizationDbConnection);
          let buttonColors=await commonLib.func.getButtonColor(organizationDbConnection);
          const bucketName = process.env.documentsBucket
          const region=process.env.region;
          let fileNameString=await commonLib.func.getDocumentSubType(organizationDbConnection,context.Org_Code,bucketName,region);
          let originalSenderName = null;
          let result = await getSenderName(organizationDbConnection);
          originalSenderName = result?.senderName;
          const templateData = {
              employeeName: args.Name,
              name: args.Name,
              redirectionUrl: fullURL,
              passCode: pin,
              orgLogo: reportLogoFileUrl,
              topCardImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + '/Email-Template-Images/topCard.png',
              domainName: process.env.domainName.toUpperCase(),
              domainContentInSubject: "Hello",
              registrationImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + '/Email-Template-Images/RegistrationImage.png',
              notificationSubject: "Invitation to join " + cName,
              isEmailClient: 0,
              isVendorBased: 0,
              isCandidate: 1,
              orgCode: cName?cName:'',
              bgColors:buttonColors?buttonColors:'',
              docContent:fileNameString?'Below is the link to the list of pre-employment requirements and the hiring forms you need to accomplish.':'',
              docLink:fileNameString?fileNameString:'',
          }

          const params = {
              "Source": originalSenderName ? `${originalSenderName} <${process.env.emailFrom}>` : process.env.emailFrom,
              "Template": "welcomeEmailToClient",
              "Destination": {
              "ToAddresses": [args.MailTo]
              },
              "ReplyToAddresses": loggedInEmployeeIdEmail.Emp_Email ? [loggedInEmployeeIdEmail.Emp_Email] : [],
              "TemplateData": JSON.stringify(templateData)
          }
      
          let emailResponse = await sendEmail(params);
          if (emailResponse) {
              return "success";
          } else {
              throw 'PBP0105';
          }
          })
          .catch((err) => {
            console.error(
              "Error in addCandidateInviteOnboard function block",
              err
            );
            organizationDbConnection
              ? organizationDbConnection.destroy()
              : null;
            let errResult = commonLib.func.getError(err, "_UH0001");
            throw new ApolloError(errResult.message, errResult.code);
          });

    } catch(err){
        
        console.error('Error in inviteCandidateOnboard function main block', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, '_UH0001');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }

}