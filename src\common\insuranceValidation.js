//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

//Function to validate insurance details

module.exports.insuranceValidation = async (args) => {
    console.log('Inside insuranceValidation function');
    let descriptionValidation = /^[\w\s\d.,!@%^&*-_+=;:'"<>?/\()|`~’\n]+$/
    try {
        let validationError = {};
            //Validate the vendor id
            if (!(args.vendorId || commonLib.commonValidation.numberValidation(args.vendorId)) || args.vendorId < 1) {
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message1;
                throw '_EC0007'
            }

        //Validate the insurance
        if (args.typeOfInsurance) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.typeOfInsurance)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message1;
            } 
            else if (!commonLib.commonValidation.checkLength(args.typeOfInsurance, 3, 50)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message3;
            }
        } else {
            validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message2;
        }

        //Validate the insurer name 
        if (args.nameOfInsurer) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.nameOfInsurer)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message5;
            }
            else if (!commonLib.commonValidation.checkLength(args.nameOfInsurer, 3, 300)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message6;
            }
        }
        else {
            validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message4;
        }

        //Validate the insured name 

        if (args.nameOfInsured) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.nameOfInsured)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message8;
            }
            else if (!commonLib.commonValidation.checkLength(args.nameOfInsured, 3, 300)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message9;
            }
        }
        else {
            validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message7;
        }

        //Validate the policy number
        if (args.policyNumber) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.policyNumber)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message10;
            }
            else if (!commonLib.commonValidation.checkLength(args.policyNumber, 3, 30)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message11;
            }
        }
        else {
            validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message12;
        }

        //Validate the sum insured
        if (args.sumInsured) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.sumInsured)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message13;
            }
            else if (!commonLib.commonValidation.checkLength(args.sumInsured, 2, 30)) {
                validationError['IVE0254'] = commonLib.func.getError('', 'IVE0254').message14;
            }
        }

        //Validate the description
        if(args.description){
            if(!descriptionValidation.test(args.description)){
                validationError['IVE0062'] = commonLib.func.getError('', 'IVE0062').message1;
            }
            if (!commonLib.commonValidation.checkLength(args.description, 1, 1000)) {
                validationError['IVE0062'] = commonLib.func.getError('', 'IVE0062').message2;
            }
        }

        return validationError;
    }
    catch (err) {
        console.log('Error in the insuranceValidation() function in the main catch block.', err);
        throw err;
    }
}
