const resolvers = {
    Query : {
        getCertification : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');
            var dateFormat = require('dateformat');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig.select('candidate_certifications.*',
                                                'candidate_certifications_documents.File_Name',
                                                'candidate_certifications_documents.Document_Name',
                                                'candidate_certifications_documents.Sub_Type_Id',
                                                'document_sub_type.Document_Sub_Type')
                        .from('candidate_certifications')
                        .leftJoin('candidate_certifications_documents','candidate_certifications.Certification_Id','candidate_certifications_documents.Certification_Id')
                        .leftJoin('document_sub_type','candidate_certifications_documents.Sub_Type_Id','document_sub_type.Document_Sub_Type_Id')
                        .where('candidate_certifications.Candidate_Id',url[0].Candidate_Id)
                        .then((certificates)=>{
                            if(!certificates[0]){
                                return {
                                    certificates: null
                                }
                            } else {
                                var newCertificates = certificates.map((arr)=>{ 
                                    arr['Received_Date']  = dateFormat(new Date(arr['Received_Date']), "yyyy-mm-dd");
                                    return arr;
                                });
                                return {
                                    certificates: newCertificates
                                }
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return result;
            }).catch(function(err){
                console.log('Error in getCertification',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;