//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs, validateDateOfJoinAndDOB } = require('../../common/commonFunctions')

//function to update job details
module.exports.updateJobDetails = async (parent, args, context, info) => {
    console.log('Inside updateJobDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            const fieldValidations = {
                empEmail: 'IVE0397'
            };
            validationError = validateCandidateInputs({ empEmail: args.Emp_Email }, fieldValidations);
            validationError = await validateAlreadyExists(organizationDbConnection, args, validationError);
            validationError = await validateDateOfJoin(organizationDbConnection, args, validationError);
            if (Object.keys(validationError).length == 0) {
                return (
                    organizationDbConnection
                        .transaction(async function (trx) {
                            args.Job_Role_Ids = Array.isArray(args.Job_Role_Ids) && args.Job_Role_Ids.length ? JSON.stringify(args.Job_Role_Ids) : null;
                            return (
                                organizationDbConnection(ehrTables.candidateJob)
                                    .update(args)
                                    .where('Candidate_Id', args.Candidate_Id)
                                    .transacting(trx)
                                    .then(async (data) => {
                                        return data
                                    })
                                    .catch((catchError) => {
                                        console.log('Error in updateJobDetails .catch block', catchError);
                                        throw catchError
                                    })
                            )

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.Candidate_Id,
                                    message: `The candidate job details has been updated.`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);
                                organizationDbConnection ? organizationDbConnection.destroy() : null;

                                return { errorCode: "", message: "Candidate job details was updated successfully." };
                            } else {
                                throw 'IO0108'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in updateJobDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0108');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to update the job details');
            throw '_DB0102';
        }
    } catch (mainCatchError) {
        console.log('Error in updateJobDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in updateJobDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0009');
            // return response
            throw new ApolloError(errResult.message, errResult.code,);
        }
    }
}

async function validateAlreadyExists(organizationDbConnection, args, validationError) {
    try {
        return (
            organizationDbConnection(ehrTables.empJob)
                .select('*')
                .where(function () {
                    if (args.Emp_Email && args.Emp_Email.length) {
                        this.orWhere('Emp_Email', args.Emp_Email)
                    }
                    if (args.Pf_PolicyNo && args.Pf_PolicyNo.length) {
                        this.orWhere('Pf_PolicyNo', args.Pf_PolicyNo)
                    }
                })
                .where('Emp_Status', 'Active')
                .then((data) => {
                    let pf = [], email = []
                    if (data && data.length) {
                        data.forEach(element => {
                            if (args.Pf_PolicyNo && args.Pf_PolicyNo.length && element.Pf_PolicyNo === args.Pf_PolicyNo) {
                                pf.push(element.User_Defined_EmpId)
                            }
                            if (args.Emp_Email && args.Emp_Email.length && element.Emp_Email === args.Emp_Email) {
                                email.push(element.User_Defined_EmpId)
                            }
                        });

                        if (pf.length) {
                            validationError['IVE0426'] = commonLib.func.getError('', 'IVE0426').message + ' ' + pf.join(',');
                        }
                        if (email.length) {
                            validationError['IVE0430'] = commonLib.func.getError('', 'IVE0430').message + ' ' + email.join(', ');
                        }

                    }
                    return validationError
                })
                .catch((err) => {
                    console.log('Error in validateAlreadyExists .catch', err)
                    throw err
                })
        )

    } catch (err) {
        console.log('Error in validateAlreadyExists main catch', err)
        throw err
    }
}

async function validateDateOfJoin(organizationDbConnection, args, validationError) {
    const getPersonalInfoData = await organizationDbConnection(ehrTables.candidatePersonalInfo)
    .where('Candidate_Id', args.Candidate_Id).first();
    if(!validateDateOfJoinAndDOB(getPersonalInfoData.DOB, args.Date_Of_Join)){
        validationError['IVE0605'] = commonLib.func.getError('', 'IVE0605').message
    }
    return validationError;
}