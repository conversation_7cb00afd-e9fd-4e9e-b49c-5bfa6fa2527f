const resolvers = {
    Mutation : {
        deleteCandidateRelation : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            var Candidate_Id;
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        Candidate_Id = url[0].Candidate_Id;
                        return knexconfig('candidate_relation_details')
                        .where('Relation_Id',args.Relation_Id)
                        .andWhere('Candidate_Id', Candidate_Id)
                        .then((relation)=>{
                            if(!relation[0]){
                                throw new Error("Relation not found");
                            } else {
                                return knexconfig('candidate_relation_details')
                                .where('Relation_Id', args.Relation_Id)
                                .del()
                                .transacting(trc)
                                .then(()=>{
                                    return {
                                        message:"Relation details deleted"
                                    }
                                })
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return knexconfig('candidate_relation_details')
                .where('Candidate_Id',Candidate_Id)
                .then((relation)=>{
                    if(!relation[0]){
                        return {
                            relation: null
                        }
                    } else {
                        return {
                            relation: relation
                        }
                    }
                })
            }).catch(function(err){
                console.log('Error in deleteCandidateRelation ', err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Relation not found"){
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;