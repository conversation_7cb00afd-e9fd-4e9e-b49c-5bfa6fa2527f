const resolvers = {
    Mutation: {
        updateExperience: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var dateFormat = require('dateformat');
            var calculateFullAge = require('full-age-calculator');
            var { ApolloError, UserInputError } = require('apollo-server-lambda');


            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_url')
                    .where('Url_Hash', args.Url_Hash)
                    .then((url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } else {
                            return knexconfig('candidate_experience')
                                .where('Experience_Id', args.Experience_Id)
                                .andWhere('Candidate_Id', url[0].Candidate_Id)
                                .then((details) => {
                                    if (!details[0]) {
                                        throw new Error("Experience Detail not found");
                                    } else {
                                        if (!args.Prev_Company_Name) {
                                            throw new Error('Company Name should not be empty');
                                        } else if (args.Prev_Company_Name.length < 3 || args.Prev_Company_Name.length > 50) {
                                            throw new Error('Company Name should be between 3 and 50 characters');
                                        } else if (!args.Prev_Company_Location) {
                                            throw new Error('Location should not be empty');
                                        } else if (args.Prev_Company_Location.length < 3 || args.Prev_Company_Location.length > 50) {
                                            throw new Error('Location should be between 3 and 50 characters');
                                        } else if (!args.Designation) {
                                            throw new Error('Designation should not be empty');
                                        } else if (args.Designation.length < 3 || args.Designation.length > 50) {
                                            throw new Error('Designation should be between 3 and 50 characters');
                                        } else if (!args.Start_Date) {
                                            throw new Error('Start Date should not be empty');
                                        } else if (!args.End_Date) {
                                            throw new Error('End Date should not be empty');
                                        }
                                        try {
                                            var Start_Date = (!args.Start_Date || isNaN(Date.parse(args.Start_Date))) ? (null) : ((isNaN(Number(args.Start_Date))) ? dateFormat(new Date((args.Start_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Start_Date)), "yyyy-mm-dd"));
                                            var End_Date = (!args.End_Date || isNaN(Date.parse(args.End_Date))) ? (null) : ((isNaN(Number(args.End_Date))) ? dateFormat(new Date((args.End_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.End_Date)), "yyyy-mm-dd"));
                                        }
                                        catch (err) {
                                            throw new Error("Invalid Date Format")
                                        }
                                        var Duration = '';
                                        if (End_Date) {
                                            var date = calculateFullAge.getFullAge(Start_Date, End_Date)
                                            if (date.years !== 0) {
                                                Duration = Duration + date.years + ' years';
                                            }
                                            if (date.months !== 0) {
                                                Duration = Duration + ' ' + date.months + ' months';
                                            }
                                            if (date.days !== 0) {
                                                Duration = Duration + ' ' + date.days + ' days';
                                            }
                                        }
                                        return knexconfig('candidate_experience')
                                            .where('Experience_Id', args.Experience_Id)
                                            .andWhere('Candidate_Id', url[0].Candidate_Id)
                                            .update({
                                                Prev_Company_Name: args.Prev_Company_Name,
                                                Prev_Company_Location: args.Prev_Company_Location,
                                                Designation: args.Designation,
                                                Start_Date: Start_Date,
                                                End_Date: End_Date,
                                                Duration: Duration,
                                                Years: date.years,
                                                Months: date.months,
                                            })
                                            .transacting(trc)
                                            .then(() => {
                                                return knexconfig('candidate_experience_documents')
                                                    .where('Experience_Id', args.Experience_Id)
                                                    .then(async (document_exist) => {
                                                        await updateExperienceReference(knexconfig, args, trc);
                                                        if (!document_exist[0]) {
                                                            if (!args.File_Name) {
                                                                return {
                                                                    message: "Experience details updated"
                                                                }
                                                            } else {
                                                                return knexconfig('candidate_experience_documents')
                                                                    .insert({
                                                                        Experience_Id: args.Experience_Id,
                                                                        File_Name: args.File_Name
                                                                    })
                                                                    .transacting(trc)
                                                                    .then(() => {
                                                                        return {
                                                                            message: "Experience details updated"
                                                                        }
                                                                    })
                                                            }
                                                        } else {
                                                            if (!args.File_Name) {
                                                                return knexconfig('candidate_experience_documents')
                                                                    .where('Experience_Id', args.Experience_Id)
                                                                    .del()
                                                                    .transacting(trc)
                                                                    .then(() => {
                                                                        return {
                                                                            message: "Experience details updated"
                                                                        }
                                                                    })
                                                            } else {
                                                                return knexconfig('candidate_experience_documents')
                                                                    .where('Experience_Id', args.Experience_Id)
                                                                    .update({
                                                                        File_Name: args.File_Name
                                                                    })
                                                                    .transacting(trc)
                                                                    .then(() => {
                                                                        return {
                                                                            message: "Experience details updated"
                                                                        }
                                                                    })
                                                            }
                                                        }

                                                    })
                                            })
                                    }
                                })
                        }
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                console.log('return successful response from updateExperience');
                return result;
            }).catch(function (err) {
                console.log('Error in updateExperience', err);
                if (err.message == "REO0101") {
                    console.log('URL not found')
                    throw new ApolloError("URL not found", "REO0101")
                } else if (err == "ESS0156") {
                    throw new UserInputError("Each employee reference must have unique email and number details. Please enter unique information.")
                } else if (err.message == "Company Name should not be empty") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Company Name should be between 3 and 50 characters") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Location should not be empty") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Location should be between 3 and 50 characters") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Designation should not be empty") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Designation should be between 3 and 50 characters") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Start Date should not be empty") {
                    throw new UserInputError(err.message)
                } else if (err.message == "End Date should not be empty") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Invalid Date Format") {
                    throw new UserInputError(err.message)
                } else if (err.message == "Experience Detail not found") {
                    throw new Error(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}

async function updateExperienceReference(knexconfig, args, trc) {
    try {
        //Remove Existing Reference
        await knexconfig('candidate_experience_reference')
            .where('Experience_Id', args.Experience_Id)
            .transacting(trc)
            .del()
        if (args.Reference_Details?.length) {

            args.Reference_Details.forEach(element => {
                element.Experience_Id = args.Experience_Id
            });

            const duplicates = args.Reference_Details.filter((item, index, self) => {
                // Ignore entries with null or empty values
                if (!item.Reference_Email && !item.Reference_Number) {
                    return false;
                }

                // Check for duplicates based on Reference_Email or Reference_Number
                return self.findIndex(obj =>
                    (obj.Reference_Email === item.Reference_Email && obj.Reference_Email) ||
                    (obj.Reference_Number === item.Reference_Number && obj.Reference_Number)
                ) !== index;
            });

            if (duplicates?.length) {
                throw 'ESS0156'
            }

            await knexconfig('candidate_experience_reference')
                .insert(args.Reference_Details)
                .transacting(trc)
        }
        return true
    }
    catch (err) {
        console.log('Error in updateExperienceReference', err);
        throw err
    }
}

exports.resolvers = resolvers;