//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')

//function to add / update certification details
module.exports.addUpdateCertificationDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateCertificationDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const fieldValidations = {
                certificationName: "IVE0613",
                receivedFrom: "IVE0614"
            }
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let certificationData = {
                    Candidate_Id: args.candidateId,
                    Certification_Name: args.certificationName,
                    Received_Date: args.receivedDate,
                    Ranking: args.ranking || null,
                    Certificate_Received_From: args.receivedFrom,
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (args.certificationId) {
                                return (
                                    organizationDbConnection(ehrTables.candidateCertifications)
                                        .update(certificationData)
                                        .transacting(trx)
                                        .where('Certification_Id', args.certificationId)
                                        .then(async (updateData) => {
                                            if (updateData) {
                                                await organizationDbConnection(ehrTables.candidateCertificationsDocuments)
                                                    .update({ 'File_Name': args.fileName, 'Sub_Type_Id': args.documentSubTypeId, 'Document_Name': args.fileName })
                                                    .where('Certification_Id', args.certificationId)
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the certification details', certificationData)
                                                throw 'IO0118'
                                            }
                                        })
                                )
                            } else {
                                return (
                                    organizationDbConnection(ehrTables.candidateCertifications)
                                        .insert(certificationData)
                                        .transacting(trx)
                                        .then(async (insertData) => {
                                            if (insertData) {
                                                await organizationDbConnection(ehrTables.candidateCertificationsDocuments)
                                                    .insert({ 'Certification_Id': insertData, 'File_Name': args.fileName, 'Sub_Type_Id': args.documentSubTypeId, 'Document_Name': args.fileName })

                                                return 'success'
                                            } else {
                                                console.log('Error while updating the certification details', certificationData)
                                                throw 'IO0118'
                                            }
                                        })
                                        .catch((err) => {
                                            console.log('Error while updating the certification details', err)
                                            throw 'IO0118'
                                        })
                                )
                            }

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The candidate ${args.certificationName} certification details has been ${args.certificationId ? 'updated' : 'added'}.`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);

                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Candidate Certification details has been added/updated successfully." };
                            } else {
                                throw 'IO0118'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateCertificationDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0118');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )

            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the certification details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateCertificationDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateCertificationDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0017');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}