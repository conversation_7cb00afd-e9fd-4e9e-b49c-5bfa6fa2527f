//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { formName, systemLogs, formIds } = require('../../common/appConstants');
const moment = require('moment-timezone');
const axios = require('axios');
const { getHRGroupEmployeeEmails, sendEmailToCandidateManagerInitiatorHrGroup, sendCustomEmail, incrementNextNumber } = require('../../common/commonFunctions');


//function to clone the inactive employee
module.exports.convertCandidateToEmployee = async (parent, args, context) => {
    console.log('Inside convertCandidateToEmployee function');
    let organizationDbConnection;
    let candidateEmployeeData = args
    let candidateId = candidateEmployeeData.Candidate_Id
    delete candidateEmployeeData.Candidate_Id
    try {
        let loginEmployeeId = context.Employee_Id;
        let orgCode = context.Org_Code;
        let partnerId = context.partnerid
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Add === 1 || checkRights.Role_Update === 1)) {
            const result = await organizationDbConnection
                .transaction(async function (trx) {

                    //Emp Personal Info
                    let getPersonalInfoData = await validateAndInsertPersonalInfo(organizationDbConnection, trx, args, candidateId, candidateEmployeeData)

                    //Emp Job Insertion
                    let getJobDetailsData = await insertIntoJobInfo(organizationDbConnection, trx, candidateId, candidateEmployeeData, context, getPersonalInfoData.Employee_Id)

                    //Direct Run
                    await convertOtherDetails(organizationDbConnection, getPersonalInfoData.Employee_Id, candidateId, getPersonalInfoData, getJobDetailsData, context, trx)

                    //Copy the Custom Field data to the new employee
                    await copyCustomFieldData(organizationDbConnection, getPersonalInfoData.Employee_Id, candidateId, context, trx)

                    await organizationDbConnection(ehrTables.candidateRecruitmentInfo).update({ Candidate_Status: 24 })
                        .where('Candidate_Id', candidateId).transacting(trx);
                    // let inputData = {
                    //     'orgCode': orgCode, 'partnerId': partnerId, 'inputParams': {
                    //         newEmployeeId: getPersonalInfoData.Employee_Id,
                    //         candidateId: candidateId,
                    //         personalInfoData: getPersonalInfoData,
                    //         jobInfoData: getJobDetailsData,
                    //         orgCode: orgCode,
                    //         functionName: 'employeeClone'
                    //     }
                    // }

                    // //Cloning in other tables
                    // await commonLib.stepFunctions.triggerStepFunction(process.env.commonStepFunction, 'commonStepFunction', '', inputData);


                    await addExternalApiIntegrationLog(organizationDbConnection, getJobDetailsData.Employee_Id, candidateId)

                    return {
                        employeeId: getJobDetailsData.Employee_Id
                    }
                })
                .then(async (result) => {
                    let newEmployeeId = result.employeeId;

                    //Custom group refresh
                    await customGroupRefresh([newEmployeeId], context, 1, "", false, false, false);

                    //Calling TrstScore and CamuAPI and adding log for timestamp
                    await addTimeStampInfoLog(organizationDbConnection, context, newEmployeeId);

                    let systemLogParam = { 
                        action: systemLogs.roleMigrate, 
                        userIp: context.User_Ip, 
                        employeeId: loginEmployeeId, 
                        formId:  formIds.individuals,
                        organizationDbConnection,
                        isEmployeeTimeZone: 0,
                        changedData: args,
                        uniqueId: candidateId, 
                        message: `Conversion of the candidate to an employee has been completed successfully. The Employee-ID is ${candidateEmployeeData?.User_Defined_EmpId || newEmployeeId} ${candidateEmployeeData?.External_EmpId ? ', and External Employee-ID is '+candidateEmployeeData.External_EmpId :''}.` 
                    };
                    // Call the function to add the system log
                    await commonLib.func.createSystemLogActivities(systemLogParam);

                    systemLogParam.action = systemLogs.roleAdd;
                    systemLogParam.uniqueId = newEmployeeId;
                    systemLogParam.formId = formIds.myTeamSummary;
                    systemLogParam.message = `The employee has been added via the migration process.`;
                    await commonLib.func.createSystemLogActivities(systemLogParam);

                    const params = { orgCode: orgCode, partnerId: partnerId, employeeIds: [newEmployeeId], entityType: 'employee', action: 'create' };
                    await commonLib.stepFunctions.triggerStepFunction(process.env.asyncSyntrumAPIStepFunction, 'asyncSyntrumAPIStepFunction', null, params);


                    const defaultTemplate = await commonLib.func.getDefaultTemplate(
                        {
                            categoryId: 17,
                            formId: 178
                        },
                        organizationDbConnection
                    )
                    if (defaultTemplate && Object.keys(defaultTemplate).length) {
                        const templateId = defaultTemplate.Template_Id;
                        let eventVal = {
                            Source: process.env.emailFrom,
                            ReplyToAddresses: [process.env.emailReplyTo],
                            bucketName: process.env.documentsBucket,
                            region: process.env.region
                        }
                        const { emailResult, ses, event } =
                            await commonLib.func.listEmailTemplatePlaceHolderValues(
                                { templateId, candidateId: candidateId },
                                organizationDbConnection,
                                context,
                                eventVal,
                                []
                            );
                        let inviteStatus = await sendCustomEmail(emailResult, ses, event, null, organizationDbConnection);
                        if (inviteStatus && inviteStatus.toLowerCase() === "invited") {
                            return { errorCode: "", message: "Candidate migrated successfully." };
                        } else {
                            console.log('Error in sendEmailStatus in convertCandidateToEmployee');
                            throw 'IO0033'
                        }
                    }
                    if(args.empPrefixSettingId){
                        await incrementNextNumber(organizationDbConnection, args.empPrefixSettingId);
                    }
                    await sendMigratedEmail(organizationDbConnection, context, candidateId);

                    return { errorCode: "", message: "Candidate migrated successfully." };
                });

            return result

        } else {
            console.log('No rights to convert candidate to employee');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.error('Error in convertCandidateToEmployee function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError && typeof mainCatchError === 'object') {
            let errResult = commonLib.func.getError(mainCatchError.errorCode ? mainCatchError.errorCode : 'IO0033', 'IO0033');
            // return response
            throw new ApolloError(errResult.message, errResult.code, mainCatchError);
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0033');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    } finally {
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}

async function validateAndInsertPersonalInfo(organizationDbConnection, trx, args, candidateId) {
    try {

        let getPersonalInfoData = await getCommonFunction(organizationDbConnection, ehrTables.candidatePersonalInfo, { 'Candidate_Id': candidateId })

        if (!getPersonalInfoData || !getPersonalInfoData.length) {
            //Throw employee not found error
            throw 'DB0017'
        }
        getPersonalInfoData = getPersonalInfoData[0]

        let commonCheck = {
            "Emp_First_Name": getPersonalInfoData.Emp_First_Name,
            "Emp_Last_Name": getPersonalInfoData.Emp_Last_Name,
            "Nationality": getPersonalInfoData.Nationality,
            "DOB": getPersonalInfoData.DOB,
            "Blood_Group": getPersonalInfoData.Blood_Group,
            "Marital_Status": getPersonalInfoData.Marital_Status,
        }

        //Check if the employee active already exists
        let alreadyEmployeeExists = await checkEmployeeExists(organizationDbConnection, commonCheck)
        if (alreadyEmployeeExists) {
            //Throw employee already exists error
            throw 'IO0135'
        }

        //Get the old employeeDetails
        let oldJobDetails = await getCommonFunction(organizationDbConnection, ehrTables.candidateJob, { 'Candidate_Id': candidateId })
        oldJobDetails = oldJobDetails[0]
        oldJobDetails.Emp_Email=args.Emp_Email && args.Emp_Email.length ? args.Emp_Email : oldJobDetails.Emp_Email
        //Get the old bankDetails
        let oldBankDetails = await getCommonFunction(organizationDbConnection, ehrTables.candidateBankDetails, { 'Candidate_Id': candidateId })
        oldBankDetails = oldBankDetails.map((el) => el.Bank_Account_Number)

        //Get the contactDetails
        let oldContactDetails = await getCommonFunction(organizationDbConnection, ehrTables.candidateContact, { 'Candidate_Id': candidateId })
        oldContactDetails = oldContactDetails[0]

        //Get the insuranceDetails
        // let oldInsuranceDetails = await getCommonFunction(organizationDbConnection, ehrTables.empInsurance, {'Candidate_Id': candidateId})

        //Validate Already Exists Value 
        let failedData = await commonLib.func.validateEmployeeAlreadyExistsValues(organizationDbConnection, args.User_Defined_EmpId,oldJobDetails?.Emp_Email, oldBankDetails, oldContactDetails, null, null, getPersonalInfoData, oldJobDetails.Pf_PolicyNo, null)

        if (failedData && failedData.length) {
            throw {
                errorCode: 'IO0135',
                failedData: failedData
            }
        }

        //Validate Biometric Id
        if (args.External_EmpId && args.External_EmpId.length) {
            let biometricIntegrationId = await getCommonFunction(organizationDbConnection, ehrTables.empJob, { 'External_EmpId': args.External_EmpId })
            if (biometricIntegrationId && biometricIntegrationId.length) {
                throw 'IO0136'
            }
        }

        getPersonalInfoData.Form_Status = 1

        delete getPersonalInfoData.Candidate_Id
        delete getPersonalInfoData.Irukka_Candidate_Id
        delete getPersonalInfoData.Lock_Flag
        delete getPersonalInfoData.Section1_Progress
        delete getPersonalInfoData.Section2_Progress
        delete getPersonalInfoData.Section3_Progress
        delete getPersonalInfoData.Section4_Progress
        delete getPersonalInfoData.Candidate_Status
        delete getPersonalInfoData.Is_Duplicate
        delete getPersonalInfoData.Duplicate_Count
        delete getPersonalInfoData.Source_Type
        delete getPersonalInfoData.Added_On
        delete getPersonalInfoData.Added_By
        delete getPersonalInfoData.Updated_On
        delete getPersonalInfoData.Updated_By
        delete getPersonalInfoData.Talent_Pool_Id;
        delete getPersonalInfoData.Archive_Comment;
        delete getPersonalInfoData.Archive_Reason_Id;
        delete getPersonalInfoData.Data_Privacy_Statement;

        //Get the maximum employeeId from empPersonalInfo
        let maxEmpId = await organizationDbConnection(ehrTables.empPersonalInfo)
            .max('Employee_Id as maxEmpId')
            .first()
            .transacting(trx)

        getPersonalInfoData.Employee_Id = maxEmpId.maxEmpId + 1
        getPersonalInfoData.Allow_User_Signin = 1;

        let employeeId = await organizationDbConnection(ehrTables.empPersonalInfo)
            .insert(getPersonalInfoData)
            .transacting(trx)

        getPersonalInfoData.Employee_Id = employeeId[0]

        return getPersonalInfoData;

    } catch (err) {
        console.error('Error in validateAndInsertPersonalInfo main() function', err);
        throw err
    }
}

async function insertIntoJobInfo(organizationDbConnection, trx, candidateId, candidateEmployeeData, context, newEmployeeId) {
    try {
        //get the employee job details
        let getJobDetailsData = await getCommonFunction(organizationDbConnection, ehrTables.candidateJob, { 'Candidate_Id': candidateId })
        getJobDetailsData = getJobDetailsData[0]

        getJobDetailsData.Emp_Email=candidateEmployeeData.Emp_Email?candidateEmployeeData.Emp_Email:getJobDetailsData.Emp_Email
        //Replace with the new data
        getJobDetailsData.Employee_Id = newEmployeeId
        getJobDetailsData.Designation_Id_Effective_Date = getJobDetailsData.Date_Of_Join
        getJobDetailsData.Department_Id_Effective_Date = getJobDetailsData.Date_Of_Join
        getJobDetailsData.EmpType_Id_Effective_Date = getJobDetailsData.Date_Of_Join
        getJobDetailsData.Location_Id_Effective_Date = getJobDetailsData.Date_Of_Join
        getJobDetailsData.Work_Schedule_Effective_Date = getJobDetailsData.Date_Of_Join
        getJobDetailsData.Business_Unit_Id_Effective_Date = getJobDetailsData.Date_Of_Join
        getJobDetailsData.Manager_Id_Effective_Date = getJobDetailsData.Date_Of_Join
        getJobDetailsData.External_EmpId = candidateEmployeeData.External_EmpId
        getJobDetailsData.Confirmed = 0
        getJobDetailsData.Confirmation_Date = null
        getJobDetailsData.Emp_Status = "Active"
        getJobDetailsData.Emp_InActive_Date = null
        getJobDetailsData.User_Defined_EmpId = candidateEmployeeData.User_Defined_EmpId
        getJobDetailsData.Commission_Employee = getJobDetailsData.Commission_Employee ? getJobDetailsData.Commission_Employee : 0
        getJobDetailsData.Added_By = context.Employee_Id
        getJobDetailsData.Added_On = moment.utc().format("YYYY-MM-DD HH:mm:ss")
        getJobDetailsData.Timekeeping_Id = candidateEmployeeData.Timekeeping_Id ? candidateEmployeeData.Timekeeping_Id : null;
        getJobDetailsData.Career_Id = candidateEmployeeData.Career_Id ? candidateEmployeeData.Career_Id : null;
        getJobDetailsData.Roles_Id = candidateEmployeeData.Roles_Id ? candidateEmployeeData.Roles_Id : null;
        //Profession should be 1 (Other Profession) by default
        getJobDetailsData.Emp_Profession = candidateEmployeeData.Emp_Profession ? candidateEmployeeData.Emp_Profession : 1;
        let partnerId = context?.partnerid
        if (partnerId && partnerId.toLowerCase() ==='entomo') {
            getJobDetailsData.Invitation_Status = "Signed Up"
        }
        delete getJobDetailsData.Candidate_Id

        //insert into emp_job
        await commonLib.leaveCommonFunction.insertInTableWithTrx(organizationDbConnection, ehrTables.empJob, getJobDetailsData, trx);

        return getJobDetailsData;
    }
    catch (err) {
        console.error('Error in insertIntoJobInfo main() function', err)
        throw err
    }
}

async function addTimeStampInfoLog(organizationDbConnection, context, newEmployeeId) {
    try {
        let partnerId = context.partnerid ? context.partnerid : context.Partnerid;
        if (partnerId && partnerId !== undefined && partnerId.toLowerCase() === "camu") {
            await addTimeStampLog(organizationDbConnection, newEmployeeId, "Add", true)
            await callTrstScoreCreateApi(newEmployeeId, organizationDbConnection, context)
            await callCamuCreateApi(newEmployeeId, context)
        } else {
            await addTimeStampLog(organizationDbConnection, newEmployeeId, "Add", true)
            await callTrstScoreCreateApi(newEmployeeId, organizationDbConnection, context)
        }
    } catch (err) {
        console.error('Error in addTimeStampLog function', err)
        throw err;
    }
}

async function getCommonFunction(organizationDbConnection, table, whereCondition, selectData) {
    try {
        return (
            organizationDbConnection(table)
                .modify(function () {
                    if (selectData) {
                        this.select(selectData)
                    } else {
                        this.select("*")
                    }
                    if (whereCondition) {
                        this.where(whereCondition)
                    }
                })
                .then((data) => {
                    return data
                })
                .catch((err) => {
                    console.error('Error in getCommonFunction .catch()', err);
                    throw err
                })
        )
    } catch (err) {
        console.error('Error in getCommonFunction main catch()', err);
        throw err
    }
}

async function checkEmployeeExists(organizationDbConnection, whereCondition) {
    try {
        return (
            organizationDbConnection(ehrTables.empPersonalInfo + " as EP")
                .select("*")
                .leftJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "EP.Employee_Id")
                .where(whereCondition)
                .andWhere("EJ.Emp_Status", "Active")
                .then((data) => {
                    if (data && data.length) {
                        return true;
                    } else {
                        return false
                    }
                })
        )
    } catch (err) {
        console.error('Error in checkEmployeeExists function', err);
        throw err
    }
}

async function callCamuCreateApi(employeeId, context) {
    try {
        if (employeeId) {
            const apiBaseUrl = "https://" + process.env.commonAPIDomainName + '/integration/rographql'
            const requestBody = JSON.stringify({
                variables: {
                    employeeId: [employeeId],
                },
                query: "query CommentQuery($employeeId: [Int]!) { camuCreateStaff (employeeId:$employeeId) { errorCode message }}",
            });

            const apiHeaders = {
                org_code: context.Org_Code,
                Authorization: context.Auth_Token,
                refresh_token: context.refreshToken,
                partnerid: context.partnerid,
            };

            const config = {
                method: 'post',
                url: apiBaseUrl,
                maxBodyLength: Infinity,
                data: requestBody,
                headers: apiHeaders
            };
            await Promise.resolve(axios.request(config))
            //await axios.post(apiBaseUrl, requestBody, { headers: apiHeaders });
            return true;
        }
    } catch (err) {
        console.error('Error in callCamuCreateApi function', err);
        throw err
    }
}

async function getCompanyId(organizationDbConnection) {
    try {
        return (
            organizationDbConnection(ehrTables.orgDetails)
                .select('Field_Force')
                .then((data) => {
                    if (data[0].Field_Force) {
                        return (
                            organizationDbConnection(ehrTables.taxConfiguration + " as TC")
                                .select('TCD.Company_Id')
                                .leftJoin(ehrTables.trstscoreContactDetails + " as TCD", "TC.Tax_Configuration_Id", "TCD.Tax_Configuration_Id")
                                .then((data) => {
                                    if (data) {
                                        return data[0].Company_Id;
                                    }
                                })
                        )
                    } else {
                        return (
                            organizationDbConnection(ehrTables.trstscoreContactDetails)
                                .select('Company_Id')
                                .then((data) => {
                                    if (data && data.length) {
                                        return data[0].Company_Id;
                                    }
                                })
                        )
                    }
                })
        )
    } catch (err) {
        console.error('Error while getting the company id', err)
        throw err
    }
}


async function callTrstScoreCreateApi(employeeId, organizationDbConnection, context) {
    try {
        const companyId = await getCompanyId(organizationDbConnection);
        if (companyId && employeeId) {
            const apiBaseUrl = process.env.commonAPIDomainName + '/integration/graphql'

            const variables = {
                employeeId: employeeId,
                companyId: companyId,
            };

            const requestBody = JSON.stringify({
                variables: variables,
                query: "query CommentQuery($employeeId: Int!, $companyId: String!) { addEmployee (employeeId: $employeeId, companyId: $companyId) { errorCode message }}",
            });

            const apiHeaders = {
                org_code: context.Org_Code,
                Authorization: context.Auth_Token,
                refresh_token: context.refreshToken,
                partnerid: context.partnerid,
            };
            const config = {
                method: 'post',
                url: apiBaseUrl,
                maxBodyLength: Infinity,
                data: requestBody,
                headers: apiHeaders
            };
            await Promise.resolve(axios.request(config))
            //await axios.post(apiBaseUrl, requestBody, { headers: apiHeaders });
        }
        return true
    } catch (err) {
        console.error('Error in callTrstScoreCreateApi main() function', err);
        throw err
    }
}

async function customGroupRefresh(employeeId, context, updateOrganizationLeaveBalance = 0, leaveEnforcementConfigValue = "", isJobDetailsUpdated = false, isProbationDateUpdated = false, isDOJUpdated = false) {
    console.log("inside customGroupRefresh function")
    try {
        let coreHrRoBaseUrl = "https://" + process.env.commonAPIDomainName + '/coreHr/rographql'
        let url = coreHrRoBaseUrl
        let requestBody = {
            "variables": {
                "employeeId": employeeId,
                "logInEmpId": context.Employee_Id,
                "isCustomGroupRefresh": 1,
                "orgCode": context.Org_Code,
                "updateOrganizationLeaveBalance": updateOrganizationLeaveBalance,
                "leaveEnforcementConfigValue": leaveEnforcementConfigValue,
                "isJobDetailsUpdated": isJobDetailsUpdated,
                "isProbationDateUpdated": isProbationDateUpdated,
                "isDOJUpdated": isDOJUpdated,
            },
            "query": "query CommentQuery($employeeId:[Int],$logInEmpId:Int!,$isCustomGroupRefresh:Int,$orgCode:String!,$updateOrganizationLeaveBalance: Int, $leaveEnforcementConfigValue: String, $isJobDetailsUpdated: Boolean, $isProbationDateUpdated: Boolean, $isDOJUpdated: Boolean) { initiateRefreshCustomEmpGroups (employeeId:$employeeId,logInEmpId:$logInEmpId,isCustomGroupRefresh:$isCustomGroupRefresh,orgCode:$orgCode,updateOrganizationLeaveBalance: $updateOrganizationLeaveBalance, leaveEnforcementConfigValue: $leaveEnforcementConfigValue, isDOJUpdated: $isDOJUpdated, isProbationDateUpdated: $isProbationDateUpdated, isJobDetailsUpdated: $isJobDetailsUpdated) { errorCode message }}"
        }
        const apiHeaders = {
            org_code: context.Org_Code,
            Authorization: context.Auth_Token,
            refresh_token: context.refreshToken,
            partnerid: context.partnerid,
        };
        const config = {
            method: 'post',
            url: url,
            maxBodyLength: Infinity,
            data: requestBody,
            headers: apiHeaders
        };
        let returnedValue = await Promise.resolve(axios.request(config))
        //let returnedValue=await axios.post(url, requestBody, { headers: apiHeaders });
        return true
    }
    catch (error) {
        console.error('Error in customGroupRefresh() function', error)
        throw error
    }
}

function addTimeStampLog(organizationDbConnection, employeeId, action = "Update", camuStatus = null) {
    try {
        let insertParams = {
            'Employee_Id': employeeId,
            'Action': action,
            'Log_Timestamp': moment.utc().format("YYYY-MM-DD HH:mm:ss"),
        }
        if (camuStatus) {
            insertParams.Camu_Push_Status = 'Open'
        }
        return (
            organizationDbConnection(ehrTables.employeeLog)
                .insert(insertParams)
                .then(() => {
                    return true
                })
        )
    }
    catch (err) {
        console.error('Error in addTimeStampLog() function', err)
        throw err
    }
}

async function addExternalApiIntegrationLog(organizationDbConnection, employeeId, candidateId) {
    try {
        console.log('Inside addExternalApiIntegrationLog function');
        let insertParams = {
            'Employee_Id': employeeId,
            'Candidate_Id': candidateId,
        }
        await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .insert(insertParams)
    }
    catch (err) {
        console.error('Error in addExternalApiIntegrationLog() function', err)
        throw err
    }
}


//Function to clone the details from the employee
async function convertOtherDetails(organizationDbConnection, newEmployeeId, oldEmployeeId, personalInfoData, jobInfoData, context, trx) {
    console.log('In convertOtherDetails function');
    const tablesToBeCloned = [ehrTables.candidateLanguage, ehrTables.candidateDrivingLicense, ehrTables.candidatePassport, ehrTables.candidateDependent,
    ehrTables.candidateExperience, ehrTables.candidateContact, ehrTables.candidateEducation,
    ehrTables.candidateCertifications, ehrTables.candidateAwards, ehrTables.candidateSkills, ehrTables.candidateBankDetails,
    ehrTables.candidateTraining, ehrTables.candidateAccreditationDetails, ehrTables.candidateDocumentCategory, ehrTables.candidateAssets];
    const empTables = [ehrTables.empLanguages, ehrTables.empDrivingLicense, ehrTables.empPassport, ehrTables.empDependent,
    ehrTables.empExperience, ehrTables.contactDetails, ehrTables.empEducation,
    ehrTables.empCertifications, ehrTables.empAwards, ehrTables.empSkills, ehrTables.empBankDetails,
    ehrTables.empTraining, ehrTables.employeeAccreditationDetails, ehrTables.empDocumentCategory, ehrTables.empAssets];
    const tablesWhichHasOwnId = [ehrTables.candidateDependent, ehrTables.candidateExperience, ehrTables.candidateCertifications, ehrTables.candidateDocumentCategory, ehrTables.candidateSkills,
    ehrTables.candidateAwards, ehrTables.candidateBankDetails, ehrTables.candidateTraining, ehrTables.candidateAccreditationDetails, ehrTables.candidateEducation, ehrTables.candidatePassport, ehrTables.candidateAssets]
    const documentTables = [ehrTables.candidateExperience, ehrTables.candidateDocumentCategory,
    ehrTables.candidateEducation, ehrTables.candidateCertifications, ehrTables.candidateTraining]
    let contactDetails = {};
    try {
        let dependentIdMap = new Map();
        for (let i = 0; i < tablesToBeCloned.length; i++) {
            const data = await organizationDbConnection(tablesToBeCloned[i])
                .where('Candidate_Id', oldEmployeeId);

            if (data && data.length) {
                for (let j = 0; j < data.length; j++) {
                    var oldDocumentId = null;
                    if (tablesWhichHasOwnId.includes(tablesToBeCloned[i])) {
                        //If the table is emp_dependent remove the Dependent_Id
                        switch (tablesToBeCloned[i]) {
                            case ehrTables.candidateSkills:
                                data[j] = {
                                    Primary_Skill: data[j].Primary,
                                    Secondary_Skill: data[j].Skills,
                                    Known_Skills: data[j].Proficiency
                                }
                                break;
                            case ehrTables.candidatePassport:
                                data[j].Visa = data[j].Visa ? data[j].Visa : ''
                                break;
                            case ehrTables.candidateDependent:
                                oldDocumentId = data[j].Dependent_Id
                                delete data[j].Dependent_Id
                                data[j].Gender = data[j].Gender ? data[j].Gender : ''
                                data[j].Gender_Id = data[j].Gender_Id
                                break;
                            case ehrTables.candidateExperience:
                                oldDocumentId = data[j].Experience_Id
                                data[j].Start_Date_Join = data[j].Start_Date
                                delete data[j].Start_Date
                                delete data[j].Experience_Id
                                break;
                            case ehrTables.candidateEducation:
                                oldDocumentId = data[j].Education_Id
                                delete data[j].Education_Id
                                break;
                            case ehrTables.candidateCertifications:
                                oldDocumentId = data[j].Certification_Id
                                delete data[j].Certification_Id
                                break;
                            case ehrTables.candidateAwards:
                                delete data[j].Award_Id
                                break;
                            case ehrTables.candidateBankDetails:
                                delete data[j].Bank_Id
                                break;
                            case ehrTables.candidateTraining:
                                oldDocumentId = data[j].Training_Id
                                delete data[j].Training_Id
                                break;
                            case ehrTables.candidateAccreditationDetails:
                                // Replace candidate's Dependent_Id with the mapped employee's Dependent_Id
                                if (data[j].Dependent_Id && dependentIdMap.has(data[j].Dependent_Id)) {
                                    data[j].Dependent_Id = dependentIdMap.get(data[j].Dependent_Id);
                                } else {
                                    data[j].Dependent_Id = null;
                                }
                                oldDocumentId = data[j].Accreditation_Detail_Id
                                delete data[j].Accreditation_Detail_Id
                                break;
                            case ehrTables.candidateDocumentCategory:
                                oldDocumentId = data[j].Document_Id
                                delete data[j].Document_Id
                                delete data[j].Category_Id
                                delete data[j].Document_Type_Id
                                data[j].Document_Sub_Type_Id = data[j].Sub_Document_Type_Id
                                delete data[j].Sub_Document_Type_Id
                                break;
                            case ehrTables.contactDetails:
                                contactDetails = data[j]
                                break;

                            case ehrTables.candidateAssets:
                                delete data[j].Asset_Id
                                break;

                        }
                    }
                    delete data[j].Candidate_Id
                    data[j].Employee_Id = newEmployeeId;

                    let newDocumentId = await organizationDbConnection(empTables[i])
                        .insert(data[j])
                        .transacting(trx)

                    if (tablesToBeCloned[i] == ehrTables.candidateDependent && oldDocumentId) {
                        dependentIdMap.set(oldDocumentId, newDocumentId[0]);
                    }

                    if (documentTables.includes(tablesToBeCloned[i])) {
                        await copyDocuments(organizationDbConnection, tablesToBeCloned[i], oldDocumentId, newDocumentId[0], newEmployeeId, trx)
                    }

                    //If the table is experience include the experience reference
                    if (tablesToBeCloned[i] == ehrTables.candidateExperience) {
                        //Retrieve the candidate experience reference
                        let referenceDetails = await organizationDbConnection(ehrTables.candidateExperienceReference)
                            .where('Experience_Id', oldDocumentId)

                        if (referenceDetails && referenceDetails.length) {
                            referenceDetails.forEach(element => {
                                element.Experience_Id = newDocumentId[0]
                            })
                            await organizationDbConnection(ehrTables.empExperienceReference)
                                .insert(referenceDetails)
                                .transacting(trx)
                        }
                    }

                    console.log(`${tablesToBeCloned[i]} was cloned for ${newEmployeeId}`);

                }
            }
        }
        // Insert into emp_user table
        let userName = ""
        if (personalInfoData.Allow_User_Signin) {
            if (personalInfoData.Enable_Sign_In_With_Mobile_No && contactDetails) {
                userName = contactDetails.Mobile_No_Country_Code + contactDetails.Mobile_No;
            } else {
                userName = jobInfoData.Emp_Email;
            }
        }
        await commonLib.func.insertIntoTable(organizationDbConnection, ehrTables.empUser, { "Employee_Id": newEmployeeId, "User_Name": userName, "Created_Date": moment.utc().format("YYYY-MM-DD HH:mm:ss") });
        //Update the Candidate Status
        await organizationDbConnection(ehrTables.candidatePersonalInfo)
            .update({ "Candidate_Status": "Migrated" })
            .transacting(trx)
            .where('Candidate_Id', oldEmployeeId);
    } catch (err) {
        console.error('Error in convertOtherDetails function', err);
        throw err;
    }
}

async function copyDocuments(organizationDbConnection, type, oldDocumentId, newDocumentId, employeeId, trx) {
    try {
        let table, where, newFileName, newSubtypeId, documentName;
        if (type === ehrTables.candidateDocumentCategory) {
            table = ehrTables.empDocuments
            where = 'Document_Id'
        } else if (type === ehrTables.candidateExperience) {
            table = ehrTables.empExperienceDocuments
            where = 'Experience_Id'
        } else if (type === ehrTables.candidateEducation) {
            table = ehrTables.empDocuments
            where = 'Document_Id'
        } else if (type === ehrTables.candidateCertifications) {
            table = ehrTables.empDocuments
            where = 'Document_Id'
        } else if (type === ehrTables.candidateTraining) {
            table = ehrTables.empDocuments
            where = 'Document_Id'
        }

        //Updating in the table
        if (type === ehrTables.candidateEducation) {
            let data = await organizationDbConnection(ehrTables.candidateEducationDocuments + ' as CED')
                .select('CED.File_Name', 'CED.Sub_Type_Id', 'DC.Category_Fields')
                .leftJoin(ehrTables.documentSubType + ' as DST', 'DST.Document_Sub_Type_Id', 'CED.Sub_Type_Id')
                .leftJoin(ehrTables.documentType + ' as DT', 'DT.Document_Type_Id', 'DST.Document_Type_Id')
                .leftJoin(ehrTables.documentCategory + ' as DC', 'DC.Category_Id', 'DT.Category_Id')
                .where('CED.Education_Id', oldDocumentId)
            if (data && data.length) {
                newFileName = data[0].File_Name
                newSubtypeId = data[0].Sub_Type_Id
                documentName = data[0].Category_Fields
            }
        } else if (type === ehrTables.candidateDocumentCategory) {
            let data = await organizationDbConnection(ehrTables.candidateDocument)
                .select('File_Name')
                .where('Document_Id', oldDocumentId)
            if (data && data.length) {
                newFileName = data[0].File_Name
            }
        } else if (type === ehrTables.candidateCertifications) {
            let data = await organizationDbConnection(ehrTables.candidateCertificationsDocuments + ' as CCD')
                .select('CCD.File_Name', 'CCD.Sub_Type_Id', 'DC.Category_Fields')
                .leftJoin(ehrTables.documentSubType + ' as DST', 'DST.Document_Sub_Type_Id', 'CCD.Sub_Type_Id')
                .leftJoin(ehrTables.documentType + ' as DT', 'DT.Document_Type_Id', 'DST.Document_Type_Id')
                .leftJoin(ehrTables.documentCategory + ' as DC', 'DC.Category_Id', 'DT.Category_Id')
                .where('CCD.Certification_Id', oldDocumentId)
            if (data && data.length) {
                newFileName = data[0].File_Name
                newSubtypeId = data[0].Sub_Type_Id
                documentName = data[0].Category_Fields
            }
        } else if (type === ehrTables.candidateTraining) {
            let data = await organizationDbConnection(ehrTables.candidateTrainingDocuments + ' as CTD')
                .select('CTD.File_Name', 'CTD.Sub_Type_Id', 'DC.Category_Fields')
                .leftJoin(ehrTables.documentSubType + ' as DST', 'DST.Document_Sub_Type_Id', 'CTD.Sub_Type_Id')
                .leftJoin(ehrTables.documentType + ' as DT', 'DT.Document_Type_Id', 'DST.Document_Type_Id')
                .leftJoin(ehrTables.documentCategory + ' as DC', 'DC.Category_Id', 'DT.Category_Id')
                .where('CTD.Training_Id', oldDocumentId)
            if (data && data.length) {
                newFileName = data[0].File_Name
                newSubtypeId = data[0].Sub_Type_Id
                documentName = data[0].Category_Fields
            }
        } else if (type === ehrTables.candidateExperience) {
            let data = await organizationDbConnection(ehrTables.candidateExperienceDocuments)
                .select('File_Name')
                .where('Experience_Id', oldDocumentId)
            if (data && data.length) {
                newFileName = data[0].File_Name
            }
        }
        else {
            let data = await organizationDbConnection(type)
                .select('File_Name')
                .where(where, oldDocumentId)
            if (data && data.length) {
                newFileName = data[0].File_Name
            }
        }


        if (type === ehrTables.candidateEducation || type === ehrTables.candidateCertifications || type === ehrTables.candidateTraining) {

            if (newSubtypeId && (documentName || newFileName)) {
                let documentCategory = await organizationDbConnection(ehrTables.empDocumentCategory).insert({
                    Unique_Id: newDocumentId,
                    Employee_Id: employeeId,
                    Document_Sub_Type_Id: newSubtypeId,
                    Document_Name: documentName ? documentName : newFileName,
                    Added_On: moment.utc().format("YYYY-MM-DD HH:mm:ss"),
                    Added_By: employeeId
                })
                    .transacting(trx)
                newDocumentId = documentCategory[0];
            }
        }
        let insertObj = {
            [where]: newDocumentId,
            'File_Name': newFileName
        }
        await organizationDbConnection(table)
            .insert(insertObj)
            .transacting(trx)
    }
    catch (err) {
        console.error('Error in copyDocuments main function', err)
        throw err
    }

}

async function sendMigratedEmail(organizationDbConnection, context, candidateId) {
    try {

        let [orgDetails, candidateDetails, hrGroupEmail, initiatorEmail] = await Promise.all([
            commonLib.func.getOrgDetails(context.Org_Code, organizationDbConnection, 1),
            organizationDbConnection(ehrTables.candidateJob + " as CJ")
                .select(organizationDbConnection.raw("CONCAT_WS(' ', CP.Emp_First_Name, CP.Emp_Middle_Name, CP.Emp_Last_Name) as candidateName"), 'EJ.Emp_Email as managerEmail', 'CP.Personal_Email as candidateEmail')
                .leftJoin(ehrTables.candidatePersonalInfo + " as CP", "CJ.Candidate_Id", "CP.Candidate_Id")
                .leftJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "CJ.Manager_Id")
                .where('CP.Candidate_Id', candidateId),
            getHRGroupEmployeeEmails(organizationDbConnection),
            organizationDbConnection(ehrTables.empJob).select('Emp_Email').where('Employee_Id', context.Employee_Id)
        ])

        let emailData = {
            'status': '',
            'candidateName': candidateDetails[0].candidateName,
            'candidateEmail': candidateDetails[0].candidateEmail,
            'orgLogo': orgDetails.logoPath ? orgDetails.logoPath : '',
            'orgCode': context.Org_Code,
            'orgName': orgDetails.orgShortName ? orgDetails.orgShortName : (orgDetails.orgName ? orgDetails.orgName : '')
        }
        let emails = {
            hrGroupEmail: hrGroupEmail,
            managerEmail: candidateDetails[0].managerEmail ? candidateDetails[0].managerEmail : '',
            initiatorEmail: initiatorEmail[0].Emp_Email ? initiatorEmail[0].Emp_Email : ''
        }

        await sendEmailToCandidateManagerInitiatorHrGroup(emails, 'migrate', emailData, null, organizationDbConnection);
    } catch (err) {
        console.error("Error in sendMigratedEmail catch", err)
        throw err
    }
}

/**
 * Copies the custom field data of a candidate to an employee
 * @param {knex} organizationDbConnection - connection to the organization database
 * @param {number} employeeId - the id of the employee
 * @param {number} candidateId - the id of the candidate
 * @param {object} context - context of the API call, contains the id of the user who is making the call
 * @param {object} trx - transaction object
 * @returns {Promise<boolean>} - resolves to true if the copying is successful
 */
async function copyCustomFieldData(organizationDbConnection, employeeId, candidateId, context, trx) {
    try {
        //Retrieve candidate custom field data
        let candidateCustomFields = await organizationDbConnection(ehrTables.candidatesCustomFieldValues)
            .select('*')
            .where('Primary_Id', candidateId)
            .transacting(trx)

        if (candidateCustomFields && candidateCustomFields.length) {
            candidateCustomFields.forEach(element => {
                element.Primary_Id = employeeId
                element.Updated_On = moment.utc().format("YYYY-MM-DD HH:mm:ss")
                element.Updated_By = context.Employee_Id
                delete element.Custom_Field_Value_Id
            });
            //Insert candidate custom field data into employee custom field table
            await organizationDbConnection(ehrTables.teamSummaryCustomFieldValues)
                .insert(candidateCustomFields)
                .transacting(trx)
        }

        return true
    }
    catch (err) {
        console.error('Error in copyCustomFieldData main function', err)
        throw err
    }
}