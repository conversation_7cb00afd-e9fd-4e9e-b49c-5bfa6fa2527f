//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { formName, systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')

//function to add / update asset details
module.exports.addUpdateAssetDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateAssetDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const fieldValidations = {
                assetName: "IVE0386",
                serialNo: "IVE0387",
                receiveDate: "IVE0388",
                returnDate: "IVE0389",
            }
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let assetData = {
                    Candidate_Id: args.candidateId,
                    Asset_Name: args.assetName,
                    Serial_No: args.serialNo,
                    Receive_Date: args.receiveDate,
                    Return_Date: args.returnDate,
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (args.assetId) {
                                return (
                                    organizationDbConnection(ehrTables.candidateAssets)
                                        .update(assetData)
                                        .transacting(trx)
                                        .where('Asset_Id', args.assetId)
                                        .then(async (updateData) => {
                                            if (updateData) {
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the asset details', assetData)
                                                throw 'IO0111'
                                            }
                                        })
                                )
                            } else {
                                return (
                                    organizationDbConnection(ehrTables.candidateAssets)
                                        .insert(assetData)
                                        .transacting(trx)
                                        .then(async (insertData) => {
                                            if (insertData) {
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the asset details', assetData)
                                                throw 'IO0111'
                                            }
                                        })
                                )
                            }

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The candidate asset details has been ${args.assetId ? 'updated' : 'added'}.`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);

                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Candidate Asset details has been added/updated successfully." };
                            } else {
                                throw 'IO0111'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateAssetDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0111');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )

            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the asset details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateAssetDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateAssetDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0011');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}