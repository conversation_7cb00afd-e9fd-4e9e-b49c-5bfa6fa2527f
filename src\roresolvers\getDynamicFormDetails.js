//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');

//fuction to get vendor details
let organizationDbConnection;
module.exports.getDynamicFormDetails = async (parent, args, context, info) => {
    console.log('Inside getDynamicFormDetails function');
    try {
        let loginEmployeeId = context.Employee_Id ? context.Employee_Id : null;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights;
        if (args.checkAccess) {
            checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        }
        if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1)) {
            return (
                organizationDbConnection(ehrTables.dynamicFormResponse)
                    .select('Response as Form_Template', 'Response_Id')
                    .where('Vendor_Id', args.vendorId)
                    .then(async (data) => {
                        if (data && data.length) {
                            //Get the required details form dynamic form builder
                            let isRequired = await organizationDbConnection(ehrTables.formBuilder)
                                .select('Required')
                                .where('Template_Id', args.templateId)
                                .first();

                            for (let i = 0; i < data.length; i++) {
                                data[i].Required = isRequired ? isRequired.Required : 0;
                            }
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Dynamic form details has been fetched successfully.", dynamicFormDetails: data };
                        } else {
                            return (
                                organizationDbConnection(ehrTables.formBuilder)
                                    .select("Template_Name", "Template_Id", "Form_Template", 'Required')
                                    .where(function () {
                                        if (args.vendorBased) {
                                            this.where('Form_For', 'VEND')
                                        }
                                        if (args.templateId) {
                                            this.where('Template_Id', args.templateId)
                                        }
                                        this.where('Status', 'Active')
                                    })
                                    .then((data) => {
                                        if (data) {
                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                            return { errorCode: "", message: "Dynamic form has been fetched successfully.", dynamicFormDetails: data };
                                        }
                                        else {
                                            throw 'VO0140';
                                        }
                                    })
                                    .catch((err) => {
                                        //Destroy DB connection
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        console.log('Error in getDynamicFormDetails function .catch block.', err);
                                        let errResult = commonLib.func.getError(err, 'VO0140');
                                        throw new ApolloError(errResult.message, errResult.code)
                                    })

                            )
                        }
                    })

            )
        } else {
            console.log('No rights to view the vendor dynamic form');
            throw '_DB0100';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getDynamicFormDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'VO0025');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
