const resolvers = {
    Query : {
        listBankName : async (root,args,context,info) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0] && !args.Vendor_Based){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig("bank_details")
                        .orderBy('Bank_Name', 'asc')
                        .innerJoin('payroll_general_settings','payroll_general_settings.Payroll_Country','bank_details.Country_Code')
                        .then((bank)=>{
                            if(!bank[0]){
                                return {
                                    bankName: null
                                }
                            } else{
                                return {
                                    bankName: bank
                                }
                            }
                        })
                    }
                })
                .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listBankName')
                return result;
            }).catch(function(err){
                console.log('Error in listBankName',err);
                if (err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;