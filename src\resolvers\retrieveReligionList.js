const resolvers = {
    Query : {
        retrieveReligionList : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig("religion")
            .select('Religion_Id as religionId', 'Religion_Code as religionCode', 'Religion as religion')
            .orderBy('Religion', 'asc')
            .then((religion)=>{
                if(!religion[0]){
                    return {
                        religionData: null
                    }
                } else{
                    return {
                        religionData: religion
                    }
                }
            })
            .catch(function(err){
                console.log('Error in retrieveReligionList',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;