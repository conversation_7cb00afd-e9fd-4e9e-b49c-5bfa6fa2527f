//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { formName, systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')

//function to add / update experience details
module.exports.addUpdateExperienceDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateExperienceDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const fieldValidations = {
                companyName: "IVE0359",
                designation: "IVE0360",
                companyLocation: "IVE0361",
                startDate: "IVE0362",
                endDate: "IVE0363",
                duration: "IVE0364"
            }
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let experienceData = {
                    Candidate_Id: args.candidateId,
                    Prev_Company_Name: args.companyName,
                    Prev_Company_Location: args.companyLocation,
                    Designation: args.designation,
                    Start_Date: args.startDate,
                    End_Date: args.endDate,
                    Duration: args.duration,
                    Years: args.years,
                    Months: args.months,
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (args.experienceId) {
                                return (
                                    organizationDbConnection(ehrTables.candidateExperience)
                                        .update(experienceData)
                                        .transacting(trx)
                                        .where('Experience_Id', args.experienceId)
                                        .then(async (updateData) => {
                                            if (updateData) {
                                                await insertUpdateExperienceReferences(organizationDbConnection, args.referenceDetails, args.experienceId, true, trx)
                                                let document = await insertUpdateExperienceDocuments(organizationDbConnection, args.fileName, args.fileSize, args.experienceId)
                                                if (document) {
                                                    return 'success'
                                                } else {
                                                    throw 'IO0110'
                                                }
                                            } else {
                                                console.log('Error while updating the experience details', experienceData)
                                                throw 'IO0110'
                                            }
                                        })
                                )
                            } else {
                                return (
                                    organizationDbConnection(ehrTables.candidateExperience)
                                        .insert(experienceData)
                                        .transacting(trx)
                                        .then(async (insertData) => {
                                            if (insertData) {
                                                await insertUpdateExperienceReferences(organizationDbConnection, args.referenceDetails, insertData[0], false, trx)
                                                let document = await insertUpdateExperienceDocuments(organizationDbConnection, args.fileName, args.fileSize, insertData[0])
                                                if (document) {
                                                    return 'success'
                                                } else {
                                                    throw 'IO0110'
                                                }
                                            } else {
                                                console.log('Error while updating the experience details', experienceData)
                                                throw 'IO0110'
                                            }
                                        })
                                )
                            }

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The candidate experience details has been ${args.experienceId ? 'updated' : 'added'}.`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);

                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Candidate Experience details has been added/updated successfully." };
                            } else {
                                throw 'IO0110'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateExperienceDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0110');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )

            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the experience details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateExperienceDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateExperienceDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0010');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

async function insertUpdateExperienceDocuments(organizationDbConnection, fileName, fileSize, experienceId) {
    try {
        return (
            organizationDbConnection(ehrTables.candidateExperienceDocuments)
                .delete()
                .where('Experience_Id', experienceId)
                .then(() => {
                    if (fileName) {
                        return (
                            organizationDbConnection(ehrTables.candidateExperienceDocuments)
                                .insert({
                                    'Experience_Id': experienceId,
                                    'File_Name': fileName,
                                })
                                .then((data) => {
                                    if (data) {
                                        return true
                                    } else {
                                        return false
                                    }
                                })
                        )
                    }
                    return true
                })
        )
    } catch (err) {
        console.log('Error while adding experience documents', err)
        throw err
    }
}


/**
 * Function to insert or update experience references
 * @param {object} organizationDbConnection - object containing the knex connection
 * @param {array} referenceDetails - array of experience references
 * @param {number} experienceId - id of experience
 * @param {boolean} isUpdate - boolean indicating if this is an update
 * @param {object} trx - knex transaction object
 * @returns {boolean} true if successful, false if not
 */
async function insertUpdateExperienceReferences(organizationDbConnection, referenceDetails, experienceId, isUpdate, trx) {
    try {
        if (isUpdate) {
            await organizationDbConnection(ehrTables.candidateExperienceReference)
                .delete()
                .transacting(trx)
                .where('Experience_Id', experienceId)
        }

        if (referenceDetails?.length) {
            //Form Reference Details
            let referencesDetail = referenceDetails.map((ref) => ({
                Experience_Id: experienceId,
                Reference_Name: ref.Reference_Name,
                Reference_Email: ref.Reference_Email,
                Reference_Number: ref.Reference_Number
            }))
            //Check for duplication
            const duplicates = referencesDetail.filter((item, index, self) => {
                // Ignore entries with null or empty values
                if (!item.Reference_Email?.length && !item.Reference_Number?.length) {
                    return false;
                }

                // Check for duplicates based on Reference_Email or Reference_Number
                return self.findIndex(obj =>
                    (obj.Reference_Email === item.Reference_Email && obj.Reference_Email) ||
                    (obj.Reference_Number === item.Reference_Number && obj.Reference_Number)
                ) !== index;
            });

            if (duplicates?.length) {
                throw 'ESS0156'
            }
            await organizationDbConnection(ehrTables.candidateExperienceReference)
                .insert(referencesDetail)
                .transacting(trx)
        }

    } catch (err) {
        console.log('Error while adding experience references', err)
        throw err
    }
}