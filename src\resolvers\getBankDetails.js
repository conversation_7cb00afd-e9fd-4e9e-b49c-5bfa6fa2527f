const resolvers = {
    Query : {
        getBankDetails : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig('candidate_bankdetails.*',
                            'account_type.Account_Type'
                        ).from('candidate_bankdetails')
                        .leftJoin('account_type','candidate_bankdetails.Account_Type_Id','account_type.Account_Type_Id')
                        .where('Candidate_Id',url[0].Candidate_Id)
                        .then((bankDetail)=>{
                            if(!bankDetail[0]){
                                return null
                            } else {
                                return bankDetail[0]
                            }
                        })
                    }
                }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                return result;
            }).catch(function(err){
                console.log('Error in getBankDetails',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
        
    },
}
exports.resolvers = resolvers;