
exports.up = function(knex, Promise) {
  return knex.schema.createTable('candidate_url',function(table){
    table.increments();
    table.string('Url',300).notNullable();
    table.string('Url_Hash',300).notNullable();
    table.integer('Pin',11).notNullable();
    table.integer('Designation_Id',11).notNullable();
    table.integer('Department_Id',11).notNullable();
    table.integer('Location_Id',11).notNullable();
    table.timestamp('Expire_Time').notNullable();
    table.integer('Candidate_Id',11).notNullable();
    table.string('Job_Code',30).notNullable();
    table.date('Date_Of_Join').notNullable();
    table.date('Probation_Date').notNullable();
    table.integer('EmpType_Id',11).notNullable();
    table.integer('Manager_Id',11).notNullable();
    table.integer('Work_Schedule',11).notNullable();
    table.string('Status',20).defaultTo('Draft');
    table.timestamp('Created_At').notNullable().defaultTo(knex.raw('now()'));
    table.integer('Created_By',11).notNullable();
    table.timestamp('Updated_At').nullable();
    table.integer('Updated_By',11).nullable();
  })
};

exports.down = function(knex, Promise) {
  return knex.schema.dropTable('candidate_url');
};
