//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
//Require constants
const { systemLogs, formName, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')
var dateFormat = require('dateformat');

module.exports.updatePersonalInfo = async (parent, args, context, info) => {
    console.log('Inside updatePersonalInfo function.');
    let { Employee_Id: loginEmployeeId } = context;
    let { candidateId, languages } = args;
    let organizationDbConnection;
    let errResult;
    let validationError = {};

    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // Get the login employee - employee personal info form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            loginEmployeeId,
            null,
            '',
            'UI',
            false,
            formIds.individuals
        );

        // Check add rights exist or not
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            const fieldValidations = {
                userDefinedEmpId: 'IVE0318',
                biometricIntegraionId: 'IVE0319',
                empFirstName: 'IVE0320',
                empMiddleName: 'IVE0321',
                empLastName: 'IVE0322',
                knownAs: 'IVE0323',
                placeOfBirth: 'IVE0324',
                nationality: 'IVE0325',
                ethnicRace: 'IVE0326',
                religion: 'IVE0327',
                caste: 'IVE0328',
                personalEmail: 'IVE0329',
            };
            validationError = validateCandidateInputs(args, fieldValidations);
            validationError = await validateAlreadyExists(organizationDbConnection, args, validationError);

            // Check validation error exist or not
            if (Object.keys(validationError).length === 0) {
                return organizationDbConnection.transaction(async (trx) => {
                    let { personalInfo } = getFormJSON(args, candidateId);

                    await organizationDbConnection(ehrTables.candidatePersonalInfo)
                        .update(personalInfo)
                        .where('Candidate_Id', candidateId)
                        .transacting(trx);

                    await organizationDbConnection(ehrTables.candidateJob)
                        .update({
                            User_Defined_EmpId: args.userDefinedEmpId ? args.userDefinedEmpId : candidateId,
                            External_EmpId: args.biometricIntegrationId ? args.biometricIntegrationId : null,
                        })
                        .where('Candidate_Id', candidateId)
                        .transacting(trx)

                    await addUpdateLanguages(languages, candidateId, organizationDbConnection, trx, 1);

                    return candidateId;
                })
                    .then(async (data) => {
                        if (data) {
                            let systemLogParam = {
                                action: systemLogs.roleUpdate,
                                userIp: context.User_Ip,
                                employeeId: loginEmployeeId,
                                formName: formName.candidate,
                                trackingColumn: '',
                                organizationDbConnection: organizationDbConnection,
                                uniqueId: data,
                            };

                            // Call the function to add the system log
                            await commonLib.func.createSystemLogActivities(systemLogParam);

                            // Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;

                            // Return success response
                            return { errorCode: '', message: 'Candidate personal information updated successfully.', candidateId: data };
                        }
                    })
                    .catch(function (catchError) {
                        console.log('Error in updatePersonalInfo() function .catch() block', catchError);
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        errResult = commonLib.func.getError(catchError, 'IO0103');
                        throw new ApolloError(errResult.message, errResult.code); // Return error response
                    });
            } else {
                throw 'IVE0000';
            }
        } else {
            throw '_DB0102';
        }
    } catch (updatePersonalInfoMainCatchErr) {
        console.log('Error in the updatePersonalInfo() function main catch block. ', updatePersonalInfoMainCatchErr);

        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        // If validation error exists
        if (updatePersonalInfoMainCatchErr === 'IVE0000') {
            console.log('Validation error in the updatePersonalInfo() function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError }); // Return error response
        } else {
            errResult = commonLib.func.getError(updatePersonalInfoMainCatchErr, 'IO0004');
            throw new ApolloError(errResult.message, errResult.code); // Return error response
        }
    }
}

//Function to add / update languages of the employee
async function addUpdateLanguages(languages, candidateId, organizationDbConnection, trx, isUpdate) {
    try {

        if (isUpdate) {
            const deleteQuery = organizationDbConnection(ehrTables.candidateLanguages)
                .delete()
                .transacting(trx)
                .where('Candidate_Id', candidateId);

            await deleteQuery;
        }

        if (languages?.length === 0) {
            return true;
        }

        let formedLanguage = languages.map((lan) => ({
            Candidate_Id: candidateId,
            Lang_Known: lan.Lang_Known,
            Lang_Spoken: lan.Lang_Spoken,
            Lang_Read_Write: lan.Lang_Read_Write,
            Lang_Proficiency: lan.Lang_Proficiency
        }));

        // If the Lang_Known is same in multiple throw error
        const checkLangKnown = formedLanguage.filter((item, index, self) => self.findIndex(obj => obj.Lang_Known === item.Lang_Known) !== index);
        if (checkLangKnown?.length > 0) {
            throw 'ESS0155';
        }

        const insertQuery = organizationDbConnection(ehrTables.candidateLanguages)
            .insert(formedLanguage)
            .transacting(trx);

        await insertQuery;

        return true;
    } catch (err) {
        console.log('Error while adding/updating languages', err);
        throw err
    }
}



function getFormJSON(args) {
    let personalInfo = {
        Photo_Path: args.photoPath,
        Salutation: args.salutation,
        Emp_First_Name: args.empFirstName,
        Emp_Middle_Name: args.empMiddleName,
        Emp_Last_Name: args.empLastName,
        Emp_Pref_First_Name: args.knownAs,
        Appellation: args.appellation,
        Gender: args.gender,
        Gender_Id: args.genderId,
        Gender_Identity_Id: args.genderIdentityId ? args.genderIdentityId : null,
        Gender_Expression_Id: args.genderExpressionId ? args.genderExpressionId : null,
        DOB: (!args.dob || isNaN(Date.parse(args.dob))) ? (null) : ((isNaN(Number(args.dob))) ? dateFormat(new Date((args.dob)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.dob)), "yyyy-mm-dd")),
        Place_Of_Birth: args.placeOfBirth,
        Marital_Status: args.maritalStatus,
        Blood_Group: args.bloodGroup,
        Hobbies: args.hobbies,
        Nationality: args.nationality,
        Nationality_Id: args.nationalityId,
        Military_Service: args.militaryService,
        Religion: args.religion,
        Religion_Id: args.religionId,
        Caste: args.caste,
        Physically_Challenged: args.disabled,
        Is_Manager: args.isManager,
        Personal_Email: args.personalEmail,
        Smoker: args.smoker,
        Smokerasof: (!args.smokerAsOf || isNaN(Date.parse(args.smokerAsOf))) ? (null) : ((isNaN(Number(args.smokerAsOf))) ? dateFormat(new Date((args.smokerAsOf)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.smokerAsOf)), "yyyy-mm-dd")),
        Aadhaar_Card_Number: args.aadharNumber,
        UAN: args.uan,
        PAN: args.pan,
        Form_Status: args.formStatus,
        Allow_User_Signin: args.allowUserSignIn,
        Enable_Sign_In_With_Mobile_No: args.enableMobileSignIn,
        Gender_Orientations: args.genderOrientations ? args.genderOrientations : null,
        Pronoun: args.pronoun ? args.pronoun : null,
        Statutory_Insurance_Number: args.statutoryInsuranceNumber ? args.statutoryInsuranceNumber : null,
        PRAN_No: args.pranNo ? args.pranNo : null,
        Tax_Code: args.taxCode ? args.taxCode : null
    }

    return { personalInfo }
}

async function validateAlreadyExists(organizationDbConnection, args, validationError) {
    try {

        const failedData = await organizationDbConnection(ehrTables.empPersonalInfo + " as EP")
            .select("EP.UAN", "EP.PAN", "EP.Aadhaar_Card_Number", "EJ.User_Defined_EmpId", "EJ.External_EmpId", "EJ.Emp_Status")
            .where(function () {
                if (args.uan && args.uan.length) {
                    this.orWhere('EP.UAN', args.uan);
                }
                if (args.pan && args.pan.length) {
                    this.orWhere('EP.PAN', args.pan);
                }
                if (args.aadharNumber && args.aadharNumber.length) {
                    this.orWhere('EP.Aadhaar_Card_Number', args.aadharNumber);
                }
                if (args.userDefinedEmpId && args.userDefinedEmpId.length) {
                    this.orWhere('EJ.User_Defined_EmpId', args.userDefinedEmpId)
                }
                if (args.biometricIntegrationId && args.biometricIntegrationId.length) {
                    this.orWhere('EJ.External_EmpId', args.biometricIntegrationId)
                }
            })
            .leftJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "EP.Employee_Id")


        if (failedData && failedData.length) {
            let uan = [], pan = [], aadharNumber = [], userDefined = [], biometric = [];

            //Loop through the data and check if the data is same
            failedData.forEach((item) => {
                if (args.uan && args.uan.length && item.UAN === args.uan && item.Emp_Status === 'Active') {
                    uan.push(item.User_Defined_EmpId);
                }
                if (args.pan && args.pan.length && item.PAN === args.pan && item.Emp_Status === 'Active') {
                    pan.push(item.User_Defined_EmpId);
                }
                if (args.aadharNumber && args.aadharNumber.length && item.Aadhaar_Card_Number === args.aadharNumber && item.Emp_Status === 'Active') {
                    aadharNumber.push(item.User_Defined_EmpId);
                }
                if (args.userDefinedEmpId && args.userDefinedEmpId.length && item.User_Defined_EmpId === args.userDefinedEmpId) {
                    userDefined.push(item.User_Defined_EmpId);
                }
                if (args.biometricIntegrationId && args.biometricIntegrationId.length && item.External_EmpId === args.biometricIntegrationId) {
                    biometric.push(item.User_Defined_EmpId);
                }
            }
            );

            if (userDefined && userDefined.length) {
                validationError['IVE0428'] = commonLib.func.getError('', 'IVE0428').message;
            }
            if (biometric && biometric.length) {
                validationError['IVE0429'] = commonLib.func.getError('', 'IVE0429').message + ' ' + biometric.join(',');
            }
            if (uan && uan.length) {
                validationError['IVE0431'] = commonLib.func.getError('', 'IVE0431').message + ' ' + uan.join(',');
            }
            if (pan && pan.length) {
                validationError['IVE0424'] = commonLib.func.getError('', 'IVE0424').message + ' ' + pan.join(',');
            }
            if (aadharNumber && aadharNumber.length) {
                validationError['IVE0425'] = commonLib.func.getError('', 'IVE0425').message + ' ' + aadharNumber.join(',');
            }
        }

        return validationError

    } catch (err) {
        console.log('Error in validateAlreadyExists main catch', err);
        throw err;
    }
}
