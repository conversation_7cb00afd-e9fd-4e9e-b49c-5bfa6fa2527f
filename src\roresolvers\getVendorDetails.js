//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');

//fuction to get vendor details
let organizationDbConnection;
module.exports.getVendorDetails = async (parent, args, context, info) => {
    console.log('Inside getVendorDetails function');
    try {
        let vendorDetails = {};
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (organizationDbConnection(ehrTables.invitedVendors)
            .select("Url_Hash")
            .where('Url_Hash', args.urlHash)
            .then((data) => {
                if (!data[0]) {
                    throw 'VO0109';
                } else {
                    return (
                        organizationDbConnection(ehrTables.serviceProvider)
                            .select("OV.Service_Provider_Id as vendorId", "OV.Vendor_Type as vendorType", "OV.Service_Provider_Name as vendorName", "OV.Business_Number as businessNumber", "OV.Entity_Trust as entityTrust",
                                "OV.Trustee_Name as trusteeName", "OV.GST_Registered as gstRegistered", "OV.Street as street", "OV.City as city",
                                "OV.State_Region as stateRegion", "OV.Zip_Code as zipCode", "OV.Country as country", "OV.Postal_Street as postalStreet", "OV.Postal_City as postalCity", "OV.Postal_State_Region as postalStateRegion",
                                "OV.Postal_Zip_Code as postalZipCode", "OV.Postal_Country as postalCountry", "OV.Telephone as telephone","OV.Contact_Person_Name as contactNameOfVendor",
                                "OV.Contact_Person_Phone_Number as mobileNo", "OV.Contact_Person_Phone_Number_Country_Code as mobileNoCountryCode", "OV.Email_Id as vendorEmail", "OV.No_Of_Employees_Within_Organization as noOfEmployeesWithinOrganization",
                                "OV.No_Of_Subcontractors as noOfSubcontractors", "OV.No_Of_Employees_To_Be_Allocated_To_Contract as noOfEmployeesToBeAllocatedToContract",
                                "OV.Trustee_Authorization_Documents as trusteeAuthorizationDocuments", "OV.Onboarding_Form_Id as onboardingFormId", "OV.GST_Number as gstNumber",
                                "OV.Service_Offered as serviceOffered", "OV.Type_Of_Industry as typeOfIndustry", "OV.Service_Priority as servicePriority",
                                "OV.Status as status", "IV.Added_On as addedOn", "IV.Added_By as addedBy", "IV.Updated_On as updatedOn", "IV.Updated_By as updatedBy",
                                )
                            .leftJoin(ehrTables.invitedVendors + " as IV", "IV.Vendor_Id", "OV.Service_Provider_Id")
                            .from(ehrTables.serviceProvider + " as OV")
                            .where('IV.Url_Hash', args.urlHash)
                            .then((data) => {
                                if (data && data.length > 0) {
                                    vendorDetails = data[0];
                                    return(
                                        organizationDbConnection(ehrTables.documentCompliance)
                                            .select("Insurance_Id as insuranceId", "Type_Of_Insurance as typeOfInsurance", "Name_Of_Insurance as nameOfInsurance", "Policy_Number as policyNumber",
                                                "Name_Of_Insurer as nameOfInsurer", "Name_Of_Insured as nameOfInsured", "Sum_Insured as sumInsured",
                                                "Expiry_Date as expiryDate","Document_File as documentFile", "Description as description")
                                            .where('Vendor_Id', data[0].vendorId)
                                            .then((data) => {
                                                if (data && data.length > 0) {
                                                    vendorDetails.insuranceDetails = JSON.parse(JSON.stringify(data));
                                                } 
                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                return { errorCode: "", message: "Vendor details has been fetched successfully.", vendorDetails: vendorDetails };
                                            })
                                    )
                                }
                                else {
                                    throw 'VO0103';
                                }
                            })
                            
                    )
                }
            }).catch((catchError) => {
                console.log('Error in getVendorDetails .catch() block', catchError);
                errResult = commonLib.func.getError(catchError, 'VO0108');
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                throw new ApolloError(errResult.message, errResult.code);
            })
            )
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getVendorDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'VO0004');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
