const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { UserInputError, ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../common/tableAlias');
const { bulkInviteValidation } = require('../common/bulkInviteValidation');
const { encodeUsingBase64, getProbationDate, getLocationDetails } = require('../common/commonFunctions');
const SHA3 = require('sha3');
const randomize = require('randomatic');
const moment = require('moment-timezone');

module.exports.bulkInviteOnboardEmployees = async (parent, args, context, info) => {
    console.log('Inside bulkInviteOnboardEmployees function');
    let organizationDbConnection;
    let validationError = {};
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const orgCode = context.Org_Code;
        const companyName = encodeURIComponent(orgCode); 

        //get appmanager db connection
        let appManagerDbConnection = knex(context.connection.AppManagerDb);
        let regionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection, orgCode);
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        if (Object.keys(regionDetails).length == 0) {
            throw new Error('Region details not found.');
        }

        //Input Validation
        validationError = await bulkInviteValidation(args);
        if (Object.keys(validationError).length) {
            throw 'IVE0000';
        }

        //Validate onboarding vendor id
        const onboardingVendorId = args.vendorId || 0;
        if (onboardingVendorId <= 0) {
            throw "VO0103"
        }

        const employeeData = args.employeeData;
        const candidatePersonalInfoData = employeeData.map((emp) => ({
            Emp_First_Name: emp.firstName,
            Emp_Last_Name: emp.lastName,
            Source_Type: "EmployeeOnboard",
            Added_On: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
        }));

        return (organizationDbConnection
            .transaction(async (trx) => {

                const [insertedCandidateId] = await organizationDbConnection(ehrTables.candidatePersonalInfo)
                    .insert(candidatePersonalInfoData)
                    .transacting(trx);

                const candidateIds = Array.from(
                    { length: args.count },
                    (_, idx) => insertedCandidateId + idx
                );

                const processedData = await prepareEmployeeData(
                    employeeData,
                    candidateIds,
                    regionDetails,
                    companyName,
                    orgCode,
                    onboardingVendorId,
                    organizationDbConnection
                );

                await Promise.all([
                    insertDataWithTransaction(organizationDbConnection, ehrTables.candidateUrl, processedData.employeeData, trx),
                    insertDataWithTransaction(organizationDbConnection, ehrTables.candidateContact, processedData.contactData, trx),
                    insertDataWithTransaction(organizationDbConnection, ehrTables.candidateJob, processedData.jobData, trx),
                    insertDataWithTransaction(organizationDbConnection, ehrTables.vendorAdminList, processedData.adminList, trx),
                ]);

                return { errorCode: "", message: "Candidate data imported successfully." };

            })
        )
    } catch (mainCatchError) {
        console.log('Error in bulkInviteOnboardEmployees function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in bulkInviteOnboardEmployees function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(mainCatchError, 'VO0008');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

/**
 * Prepare employee data for bulk onboarding.
 *
 * @param {Array} employeeData - an array of objects containing employee data.
 * @param {Array} candidateIds - an array of strings representing the candidate Ids.
 * @param {Object} regionDetails - an object containing the region details.
 * @param {String} companyName - the name of the company.
 * @param {String} orgCode - the code of the organization.
 * @param {String} vendorId - the Id of the vendor.
 * @param {Object} organizationDbConnection - the knex connection object.
 *
 * @returns {Object} - an object containing the prepared employee data.
 */

const prepareEmployeeData = async (employeeData, candidateIds, regionDetails, companyName, orgCode, vendorId, organizationDbConnection) => {
    try {
        const baseUrl = process.env.baseAUUrlUI;

        const results = await Promise.all(
            employeeData.map(async (emp, index) => {
                const locationDetails = await getLocationDetails(organizationDbConnection, emp.Location_Id);
                const countryCode = locationDetails?.[0]?.Country_Code || null;

                const hash = new SHA3.SHA3Hash().update(`${Date.now()} ${index}`).digest('hex');
                let fullUrl = baseUrl + companyName + '&country=' + countryCode?.toLowerCase() + '&code=' + orgCode + '&identifier=' + hash + '&d_code=' + regionDetails.Data_Region + '&b_code=' + regionDetails.Bucket_Region;
                const urlIndex = fullUrl.indexOf('?');
                fullUrl = fullUrl.substr(0, urlIndex) + '?' + await encodeUsingBase64((fullUrl.substr(urlIndex + 1)));

                const expireMeasureType = ['Minutes', 'Hours', 'Days'][emp.expireType - 1];
                const expireTime = moment
                    .utc()
                    .add(emp.expireValue, expireMeasureType.toLowerCase())
                    .format('YYYY-MM-DD HH:mm:ss');

                const probationDate = await getProbationDate(emp.Designation_Id, emp.Date_Of_Join, organizationDbConnection);

                const commonData = {
                    Candidate_Id: candidateIds[index],
                    Designation_Id: emp.Designation_Id,
                    Department_Id: emp.Department_Id,
                    Location_Id: emp.Location_Id,
                    Date_Of_Join: emp.Date_Of_Join,
                    Probation_Date: probationDate,
                    EmpType_Id: emp.EmpType_Id,
                    Manager_Id: emp.Manager_Id,
                    Work_Schedule: emp.Work_Schedule,
                    Service_Provider_Id: vendorId,
                };

                const employeeInfo = {
                    ...commonData,
                    Name: `${emp.firstName} ${emp.lastName}`,
                    Email: emp.email,
                    Url: fullUrl,
                    Url_Hash: hash,
                    Pin: randomize('?', 6, { chars: '123456789' }),
                    Expire_Time: expireTime,
                    URL_Expiry_Duration: emp.expireValue,
                    URL_Expiry_Duration_Measure: expireMeasureType,
                    Status: "Supplier to be verified",
                    Service_Provider_Id: vendorId,
                };

                return {
                    data: employeeInfo,
                    contactData: { Candidate_Id: candidateIds[index] },
                    jobData: commonData,
                    adminData: emp.nominateAsAdmin
                        ? { Candidate_Id: candidateIds[index], Vendor_Id: vendorId }
                        : null,
                };
            })
        );

        return {
            employeeData: results.map((r) => r.data),
            contactData: results.map((r) => r.contactData),
            jobData: results.map((r) => r.jobData),
            adminList: results.filter((r) => r.adminData).map((r) => r.adminData),
        };
    } catch (err) {
        console.log('Error in prepareEmployeeData:', err);
        throw err;
    }
};

/**
 * Inserts the given data into the specified table within the provided transaction.
 *
 * @param {Object} db - The knex database instance.
 * @param {String} table - The table to insert into.
 * @param {Array} data - The array of objects to insert.
 * @param {Object} trx - The transaction object.
 * @returns {Promise} - A promise that resolves with the result of the insert operation.
 */
const insertDataWithTransaction = async (db, table, data, trx) => {
    try {
        if (data && data.length) {
            return db(table).insert(data).transacting(trx);
        }
    }
    catch (err) {
        console.log('Error in insertDataWithTransaction', err)
        throw err
    }
};