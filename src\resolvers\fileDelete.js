const resolvers = {
    Query: {
        fileDelete: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');
            const { s3FileUpload } = require('../common/imageConstants');
            const { ehrTables } = require('../common/tableAlias');
            let tableName = "";
            let category = args.Category;
            if (category == "trusteeAuthorizationDocuments" || category == "projectResources" || category == "insurance" || category == "vendorDocuments") {
                tableName = ehrTables.invitedVendors;
            } else {
                tableName = ehrTables.candidateUrl;
            }
            return knexconfig.transaction(function (trc) {
                return knexconfig(tableName)
                    .where('Url_Hash', args.Url_Hash)
                    .then(async (url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } else {
                            let path;
                            let bucketName;
                            if (args.Category == "profileImage") {
                                path = "hrapp_upload/" + args.Org_Code + "_tmp/images/" + args.File_Name
                                bucketName = process.env.imageBucket;
                            } else if (args.Category == "document") {
                                path = process.env.domainName + "/" + args.Org_Code + "/" + "Employees Document Upload/" + args.File_Name;
                                bucketName = process.env.documentBucket;
                            } else if (args.Category == "experience") {
                                path = process.env.domainName + "/" + args.Org_Code + "/" + "Employee Experience/" + args.File_Name;
                                bucketName = process.env.documentBucket;
                            }
                            else if (category == "trusteeAuthorizationDocuments") {
                                path = process.env.domainName + "/" + args.Org_Code + "/" + url[0].Vendor_Id + "/" + "Authorization Documents/" + args.File_Name;
                                bucketName = process.env.vendorBucketName;
                            } else if (category == "projectResources") {
                                path = process.env.domainName + "/" + args.Org_Code + "/" + url[0].Vendor_Id + "/" + "Project Resources/" + args.File_Name;
                                bucketName = process.env.vendorBucketName;
                            } else if (category == "insurance") {
                                path = process.env.domainName + "/" + args.Org_Code + "/" + url[0].Vendor_Id + "/" + "Insurance/" + args.File_Name;
                                bucketName = process.env.vendorBucketName;
                            } else if (category == "vendorDocuments") {
                                path = process.env.domainName + "/" + args.Org_Code + "/" + url[0].Vendor_Id + "/" + "Vendor Documents/" + args.File_Name;
                                bucketName = process.env.vendorBucketName;
                            }
                            else if (args.Category == "accreditation") {
                                path = process.env.domainName + "/" + args.Org_Code + "/" + "Employee Accreditation/" + args.File_Name;
                                bucketName = process.env.documentBucket;
                            }
                            else if (args.Category == "bank") {
                                path = process.env.domainName + "/" + args.Org_Code + "/" + "Employee Bank Details/" + args.File_Name;
                                bucketName = process.env.documentBucket;
                            }
                            else {
                                throw new Error("Invalid document type")
                            }

                            try {

                                let params = {
                                    Bucket: bucketName,
                                    Key: path
                                };

                                const AWS = require("aws-sdk");

                                const s3 = new AWS.S3();

                                let deleteResponse = await s3.deleteObject(params).promise();

                                return {
                                    message: "File has been deleted."
                                }
                            }
                            catch (err) {
                                console.log("Err", err);
                                throw new Error("Something went wrong")
                            }
                        }
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                return result;
            }).catch(function (err) {
                console.log('Error in fileDelete', err);
                if (err.message == "REO0101") {
                    console.log('URL not found')
                    throw new ApolloError("URL not found", "REO0101")
                } else if (err.message == "Invalid document type") {
                    throw new UserInputError(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;