const resolvers = {
    Mutation: {
        updatePersonalInfo: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var dateFormat = require('dateformat');
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_url')
                    .where('Url_Hash', args.Url_Hash)
                    .then((url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } else {
                            var DOB = ((!args.DOB) || isNaN(Date.parse(args.DOB))) ? (null) : ((isNaN(Number(args.DOB))) ? dateFormat(new Date((args.DOB)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.DOB)), "yyyy-mm-dd"));
                            var Smokerasof = ((!args.Smokerasof) || isNaN(Date.parse(args.Smokerasof))) ? (null) : ((isNaN(Number(args.Smokerasof))) ? dateFormat(new Date((args.Smokerasof)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Smokerasof)), "yyyy-mm-dd"));
                            return knexconfig("candidate_personal_info")
                                .where("Candidate_Id", url[0].Candidate_Id)
                                .update({
                                    Salutation: (!args.Salutation) ? (null) : (args.Salutation),
                                    Emp_First_Name: (!args.Emp_First_Name) ? (null) : (args.Emp_First_Name),
                                    Emp_Middle_Name: (!args.Emp_Middle_Name) ? (null) : (args.Emp_Middle_Name),
                                    Emp_Last_Name: (!args.Emp_Last_Name) ? (null) : (args.Emp_Last_Name),
                                    Emp_Pref_First_Name: (!args.Emp_Pref_First_Name) ? (null) : (args.Emp_Pref_First_Name),
                                    Gender: (!args.Gender) ? (null) : (args.Gender),
                                    Gender_Id: args.Gender_Id,
                                    Gender_Identity_Id: args.Gender_Identity_Id ? args.Gender_Identity_Id : null,
                                    Gender_Expression_Id: args.Gender_Expression_Id ? args.Gender_Expression_Id : null,
                                    DOB: DOB,
                                    Place_Of_Birth: (!args.Place_Of_Birth) ? (null) : (args.Place_Of_Birth),
                                    Marital_Status: (args.Marital_Status !== 0 && !args.Marital_Status) ? (null) : (args.Marital_Status),
                                    Blood_Group: (!args.Blood_Group) ? (null) : (args.Blood_Group),
                                    Nationality: (!args.Nationality) ? (null) : (args.Nationality),
                                    Religion: (!args.Religion) ? (null) : (args.Religion),
                                    Religion_Id: (!args.Religion_Id) ? (null) : (args.Religion_Id),
                                    Nationality_Id: (!args.Nationality_Id) ? (null) : (args.Nationality_Id),
                                    Appellation: (!args.Appellation) ? (null) : (args.Appellation),
                                    Caste: (!args.Caste) ? (null) : (args.Caste),
                                    Personal_Email: (!args.Personal_Email) ? (null) : (args.Personal_Email),
                                    Physically_Challenged: (!args.Physically_Challenged) ? (0) : (1),
                                    Smoker: (!args.Smoker) ? (null) : (args.Smoker),
                                    Smokerasof: Smokerasof,
                                    PAN: (!args.PAN) ? (null) : (args.PAN),
                                    Aadhaar_Card_Number: (!args.Aadhaar_Card_Number) ? (null) : (args.Aadhaar_Card_Number),
                                    Military_Service: (!args.Military_Service) ? (null) : (args.Military_Service),
                                    Section1_Progress: (args.Section1_Progress == true) ? (true) : (false),
                                    Section2_Progress: (args.Section2_Progress == true) ? (true) : (false),
                                    Section3_Progress: (args.Section3_Progress == true) ? (true) : (false),
                                    Section4_Progress: (args.Section4_Progress == true) ? (true) : (false),
                                    Ethnic_Race: (!args.Ethnic_Race) ? (null) : (args.Ethnic_Race),
                                    Gender_Orientations: (!args.Gender_Orientations) ? (null) : (args.Gender_Orientations),
                                    Pronoun: (!args.Pronoun) ? (null) : (args.Pronoun),
                                    Statutory_Insurance_Number: (!args.Statutory_Insurance_Number) ? (null) : (args.Statutory_Insurance_Number),
                                    PRAN_No: (!args.PRAN_No) ? (null) : (args.PRAN_No),
                                    UAN: (!args.UAN) ? (null) : (args.UAN),
                                    Tax_Code: (!args.Tax_Code) ? (null) : (args.Tax_Code),
                                    Hobbies: (!args.hobbies) ? (null) : (args.hobbies),
                                    No_Dependent: (args.No_Dependent === true) ? (true) : (false),
                                    Relative_In_Company: (args.Relative_In_Company === true) ? (true) : (false)
                                })
                                .transacting(trc)
                                .then(async () => {
                                    var License_Issue_Date = (!args.License_Issue_Date) ? (null) : ((isNaN(Number(args.License_Issue_Date))) ? dateFormat(new Date((args.License_Issue_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.License_Issue_Date)), "yyyy-mm-dd"));
                                    var License_Expiry_Date = (!args.License_Expiry_Date) ? (null) : ((isNaN(Number(args.License_Expiry_Date))) ? dateFormat(new Date((args.License_Expiry_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.License_Expiry_Date)), "yyyy-mm-dd"));

                                    var insertOrUpdateQuery = require('./insertOrUpdateQuery.js');
                                    var License_File_Name = args.License_File_Name ? args.License_File_Name : null;
                                    var DL = await insertOrUpdateQuery.insertOrUpdateDrivingLicense(args.Org_Code, url[0].Candidate_Id, args.Driving_License_No,
                                        License_Issue_Date, License_Expiry_Date, args.Issuing_Authority, args.Issuing_Country, args.Issuing_State, args.Vehicle_Type, License_File_Name);


                                    var Issue_Date = (!args.Issue_Date) ? (null) : ((isNaN(Number(args.Issue_Date))) ? dateFormat(new Date((args.Issue_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Issue_Date)), "yyyy-mm-dd"));
                                    var Expiry_Date = (!args.Expiry_Date) ? (null) : ((isNaN(Number(args.Expiry_Date))) ? dateFormat(new Date((args.Expiry_Date)), "yyyy-mm-dd") : dateFormat(new Date(Number(args.Expiry_Date)), "yyyy-mm-dd"));
                                    var Passport_File_Name = args.Passport_File_Name ? args.Passport_File_Name : null;

                                    var PP = await insertOrUpdateQuery.insertOrUpdatePassport(args.Org_Code, url[0].Candidate_Id, args.Passport_No, Issue_Date, args.Passport_Issuing_Country, args.Passport_Issuing_Authority, Expiry_Date, Passport_File_Name);


                                    return knexconfig('candidate_contact_details')
                                        .where("Candidate_Id", url[0].Candidate_Id)
                                        .update({
                                            pApartment_Name: (!args.pApartment_Name) ? (null) : (args.pApartment_Name),
                                            pStreet_Name: (!args.pStreet_Name) ? (null) : (args.pStreet_Name),
                                            pCity: (!args.pCity) ? (null) : (args.pCity),
                                            pCity_Id: (!args.pCity_Id) ? (null) : (args.pCity_Id),
                                            pState: (!args.pState) ? (null) : (args.pState),
                                            pBarangay: (!args.pBarangay) ? (null) : (args.pBarangay),
                                            pBarangay_Id: (!args.pBarangay_Id) ? (null) : (args.pBarangay_Id),
                                            pRegion: (!args.pRegion) ? (null) : (args.pRegion),
                                            pCountry: (!args.pCountry) ? (null) : (args.pCountry),
                                            pPincode: (!args.pPincode) ? (null) : (args.pPincode),
                                            cApartment_Name: (!args.cApartment_Name) ? (null) : (args.cApartment_Name),
                                            cStreet_Name: (!args.cStreet_Name) ? (null) : (args.cStreet_Name),
                                            cCity: (!args.cCity) ? (null) : (args.cCity),
                                            cCity_Id: (!args.cCity_Id) ? (null) : (args.cCity_Id),
                                            cState: (!args.cState) ? (null) : (args.cState),
                                            cBarangay: (!args.cBarangay) ? (null) : (args.cBarangay),
                                            cBarangay_Id: (!args.cBarangay_Id) ? (null) : (args.cBarangay_Id),
                                            cRegion: (!args.cRegion) ? (null) : (args.cRegion),
                                            cCountry: (!args.cCountry) ? (null) : (args.cCountry),
                                            cPincode: (!args.cPincode) ? (null) : (args.cPincode),
                                            Use_Location_Address: (!args.Use_Location_Address) ? 0 : (args.Use_Location_Address),
                                            oApartment_Name: (!args.oApartment_Name) ? (null) : (args.oApartment_Name),
                                            oStreet_Name: (!args.oStreet_Name) ? (null) : (args.oStreet_Name),
                                            oCity: (!args.oCity) ? (null) : (args.oCity),
                                            oCity_Id: (!args.oCity_Id) ? (null) : (args.oCity_Id),
                                            oState: (!args.oState) ? (null) : (args.oState),
                                            oBarangay: (!args.oBarangay) ? (null) : (args.oBarangay),
                                            oBarangay_Id: (!args.oBarangay_Id) ? (null) : (args.oBarangay_Id),
                                            oRegion: (!args.oRegion) ? (null) : (args.oRegion),
                                            oCountry: (!args.oCountry) ? (null) : (args.oCountry),
                                            oPincode: (!args.oPincode) ? (null) : (args.oPincode),
                                            Land_Line_No: (!args.Land_Line_No) ? (null) : (args.Land_Line_No),
                                            Mobile_No: (!args.Mobile_No) ? (null) : (args.Mobile_No),
                                            Mobile_No_Country_Code: (!args.Mobile_No_Country_Code) ? (null) : (args.Mobile_No_Country_Code),
                                            Fax_No: (!args.Emergency_Contact_No) ? (null) : (args.Emergency_Contact_No),
                                            Emergency_Contact_Name: (!args.Emergency_Contact_Name) ? (null) : (args.Emergency_Contact_Name),
                                            Emergency_Contact_Relation: (!args.Emergency_Contact_Relation) ? (null) : (args.Emergency_Contact_Relation),
                                        })
                                        .transacting(trc)
                                        .then(() => {
                                            var givenLang = [];
                                            /**push languages known if exists */
                                            if (args.Lang_Known?.length) {
                                                var Lang_Known = args.Lang_Known;
                                                var length = (Lang_Known).length;
                                                for (i = 0; i < length; i++) {
                                                    var data = {
                                                        Candidate_Id: url[0].Candidate_Id,
                                                        Lang_Known: Lang_Known[i]?.Lang_Known,
                                                        Lang_Spoken: Lang_Known[i]?.Lang_Spoken,
                                                        Lang_Read_Write: Lang_Known[i]?.Lang_Read_Write,
                                                        Lang_Proficiency: Lang_Known[i]?.Lang_Proficiency
                                                    }
                                                    givenLang.push(data);
                                                }
                                                if (givenLang?.length) {
                                                    const checkLangKnown = givenLang.filter((item, index, self) => self.findIndex(obj => obj.Lang_Known === item.Lang_Known) !== index);
                                                    if (checkLangKnown?.length > 0) {
                                                        throw 'ESS0155'
                                                    }
                                                }
                                            }
                                            return knexconfig('candidate_language')
                                                .where('Candidate_Id', url[0].Candidate_Id)
                                                .del()
                                                .transacting(trc)
                                                .then(() => {
                                                    if (givenLang.length) {
                                                        return knexconfig('candidate_language')
                                                            .insert(givenLang)
                                                            .transacting(trc)
                                                            .then(() => {

                                                                return {
                                                                    message: "Personal info inserted"
                                                                }

                                                            })
                                                    } else {
                                                        return {
                                                            message: "Personal info inserted"
                                                        }
                                                    }
                                                })
                                        })
                                    // })
                                    // })
                                })
                        }
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                console.log('return successful response from updatePersonalInfo');
                return result;
            }).catch(function (err) {
                console.log('Error in updatePersonalInfo', err);
                if (err.message == "REO0101") {
                    console.log('URL not found')
                    throw new ApolloError("URL not found", "REO0101")
                } else if (err == 'ESS0155') {
                    throw new ApolloError("You’ve selected the same language more than once. Please pick unique languages.", "ESS0155");
                }
                else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;