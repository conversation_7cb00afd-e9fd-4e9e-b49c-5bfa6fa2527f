const resolvers = {
    Query : {
        listCountry : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig("country")
            .then((country)=>{
                if(!country[0]){
                    return {
                        country: null
                    }
                } else{
                    return {
                        country: country
                    }
                }
            })
            .catch(function(err){
                console.log('Error in listCountry',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;