//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs, validateInputs } = require('../../common/commonFunctions')

//function to add / update bank details
module.exports.addUpdateBankDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateBankDetails function');
    let organizationDbConnection;
    let validationError = {};
    let isUpdate = 0;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const isIfscValid = await validateInputs(organizationDbConnection, 136);
            const fieldValidations = {
                bankAccountNumber: "IVE0381",
                branchName: "IVE0382",
                bsb: "IVE0385",
            };
            if (args.ifsc && isIfscValid) {
                fieldValidations.ifsc = "IVE0384";
            }
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let bankData = {
                    Candidate_Id: args.candidateId,
                    Bank_Account_Name: args.bankAccountName || null,
                    Bank_Account_Number: args.bankAccountNumber,
                    Emp_Bank_Id: args.empBankId,
                    Bank_Name: "",
                    Branch_Name: args.branchName,
                    IFSC_Code: args.ifsc,
                    BSB_Code: args.bsb,
                    Street: args.bank_streetName,
                    City: args.bank_city,
                    State: args.bank_state,
                    Zip: args.bank_pinCode,
                    Account_Type_Id: args.accountType,
                    Credit_Account: args.creditAccount,
                    Beneficiary_Id: args.beneficiaryId,
                    Status: args.status,
                    File_Name: args.fileName
                }
                if (args.status == 'Active') {
                    let isNotActive = await checkForActiveBankDetails(organizationDbConnection, args)
                    if (!isNotActive) {
                        throw 'IO0125'
                    }
                }
                return (
                    organizationDbConnection
                        .transaction(async function (trx) {
                            //Already Exists Account Id
                            let bankAccountNumberAlreadyExists = await organizationDbConnection(ehrTables.empBankDetails)
                                .select("Bank_Account_Number")
                                .where(function () {
                                    this.where('Bank_Account_Number', args.bankAccountNumber)
                                    this.where('Status', 'Active')
                                })

                            if (bankAccountNumberAlreadyExists && bankAccountNumberAlreadyExists.length) {
                                //Bank Account Number Already Exists
                                throw 'IO0126'
                            }
                            if (args.bankId) {
                                return (
                                    organizationDbConnection(ehrTables.candidateBankDetails)
                                        .update(bankData)
                                        .where('Bank_Id', args.bankId)
                                        .transacting(trx)
                                        .then((updateBank) => {
                                            if (updateBank) {
                                                isUpdate = 1;
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the bank details', bankData)
                                                throw 'IO0127'
                                            }
                                        })
                                )
                            } else {
                                return (
                                    organizationDbConnection(ehrTables.candidateBankDetails)
                                        .insert(bankData)
                                        .transacting(trx)
                                        .then((insertBank) => {
                                            if (insertBank) {
                                                return 'success'
                                            } else {
                                                console.log('Error while inserting the bank details', bankData)
                                                throw 'IO0127'
                                            }
                                        })
                                )
                            }

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The candidate bank details has been ${isUpdate ? 'updated' : 'added'}.`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);
                                organizationDbConnection ? organizationDbConnection.destroy() : null;

                                return { errorCode: "", message: "Candidate Bank details has been added/updated successfully" };
                            } else {
                                throw 'IO0127'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateBankDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0127');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the bank details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateBankDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateBankDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0024');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

async function checkForActiveBankDetails(organizationDbConnection, args) {
    try {
        return (
            organizationDbConnection(ehrTables.candidateBankDetails)
                .select("Bank_Id")
                .where("Candidate_Id", args.candidateId)
                .andWhere("Status", "Active")
                .where(function () {
                    if (args.bankId) {
                        this.whereNot("Bank_Id", args.bankId)
                    }
                })
                .then((data) => {
                    if (data && data.length) {
                        return false
                    } else {
                        return true
                    }
                })
                .catch((err) => {
                    console.log('Error in checkForActiveBankDetails .catch()', err)
                    throw err
                })
        )
    } catch (err) {
        console.log('Error in checkForActiveBankDetails main catch()', err)
        throw err
    }
}