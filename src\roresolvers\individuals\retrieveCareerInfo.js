// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appConstants');

module.exports.retrieveCareerInfo = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveCareerInfo function.");
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const data = await getCareerInfo(organizationDbConnection, args);
        if (data) {
            const { educationalInfoDetails, certificateInfoDetails, trainingInfoDetails, awardDetails, skillDetails } = data;
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return {
                errorCode: "",
                message: "Candidate Career info details retrieved successfully.",
                awardDetails: JSON.stringify(awardDetails),
                skillDetails: JSON.stringify(skillDetails)
            };
        } else {
            throw 'IO0115'
        }
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveCareerInfo function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'IO0014');
        throw new ApolloError(errResult.message, errResult.code);
    }
};

async function getCareerInfo(organizationDbConnection, args) {
    try {
        const [awardDetails, skillDetails] = await Promise.all([
            organizationDbConnection(ehrTables.candidateAwards)
                .select("*")
                .where('Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidateSkills)
                .select("*")
                .where('Candidate_Id', args.candidateId)
        ]);

        return {
            awardDetails,
            skillDetails
        };
    } catch (err) {
        console.log('Error in getCareerInfo main function', err);
        throw err;
    }
}
