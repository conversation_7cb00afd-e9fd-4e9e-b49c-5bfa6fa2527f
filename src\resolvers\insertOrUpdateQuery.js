
async function insertOrUpdateDrivingLicense(Org_Code,Candidate_Id,Driving_License_No,
    License_Issue_Date,License_Expiry_Date,Issuing_Authority,Issuing_Country,Issuing_State,Vehicle_Type,License_File_Name){

    var dbConnection = require('./dbConnection');
    var connection = await dbConnection.getConnection(Org_Code);
    const knexconfig = require('knex')(connection);

    if(!Driving_License_No && !License_Issue_Date && !License_Expiry_Date && !Issuing_Authority && !Issuing_Country &&! Issuing_State && !Vehicle_Type && !License_File_Name){
        return "Neither inserted or updated driving license ";
    } else {
        return knexconfig.transaction(function(trc){
            return knexconfig('candidate_drivinglicense')
            .where("Candidate_Id",Candidate_Id)
            .then((candidate)=>{
                if(!candidate[0]){
                    let candidateLicenseDetails={
                        Candidate_Id: Candidate_Id,
                        Driving_License_No:  Driving_License_No,
                        License_Issue_Date: License_Issue_Date,
                        License_Expiry_Date: License_Expiry_Date,
                        Issuing_Authority: Issuing_Authority,
                        Issuing_Country: Issuing_Country,
                        Issuing_State: Issuing_State,
                        Vehicle_Type: Vehicle_Type,
                    }
                    if(License_File_Name){
                        candidateLicenseDetails.File_Name=License_File_Name
                    }
                    return knexconfig('candidate_drivinglicense')
                    .insert(candidateLicenseDetails)
                    .transacting(trc)
                    .then(()=>{
                        return "driving license inserted";
                    })
                }
                else
                {
                    let candidateLicenseDetails={
                        Driving_License_No:  Driving_License_No,
                        License_Issue_Date: License_Issue_Date,
                        License_Expiry_Date: License_Expiry_Date,
                        Issuing_Authority: Issuing_Authority,
                        Issuing_Country: Issuing_Country,
                        Issuing_State: Issuing_State,
                        Vehicle_Type: Vehicle_Type,
                    }
                    if(License_File_Name){
                        candidateLicenseDetails.File_Name=License_File_Name
                    }
                return knexconfig('candidate_drivinglicense')
                .where("Candidate_Id",Candidate_Id)
                .update(candidateLicenseDetails)
                .transacting(trc)
                .then(()=>{
                    return "driving license updated";
                })
            }
        }).then(trc.commit)
        .catch(trc.rollback);
    }).then(function(result){
        console.log('return successful response from insert or update driving license')
        return result;
    }).catch(function(err){
        console.log('Error in insert or update driving license',err);
        throw new Error('Something went wrong');
    }).finally(() => {
        knexconfig.destroy();
    })
    }
}

async function insertOrUpdatePassport(Org_Code,Candidate_Id,Passport_No,Issue_Date, Issuing_Country, Issuing_Authority, Expiry_Date,Passport_File_Name){
    var dbConnection = require('./dbConnection');
    var connection = await dbConnection.getConnection(Org_Code);
    const knexconfig = require('knex')(connection);

    if(!Passport_No && !Issue_Date && !Expiry_Date && !Passport_File_Name){
        return "Neither inserted or updated passport";
    } else {
        return knexconfig.transaction(function(trc){

        return knexconfig('candidate_passport')
        .where("Candidate_Id",Candidate_Id)
        .then((candidate)=>{
            if(!candidate[0]){
                let candidatePassportDetails={
                    Candidate_Id: Candidate_Id,
                    Passport_No: Passport_No,
                    Issue_Date: Issue_Date,
                    Issuing_Authority: Issuing_Authority,
                    Issuing_Country: Issuing_Country,
                    Expiry_Date: Expiry_Date,
                }
                if(Passport_File_Name){
                    candidatePassportDetails.File_Name=Passport_File_Name
                }
                return knexconfig('candidate_passport')
                .insert(candidatePassportDetails).then(()=>{
                    return "passport inserted";
                })
            } else {
                let candidatePassportDetails={
                    Passport_No: Passport_No,
                    Issue_Date: Issue_Date,
                    Expiry_Date: Expiry_Date,
                    Issuing_Authority: Issuing_Authority,
                    Issuing_Country: Issuing_Country,
                }
                if(Passport_File_Name){
                    candidatePassportDetails.File_Name=Passport_File_Name
                }
                return knexconfig('candidate_passport')
                .where("Candidate_Id",Candidate_Id)
                .update(candidatePassportDetails).then(()=>{
                    return "passport updated";
                })
            }
        }).then(trc.commit)
        .catch(trc.rollback);
    }).then(function(result){
        console.log('return successful response from insert or update passport')
        return result;
    }).catch(function(err){
        console.log('Error in insert or update passport',err);
        throw new Error('Something went wrong');
    }).finally(() => {
        knexconfig.destroy();
    })
    }
}

module.exports.insertOrUpdateDrivingLicense = insertOrUpdateDrivingLicense;
module.exports.insertOrUpdatePassport = insertOrUpdatePassport;