const resolvers = {
    Query: {
        listEmployeeType: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);

            return knexconfig.transaction(function (trc) {
                return knexconfig("employee_type")
                    .select('EmpType_Id',
                        knexconfig.raw(`
                        CASE 
                            WHEN Employee_Type_Code IS NOT NULL 
                            THEN CONCAT(Employee_Type_Code, ' - ', Employee_Type) 
                            ELSE Employee_Type 
                        END AS Employee_Type
                    `),
                    )
                    .where('EmployeeType_Status', 'Active')
                    .then((employeeType) => {
                        if (!employeeType[0]) {
                            return {
                                employeeType: null
                            }
                        } else {
                            return {
                                employeeType: employeeType
                            }
                        }
                    })
                    .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                console.log('return successful response from listEmployeeType')
                return result;
            }).catch(function (err) {
                console.log('Error in listEmployeeType', err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;