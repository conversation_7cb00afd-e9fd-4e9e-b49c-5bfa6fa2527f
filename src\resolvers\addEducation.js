const resolvers = {
    Mutation : {
        addEducation : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError, UserInputError } = require('apollo-server-lambda');

            var Candidate_Id;
            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        Candidate_Id = url[0].Candidate_Id;
                        if (
                            args.Year_Of_Passing &&
                            (!/^\d{4}$/.test(args.Year_Of_Passing) || 
                              args.Year_Of_Passing < new Date().getFullYear() - 100)
                          ) {
                            throw new Error('Invalid year of passing');
                          }
                        return knexconfig('candidate_education')
                        .insert({
                            Candidate_Id: Candidate_Id,
                            Education_Type: args.Education_Type,
                            Specialisation: (!args.Specialisation) ? ('') : (args.Specialisation),
                            Institute_Name: (!args.Institute_Name) ? ('') : (args.Institute_Name),
                            University: (!args.University) ? ('') : (args.University),
                            Year_Of_Start: (!args.Year_Of_Start) ? null : (args.Year_Of_Start),
                            Year_Of_Passing: (!args.Year_Of_Passing) ? null : (args.Year_Of_Passing),
                            Percentage:  (!args.Percentage) ? (null) : (args.Percentage),
                            Grade:  (!args.Grade) ? (null) : (args.Grade),
                            Specialization_Id:  (!args.Specialization_Id) ? (null) : (args.Specialization_Id),
                            Institution_Id: (!args.Institution_Id) ? (null) : (args.Institution_Id),
                            Start_Date: (!args.Start_Date) ? (null) : (args.Start_Date),
                            End_Date: (!args.End_Date) ? (null) : (args.End_Date),
                            City: (!args.City) ? (null) : (args.City),
                            State: (!args.State) ? (null) : (args.State),
                            Country: (!args.Country) ? (null) : (args.Country)
                        })
                        .transacting(trc)
                        .then((dataId)=>{
                            if(!args.File_Name){
                                return {
                                    message:"Education detail added"
                                }
                            } else {
                                return knexconfig('candidate_education_documents')
                                .insert({
                                    Education_Id: dataId,
                                    File_Name: args.File_Name,
                                    Document_Name: args.Document_Name,
                                    Sub_Type_Id: args.Sub_Type_Id
                                })
                                .transacting(trc)
                                .then((documentId)=>{
                                    return {
                                        message:"Education detail added"
                                    }
                                })
                            }
                        })
                    }
                }).then(trc.commit)
                .catch(trc.rollback);
            }).then(function(result){
                return knexconfig.select('candidate_education.*',
                    'candidate_education_documents.File_Name',
                    'candidate_education_documents.Document_Name',
                    'candidate_education_documents.Sub_Type_Id',
                    'course_details.Course_Name as Education_Type_Name'
                    )
                .from('candidate_education')
                .leftJoin('course_details','candidate_education.Education_Type','course_details.Course_Id')
                .leftJoin('candidate_education_documents','candidate_education.Education_Id','candidate_education_documents.Education_Id')
                .where('candidate_education.Candidate_Id',Candidate_Id)
                .orderBy('candidate_education.Education_Id')
                .then((education)=>{
                    if(!education[0]){
                        return {
                            education: null
                        }
                    } else {
                        return knexconfig('candidate_personal_info')
                        .where('Candidate_Id',Candidate_Id)
                        .update({
                            Is_Illiterate: 1
                        })
                        .then(()=>{
                            return {
                                education: education
                            }
                        })
                    }
                })
            }).catch(function(err){
                console.log('Error in addEducation',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else if (err.message == "Invalid year of passing"){
                    throw new UserInputError(err.message)
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;