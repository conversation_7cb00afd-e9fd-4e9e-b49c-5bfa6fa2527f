const { ehrTables } = require('./tableAlias')
const AWS = require("aws-sdk");
let moment = require('moment-timezone');
let randomize = require('randomatic');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { validateWithRules } = require('@cksiva09/validationlib/src/validator');
const SHA3 = require('sha3');
const { formIds } = require('./appConstants');

async function getDataFromCandidateUrlAccordingToUrlHash(organizationDbConnection, urlHash) {
    try {
        return (
            organizationDbConnection(ehrTables.candidateUrl)
                .select('*')
                .where('Url_Hash', urlHash)
                .then(data => {
                    return data;
                })
                .catch(e => {
                    console.log("Error in getDataFromCandidateUrlAccordingToUrlHash() function .catch block", e);
                    return false;
                })
        )
    }
    catch (e) {
        console.log("Error in getDataFromCandidateUrlAccordingToUrlHash() function main catch block", e);
        return false;
    }
}
async function fetchJobRoleDetails(jobInfoDetails, organizationDbConnection) {
    try {
        // Get all unique job role IDs
        const jobRoleIds = Array.from(
            new Set(
                jobInfoDetails.flatMap(
                    ({ Job_Role_Ids }) => JSON.parse(Job_Role_Ids || '[]')
                )
            )
        );

        // Fetch all job roles
        const jobRoles = jobRoleIds.length ? await organizationDbConnection(`${ehrTables.jobRoles} as JR`)
            .select(
                organizationDbConnection.raw(`
                    CASE WHEN JR.Job_Role_Code IS NOT NULL AND TRIM(JR.Job_Role_Code) != '' 
                    THEN CONCAT(JR.Job_Role_Code, ' - ', JR.Job_Role) 
                    ELSE JR.Job_Role END AS Job_Role_Name
                `),
                'JR.Job_Role_Id'
            )
            .whereIn('JR.Job_Role_Id', jobRoleIds) : [];

        // Create lookup object
        const jobRolesMap = Object.fromEntries(
            jobRoles.map(role => [role.Job_Role_Id, role])
        );

        // Map results with job roles
        return jobInfoDetails.map(record => ({
            ...record,
            Job_Role_Ids: JSON.parse(record.Job_Role_Ids || '[]'),
            Job_Role_Details: JSON.parse(record.Job_Role_Ids || '[]')
                .map(id => jobRolesMap[id])
                .filter(Boolean)
        }));
        
    } catch (e) {
        console.log('Error in fetchJobRoleDetails:', e);
        throw e;
    }
}
//function to validate recieved date,expiry date and retrieve the date in desired format
async function validateInputDateAndGetDesiredDateFormat(receivedDate, expiryDate) {
    try {
        let validationError = {}
        let receivedDateValid;
        if(receivedDate){
        receivedDate = moment(receivedDate)
        receivedDateValid = receivedDate.isValid();
        if (!receivedDateValid) {
            validationError['IVE0233'] = commonLib.func.getError('', 'IVE0233').message;
        }
        else {
            receivedDate = receivedDate.format('YYYY-MM-DD');
        }
    }
        if (expiryDate) {
            expiryDate = moment(expiryDate)
            let expiryDateValid = expiryDate.isValid()
            if (!expiryDateValid) {
                validationError['IVE0234'] = commonLib.func.getError('', 'IVE0234').message;
            }
            else {
                expiryDate = expiryDate.format('YYYY-MM-DD');
                if (receivedDateValid) {
                    if (expiryDate < receivedDate) {
                        validationError['IVE0235'] = commonLib.func.getError('', 'IVE0235').message;
                    }
                }
            }
        }
        return { 'validationError': validationError, 'receivedDate': receivedDate, 'expiryDate': expiryDate }
    }
    catch (e) {
        console.log('Error in validateInputDateAndGetDesiredDateFormat() main catch Block', e);
        throw e;
    }
}

async function isValidExtension(category, extension) {
    try {
        const profileImageFileFormats = [
            'jpg',
            'jpeg',
            'png'
        ];
        const documentFileFormats = [
            'jpg',
            'jpeg',
            'png',
            'pdf',
            'tiff',
            'tif',
            'doc',
            'docx'

        ];

        switch (category) {
            case "profileImage": return profileImageFileFormats.includes(extension);
            case "document":
            case "accreditation":
            case "experience": return documentFileFormats.includes(extension);
            default: return false;
        }
    }
    catch (e) {
        console.log('Error in isValidExtension() main catch block.', e);
        throw (e);
    }
}

//function to get location details based on locationid
async function getLocationDetails(organizationDbConnection, locationId) {
    try {
        return (
            organizationDbConnection(ehrTables.location)
                .select('*')
                .where('Location_Id', locationId)
                .then(data => {
                    return data;
                })
                .catch(e => {
                    console.log('Error in getLocationDetails .catch block', e);
                    return false;
                })
        )
    }
    catch (e) {
        console.log('Error in getLocationDetails main catch block', e);
        return false;
    }
}

async function encodeUsingBase64(data) {
    try {
        let buff = Buffer.from(data);
        let base64data = buff.toString('base64');
        return base64data;
    }
    catch (e) {
        console.log("Error in encodeUsingBase64 main catch block", e);
        throw (e);
    }
}


async function decodeUsingBase64(url) {
    try {
        let index = url.indexOf('?');
        let decodeParam = (url.substr(index + 1));
        let leftUrl = url.substr(0, index);
        let buff = Buffer.from(decodeParam, 'base64');
        let decodedData = buff.toString('ascii');
        let newUrl = leftUrl + '?' + decodedData
        return newUrl;
    }
    catch (e) {
        console.log("Error in decodeUsingBase64 main catch block", e);
        throw (e);
    }
}

//function to send email through amazon ses
async function sendEmail(params) {
    AWS.config.update({
        region: process.env.sesTemplatesRegion
    });
    const ses = new AWS.SES({
        apiVersion: "2010-12-01"
    });

    try {
        return (
            await ses.sendTemplatedEmail(params).promise().then(function (data) {
                // console.log("Success", data);
                return true;
            })
                .catch(function (err) {
                    console.log("Error", err);
                    return false;
                })
        )
    }
    catch (err) {
        console.log("err", err);
        throw new Error("Mail hasn't sent, Please try again later")
    }
}

//function to send bulk email ses
async function sendBulkEmail(params) {
    AWS.config.update({
        region: process.env.sesTemplatesRegion
    });
    const ses = new AWS.SES({
        apiVersion: "2010-12-01"
    });

    try {
        let response = await ses.sendBulkTemplatedEmail(params).promise()
        console.log(response)
        // Handle promise's fulfilled/rejected states
        // response.then(
        //     function(data) {
        //     console.log(data);
        //     }).catch(
        //     function(err) {
        //     console.log(err, err.stack);
        //     });
    }
    catch (err) {
        console.log("err", err);
        throw new Error("Mail hasn't sent, Please try again later")
    }
}

async function getProbationDate(designationId, dateOfJoin, organizationDbConnection) {
    try {
        return (
            organizationDbConnection(ehrTables.designation)
                .select('Probation_Days as probationDays')
                .where('Designation_Id', designationId)
                .then(data => {
                    let probationDate = data[0].probationDays ? moment(dateOfJoin).add(data[0].probationDays, 'days').format('YYYY-MM-DD') : moment(dateOfJoin).format('YYYY-MM-DD');
                    return probationDate;
                })
                .catch(e => {
                    console.log('Error in getProbationDate .catch block', e);
                    return false;
                })
        )
    }
    catch (e) {
        console.log('Error in getProbationDate main catch block', e);
        return false;
    }
}

// Function to get organization name from database
async function getOrganizationName(organizationDb, orgCode) {
    try {
        // function to get organization name
        return await organizationDb(ehrTables.orgDetails)
            .select('Org_Name as orgName')
            .where('Org_Code', orgCode)
            .then((getOrgData) => {
                if (getOrgData[0]) {
                    return getOrgData[0];
                }
                else {
                    return { orgName: null }
                }
            })
    }
    catch (error) {
        console.log('Error in getOrganizationName function catch block', error);
        return { orgName: null }
    }
}

//Function to update invited vendor
async function updateResendVendor(organizationDb, vendorIds, loginEmployeeId, orgName, orgDetails, senderName = null) {
    try {
        return (
            organizationDb(ehrTables.invitedVendors)
                .select("Vendor_Id as vendorId", "Vendor_Name as vendorName", "Vendor_Email as vendorEmail", "Pass_Code as passCode", "Url as url", "Status as status", "URL_Expiry_Duration as expireDurationValue", "URL_Expiry_Duration_Measure as expireDurationType", "Expire_Time as expireTime")
                .whereIn('Vendor_Id', vendorIds)
                .andWhereNot('Status', 'Completed')
                .from(ehrTables.invitedVendors)
                .then(async (result) => {
                    if (result.length) {
                        var vendorDatas = result
                        let emailData = []
                        for await (let vendorData of vendorDatas) {
                            let expire_time;
                            let passCode = randomize('0', 6);
                            if (vendorData.expireDurationType == 'Minutes') {
                                expire_time = moment.utc().add(vendorData.expireDurationValue, 'minutes').format('YYYY-MM-DD HH:mm:ss');
                            } else if (vendorData.expireDurationType == 'Hours') {
                                expire_time = moment.utc().add(vendorData.expireDurationValue, 'hours').format('YYYY-MM-DD HH:mm:ss');
                            } else {
                                expire_time = moment.utc().add(vendorData.expireDurationValue, 'days').format('YYYY-MM-DD HH:mm:ss');
                            }
                            await organizationDb(ehrTables.invitedVendors)
                                .update({
                                    Pass_Code: passCode,
                                    Expire_Time: expire_time,
                                    Updated_On: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                                    Updated_By: loginEmployeeId,
                                })
                                .where('Vendor_Id', vendorData.vendorId)
                                .then(async () => {
                                    const templateData = {
                                        employeeName: vendorData.vendorName,
                                        orgLogo: orgDetails.logoPath ? orgDetails.logoPath : '',
                                        topCardImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + '/Email-Template-Images/topCard.png',
                                        redirectionUrl: vendorData.url,
                                        domainName: process.env.domainName.toUpperCase(),
                                        domainContentInSubject: "Hello",
                                        registrationImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + '/Email-Template-Images/RegistrationImage.png',
                                        notificationSubject: "Invitation to join " + orgName,
                                        isEmailClient: 0,
                                        isVendorBased: 1,
                                        isCandidate: 0,
                                        passCode: passCode,
                                        orgCode: orgName,
                                    }
                                    let originalSenderName = null;
                                    if(senderName){
                                        originalSenderName = senderName
                                    } else {
                                        if(organizationDb) {
                                            let result = await getSenderName(organizationDb);
                                            originalSenderName = result?.senderName;
                                        }
                                    }
                                    const params = {
                                        "Source": originalSenderName ? `${originalSenderName} <${process.env.emailFrom}>` : process.env.emailFrom,
                                        "Template": "welcomeEmailToClient",
                                        "Destination": {
                                            "ToAddresses": [vendorData.vendorEmail]
                                        },
                                        "ReplyToAddresses": orgDetails.hrAdminEmailAddress ? [orgDetails.hrAdminEmailAddress] : [process.env.emailReplyTo],
                                        "TemplateData": JSON.stringify(templateData),
                                        "Vendor_Id": vendorData.vendorId
                                    }
                                    emailData.push(params)
                                })
                        }
                        return emailData
                    }
                    else {
                        throw 'VO0130';
                    }
                }).catch((catchError) => {
                    console.log('Error in resendInviteVendor .catch() block', catchError);
                    return false
                })
        )
    } catch (err) {
        console.log('Error in resendInviteVendor function catch block', err);
        return false
    }
}
async function updateResendCandidate(organizationDbConnection,candidates,loginEmployeeId,orgName,orgDetails,trx, regionDetails, senderName = null) {

    try{
        let candidatesDetail=await organizationDbConnection('candidate_url as CU')
        .transacting(trx)
        .andWhereNot('Status', 'Finished')
        .select("CU.*"
        ).whereIn('CU.Candidate_Id',candidates).
        then(async (result) => {
            return result;
        }).catch((err)=>{
            throw err;
        });
        
        if(candidatesDetail && candidatesDetail.length){
            let Expire_Time;
            let candidateUrlDetail= await Promise.all(candidatesDetail.map(async (item)=>{
                if (item.URL_Expiry_Duration_Measure === 'Minutes') {
                    Expire_Time = moment.utc().add(item.URL_Expiry_Duration, 'minutes').format('YYYY-MM-DD HH:mm:ss');
                } else if (item.URL_Expiry_Duration_Measure === "Hours") {
                    Expire_Time = moment.utc().add(item.URL_Expiry_Duration, 'hours').format('YYYY-MM-DD HH:mm:ss');
                } else {
                    Expire_Time = moment.utc().add(item.URL_Expiry_Duration, 'days').format('YYYY-MM-DD HH:mm:ss');
                }

                let candidateUrlDetails = {
                    id: item.id,
                    Pin: randomize('?', 6, { chars: '123456789' }),
                    Expire_Time:Expire_Time,
                    Email_Resend_Status: 'Pending',
                    Updated_By:loginEmployeeId,
                    Updated_At: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                }

                // When upload the candidate we not have url and status will be "yet to be invited"
                if (item && !item.Url && item.Status && item.Status.toLowerCase() === 'yet to be invited') {
                    const locationDetails = await getLocationDetails(organizationDbConnection, item.Location_Id);
                    const countryCode = locationDetails?.[0]?.Country_Code || '';

                    const hash = new SHA3.SHA3Hash().update(` ${Date.now()}${item.Candidate_Id || ''}`).digest('hex');
                    const companyName = regionDetails && regionDetails.Org_Name ? encodeURIComponent(regionDetails?.Org_Name) : '';
                    let fullURL = `${process.env.baseAUUrlUI}${companyName}&country=${countryCode.toLowerCase()}&code=${orgDetails.orgCode}&identifier=${hash}&d_code=${regionDetails.Data_Region}&b_code=${regionDetails.Bucket_Region}`;
                    
                    const index = fullURL.indexOf('?');
                    fullURL = `${fullURL.substr(0, index)}?${await encodeUsingBase64(fullURL.substr(index + 1))}`;
                    Object.assign(candidateUrlDetails, { Url_Hash: hash, Url: fullURL, Status: 'Draft', Created_By: loginEmployeeId, Created_At: moment.utc().format('YYYY-MM-DD HH:mm:ss') });
                }
        
                return candidateUrlDetails;
            }))

            let queries = [];
            for (let i = 0; i < candidateUrlDetail.length; i++) {
                queries.push(
                    organizationDbConnection(ehrTables.candidateUrl)
                        .where('id', candidateUrlDetail[i].id) // Corrected where clause
                        .transacting(trx)
                        .update(candidateUrlDetail[i])
                );
            }
            
            try {
                await Promise.all(queries);

            } catch (error) {
                // Handle error
                console.error('Error in updateResendCandidate function candidate Promise rejection error', error);
                throw error;
            }
            let updatedCandidateDetails= await organizationDbConnection('candidate_url as CU').
            transacting(trx)
            .andWhereNot('Status', 'Finished')
            .select("CU.*"
            ).whereIn('CU.Candidate_Id',candidates).catch((err)=>{
                throw err;
            });
            
            let loggedInEmployeeIdEmail = loginEmployeeId ? await organizationDbConnection(ehrTables.empJob).select('Emp_Email').where('Employee_Id', loginEmployeeId).first() : null;

            let emailData=[];
            let buttonColors=await commonLib.func.getButtonColor(organizationDbConnection);
            const bucketName = process.env.documentsBucket
      const region=process.env.region;
            let fileNameString=await commonLib.func.getDocumentSubType(organizationDbConnection,orgName,bucketName,region);
            for (let i = 0; i < updatedCandidateDetails.length;i++) {
            const templateData = {
                    employeeName: updatedCandidateDetails[i].Name,
                    name: updatedCandidateDetails[i].Name,
                    redirectionUrl: updatedCandidateDetails[i].Url,
                    passCode: updatedCandidateDetails[i].Pin,
                    orgLogo: orgDetails.logoPath ? orgDetails.logoPath : '',
                    topCardImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + '/Email-Template-Images/topCard.png',
                    domainName: process.env.domainName.toUpperCase(),
                    domainContentInSubject: "Hello",
                    registrationImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + '/Email-Template-Images/RegistrationImage.png',
                    notificationSubject: "Invitation to join " + orgName,
                    isEmailClient: 0,
                    isVendorBased: 0,
                    isCandidate: 1,
                    bgColors:buttonColors?buttonColors:'',
                    docContent:fileNameString?'Below is the link to the list of pre-employment requirements and the hiring forms you need to accomplish.':'',
                    docLink:fileNameString?fileNameString:'',
                    orgCode: orgName,

            }
            let originalSenderName = null;
            if(senderName){
                originalSenderName = senderName
            } else {
                if(organizationDbConnection) {
                    let result = await getSenderName(organizationDbConnection);
                    originalSenderName = result?.senderName;
                }
            }
            const params = {
                "Source": originalSenderName ? `${originalSenderName} <${process.env.emailFrom}>` : process.env.emailFrom,
                "Template": "welcomeEmailToClient",
                "Destination": {
                    "ToAddresses": [updatedCandidateDetails[i].Email]
                },
                "ReplyToAddresses": loggedInEmployeeIdEmail.Emp_Email ? [loggedInEmployeeIdEmail.Emp_Email] : [],
                "TemplateData": JSON.stringify(templateData),
                "candidateId":candidatesDetail[i].id
            }
            emailData.push(params)
        }
            return emailData;
        }
        else{
            console.log("data not found");
            throw 'CCH0013';
        }
    }
    catch(err){
       throw err;
    }
}

// candidate resend details
async function resendCandidateEmail(email) {
    console.log("Inside resend email");
    let status;
    delete email.candidateId
    let emailResponse = await sendEmail(email);
    if (emailResponse) {
        status = 'Invited'
    } else {
        status = 'Pending'
    }
    return status;
}

// function to resend email
async function resendEmail(email) {
    console.log("Inside resend email");
    let status;
    delete email.Vendor_Id
    let emailResponse = await sendEmail(email);
    if (emailResponse) {
        status = 'Invited'
    } else {
        status = 'Pending'
    }
    return status;
}

async function sendCommonEmailTemplate(source, region, params) {
    try {
        let notificationParams = params
        notificationParams.Source = source
        if (!notificationParams.Template) {
            notificationParams.Template = 'CommonTemplate'
        }
        let sendEmailNotifications = await commonLib.func.sendBulkEmailNotifications(notificationParams, region)
        if (sendEmailNotifications) {
            return true
        } else {
            throw 'Error while sending bulk email notifications'
        }
    } catch (err) {
        console.log('Error in sendCommonEmailTemplate', err);
        throw err
    }
}

async function getHRGroupEmployeeEmails(organizationDbConnection) {
    try {
        const hrGroup = await organizationDbConnection(ehrTables.hrGroup).select('HR_Group_Id');

        if (hrGroup.length > 0) {
            const hrGroupId = hrGroup[0].HR_Group_Id;

            const hrEmailAddressData = await organizationDbConnection(ehrTables.empJob + " as EJ")
                .select("EJ.Emp_Email")
                .leftJoin(ehrTables.customEmployeeGroupEmployees + " as CGE", "CGE.Employee_Id", "EJ.Employee_Id")
                .where("CGE.Group_Id", hrGroupId)
                .andWhereNot('CGE.Type', 'Exclusion');


            //Get the hrEmailAddress where email is not empty
            return hrEmailAddressData.filter(item => item.Emp_Email !== null && item.Emp_Email !== '').map(item => item.Emp_Email)
        }
        return []
    } catch (err) {
        console.log('Error in getHRGroupEmployeeEmails', err);
        throw err;
    }
}

async function getHiringTeamEmployeeEmails(organizationDbConnection, candidateId) {
    try {
        
            //Candidate Mail Hiring manager and Recuiter sending Email 
       
            const hiringManagerEmails = await organizationDbConnection("candidate_recruitment_info as CRI")
                .select(organizationDbConnection.raw("GROUP_CONCAT(DISTINCT EJ1.Emp_Email) as recruiters"), 
                organizationDbConnection.raw("GROUP_CONCAT(DISTINCT EJ2.Emp_Email) as hiringManager") )
                .leftJoin("job_post_recruiters as JPR", "JPR.Job_Post_Id", "CRI.Job_Post_Id")
                .leftJoin("job_post_hiring_managers as JPHM", "JPHM.Job_Post_Id", "CRI.Job_Post_Id")
                .leftJoin("emp_job as EJ1", "EJ1.Employee_Id", "JPR.Recruiter_Id")
                .leftJoin("emp_job as EJ2", "EJ2.Employee_Id", "JPHM.Hiring_Manager_Id")
                .where("CRI.Candidate_Id", candidateId).groupBy('CRI.Job_Post_Id')
                
            let emailAddress = [];
            if(hiringManagerEmails && hiringManagerEmails.length > 0){
                emailAddress = hiringManagerEmails[0].recruiters ? hiringManagerEmails[0].recruiters.split(",").filter(item => item !== null && item!== '') : [];
                if(hiringManagerEmails[0].hiringManager)
                    emailAddress=  emailAddress.concat(hiringManagerEmails[0].hiringManager.split(",")) 
            }
            return emailAddress
       
    } catch (err) {
        console.log('Error in getHRGroupEmployeeEmails', err);
        throw err;
    }
}

function validateCandidateInputs(args, fieldValidations) {
    let validationError = {};

    for (const field in fieldValidations) {
        let fieldName = field; // By default, use the current field name
        if (field.includes('_')) {
            const parts = field.split('_');
            fieldName = parts[1]; // Use the name after the underscore
        }

        if (args[field]) {
            const validation = validateWithRules(args[field], fieldName);
            if (validation !== 'Validation not found' && (!validation.validation || !validation.length)) {
                validationError[fieldValidations[field]] = validation.length ? commonLib.func.getError('', fieldValidations[field]).message : commonLib.func.getError('', fieldValidations[field]).message1;
            }
        }
    }
    return validationError;
}

async function validateCandidateDetails(organizationDbConnection, candidateIds, validationError, isConvertEmployee = false){
    try {
        // Fetch candidate details in a single query
        let candidateDetails = await organizationDbConnection(ehrTables.candidateJob + " as CJ")
            .select('CP.*', 'CJ.*', 'CC.Mobile_No', 'CC.Mobile_No_Country_Code')
            .leftJoin(ehrTables.candidatePersonalInfo + " as CP", "CP.Candidate_Id", "CJ.Candidate_Id")
            .leftJoin(ehrTables.candidateContactDetails + " as CC", "CC.Candidate_Id", "CJ.Candidate_Id")
            .where('CJ.Candidate_Id', candidateIds);
        
        if(candidateDetails && candidateDetails.length){
            candidateDetails = candidateDetails[0]
        }else{
            throw ''
        }

        // Fetch any active employee with the same data
        let failedData = await organizationDbConnection(ehrTables.empJob + " as EJ")
            .select('EJ.User_Defined_EmpId', 'EJ.Emp_Status', 'EP.PAN', 'EP.Aadhaar_Card_Number', 'EP.Personal_Email', 'EP.UAN', 'EJ.Emp_Email', 'CD.Mobile_No', 'CD.Mobile_No_Country_Code')
            .leftJoin(ehrTables.empPersonalInfo + " as EP", "EP.Employee_Id", "EJ.Employee_Id")
            .leftJoin(ehrTables.contactDetails + " as CD", "CD.Employee_Id", "EJ.Employee_Id")
            .where(function() {
                if(candidateDetails.Emp_Email && candidateDetails.Emp_Email !== ''){
                    this.orWhere('EJ.Emp_Email', candidateDetails.Emp_Email)
                }
                if(candidateDetails.PAN && candidateDetails.PAN !== ''){
                    this.orWhere('EP.PAN', candidateDetails.PAN)
                }
                if(candidateDetails.Aadhaar_Card_Number && candidateDetails.Aadhaar_Card_Number !== ''){
                    this.orWhere('EP.Aadhaar_Card_Number', candidateDetails.Aadhaar_Card_Number)
                }
                if(candidateDetails.UAN && candidateDetails.UAN !== ''){
                    this.orWhere('EP.UAN', candidateDetails.UAN)
                }
                if(candidateDetails.Mobile_No && candidateDetails.Mobile_No !== '' && candidateDetails.Mobile_No_Country_Code && candidateDetails.Mobile_No_Country_Code !== ''){
                    this.orWhere(function() {
                        this.where('CD.Mobile_No', candidateDetails.Mobile_No)
                            .andWhere('CD.Mobile_No_Country_Code', candidateDetails.Mobile_No_Country_Code);
                    });
                }
                if(isConvertEmployee){
                    if (candidateDetails.User_Defined_EmpId && candidateDetails.User_Defined_EmpId.length) {
                        this.orWhere('EJ.User_Defined_EmpId', candidateDetails.User_Defined_EmpId)
                    }
                    if (candidateDetails.External_EmpId && candidateDetails.External_EmpId.length) {
                        this.orWhere('EJ.External_EmpId', candidateDetails.External_EmpId)
                    }
                }
            })
            if (failedData && failedData.length) {
                let uan = [], pan = [], aadharNumber = [], userDefined = [], biometric = [], mobileNo = [], email=[];
    
                //Loop through the data and check if the data is same
                failedData.forEach((item) => {
                    if (candidateDetails.Emp_Email && candidateDetails.Emp_Email.length && item.Emp_Email === candidateDetails.Emp_Email && item.Emp_Status === 'Active') {
                        email.push(item.User_Defined_EmpId);
                    }

                    if (candidateDetails.UAN && candidateDetails.UAN.length && item.UAN === candidateDetails.UAN && item.Emp_Status === 'Active') {
                        uan.push(item.User_Defined_EmpId);
                    }
                    if (candidateDetails.PAN && candidateDetails.PAN.length && item.PAN === candidateDetails.PAN && item.Emp_Status === 'Active') {
                        pan.push(item.User_Defined_EmpId);
                    }
                    if (candidateDetails.Aadhaar_Card_Number && candidateDetails.Aadhaar_Card_Number.length && item.Aadhaar_Card_Number === candidateDetails.Aadhaar_Card_Number && item.Emp_Status === 'Active') {
                        aadharNumber.push(item.User_Defined_EmpId);
                    }
                    if(candidateDetails.Mobile_No && candidateDetails.Mobile_No.length && item.Mobile_No == candidateDetails.Mobile_No && item.Mobile_No_Country_Code == candidateDetails.Mobile_No_Country_Code && item.Emp_Status === 'Active'){
                        
                        mobileNo.push(item.User_Defined_EmpId);
                    }
                    if(isConvertEmployee){
                        if (candidateDetails.User_Defined_EmpId && candidateDetails.User_Defined_EmpId.length && item.User_Defined_EmpId === candidateDetails.User_Defined_EmpId) {
                            userDefined.push(item.User_Defined_EmpId);
                        }
                        if (candidateDetails.External_EmpId && candidateDetails.External_EmpId.length && item.External_EmpId === candidateDetails.External_EmpId) {
                            biometric.push(item.User_Defined_EmpId);
                        }
                    }
                }
                );
    
                if (userDefined && userDefined.length) {
                    validationError['IVE0428'] = commonLib.func.getError('', 'IVE0428').message;
                }
                if (biometric && biometric.length) {
                    validationError['IVE0429'] = commonLib.func.getError('', 'IVE0429').message + ' ' + biometric.join(',');
                }
                if (uan && uan.length) {
                    validationError['IVE0431'] = commonLib.func.getError('', 'IVE0431').message + ' ' + uan.join(',');
                }
                if (pan && pan.length) {
                    validationError['IVE0424'] = commonLib.func.getError('', 'IVE0424').message + ' ' + pan.join(',');
                }
                if (aadharNumber && aadharNumber.length) {
                    validationError['IVE0425'] = commonLib.func.getError('', 'IVE0425').message + ' ' + aadharNumber.join(',');
                }
                if(mobileNo && mobileNo.length){
                    validationError['ESS0139'] = commonLib.func.getError('', 'ESS0139').message + ' ' + mobileNo.join(', ');
                }
                if(email && email.length){
                    validationError['EM0170'] = commonLib.func.getError('', 'EM0170').message + ' ' + email.join(', ');
                }
            }
            return validationError

    } catch(err) {
        console.log('Error in validateCandidateDetails function', err);
        throw err;
    }
}



async function sendEmailToCandidateManagerInitiatorHrGroup(result, type = "statusUpdate", emailData, senderName = null, organizationDbConnection = null) {
    try {
        let candidateStatus = emailData.status;
        let candidateName = emailData.candidateName
        let candidateEmail = emailData.candidateEmail;
        let orgLogo = emailData.orgLogo;
        let mailSubject = "", candidateSubTitle = "", hrGroupManagerSubTitle = "";
        let managerHrGroupMails = [];
        let originalSenderName = null;
        if(senderName){
            originalSenderName = senderName
        } else {
            if(organizationDbConnection) {
                let result = await getSenderName(organizationDbConnection);
                originalSenderName = result?.senderName;
            }
        }
        for (let group of result.hrGroupEmail) {
            managerHrGroupMails.push(group);
        }
        if (result.managerEmail) {
            managerHrGroupMails.push(result.managerEmail);
        }
        if(result.hiringTeamEmail && result.hiringTeamEmail.length > 0 ){
            for (let team of result.hiringTeamEmail) {
                managerHrGroupMails.push(team);
            }
        }
        managerHrGroupMails = [...new Set(managerHrGroupMails)];

        if (type === "statusUpdate") {
            if (candidateStatus === "Rejected") {
                mailSubject = "Self-Onboarding: Candidate verification failed";
                candidateSubTitle = `We regret to inform you that, upon thorough review, we were unable to verify some of the details you provided during the onboarding process. As a result, we are unable to confirm the accuracy of your information within our system. <br>
            We understand that discrepancies can occur, and we are here to assist you in resolving any issues. If you have questions or need further guidance, please don't hesitate to reach out to our HR department. They will provide you with the necessary support to address and rectify any discrepancies. <br>
            We apologize for any inconvenience this may cause and appreciate your understanding in this matter. Your accuracy and completeness of information are essential to ensure a successful onboarding process.`;
                hrGroupManagerSubTitle = `We would like to inform you that the candidate's verification process has indicated discrepancies or inaccuracies in the provided information. As a result, we need your assistance in reaching out to the candidate, ${candidateName}, to seek clarification and determine if the details can be reprocessed.`;
            } else {
                mailSubject = "Self-Onboarding: Candidate verification successful";
                candidateSubTitle = "Your onboarding details have been successfully verified, confirming the accuracy of your information within our system. If you have any questions or require further assistance, please feel free to contact our HR department for support.";
                hrGroupManagerSubTitle = `${candidateName}, has successfully completed their onboarding, and their details have been verified in our system. This marks an important step in the onboarding process, and we are now ready to proceed with the conversion of the candidate to an employee. <br>
            Please take the necessary action to convert ${candidateName} to an employee in our system promptly.`;
            }
        } else {
            mailSubject = `Onboarding Successful: Congratulations ${emailData.orgName || ''}!`;
            candidateSubTitle = "We are writing to inform you that your onboarding process has been successfully completed, and you have been officially converted to an employee within our organization.";
            hrGroupManagerSubTitle = `${candidateName}, has been successfully converted to an employee within our organization. This marks the final step in the onboarding process.`;
        }
        let candidateTemplate = {
            "emailSubject": mailSubject,
            "orgLogo": orgLogo,
            "title1": '',
            "title2": '',
            "centerImage": '',
            "subTitle": candidateSubTitle,
            "redirectionUrl": '',
            "buttonText": '',
            "footer": '',
            "supportEmail": ""
        }
        let hrGroupTemplate = {
            "emailSubject": mailSubject,
            "orgLogo": orgLogo,
            "title1": '',
            "title2": '',
            "centerImage": '',
            "subTitle": hrGroupManagerSubTitle,
            "redirectionUrl": type === "migrate" ? `https://${emailData.orgCode + process.env.fullDomainName}/v3/my-team/team-summary` : `https://${emailData.orgCode + process.env.fullDomainName}/v3/onboarding/onboarded-individuals`,
            "buttonText": 'Check Out',
            "footer": '',
            "supportEmail": ""
        }
        let designationInput = [
            {
                "Destination": {
                    "ToAddresses": [result.initiatorEmail],
                    "CcAddresses": [],
                    "BccAddresses": managerHrGroupMails
                },
                "ReplacementTemplateData": JSON.stringify(hrGroupTemplate)
            },
        ];
        if (candidateEmail) {
            designationInput.push({
                Destination: {
                    "ToAddresses": [candidateEmail]
                },
                "ReplacementTemplateData": JSON.stringify(candidateTemplate)
            });
        }
        const params = {
            "Source": originalSenderName ? `${originalSenderName} <${process.env.emailFrom}>` : process.env.emailFrom,
            "Template": "CommonTemplate",
            "Destinations": designationInput,
            "DefaultTemplateData": JSON.stringify(candidateTemplate),
        }
        await sendBulkEmail(params)
    } catch (err) {
        console.log('Error in sendEmailToCandidateManagerInitiatorHrGroup function', err)
        throw err
    }
}
async function coverageIsCustomGroup (organizationDbConnection){
    let coverage= await organizationDbConnection('recruitment_settings').pluck('Coverage').limit(1);
    if(coverage && coverage.length && coverage[0].toLowerCase()==='custom group'){
        return true;
    }
    else{
        return false;
    }
}
async function getCustomGroupList (organizationDbConnection,loginEmpId,formId){
    let customGroupArray= await organizationDbConnection('custom_employee_group_employees as CEGE').pluck('CEGE.Group_Id').where('CEGE.Employee_Id',loginEmpId)
    .innerJoin('custom_group_associated_forms as CGAF','CGAF.Custom_Group_Id','CEGE.Group_Id' )
    .whereIn("CEGE.Type", ['AdditionalInclusion', 'Default'])
    .where('CGAF.Form_Id',formId);
    if(customGroupArray && customGroupArray.length){
        return customGroupArray;
    }
    else{
        return [];
    }
}

async function getIndividualsRollAccess(organizationDbConnection, checkRights, loginEmpId){

    try{
        console.log("Inside getIndividualsRollAccess function.");
        let employeeRole = checkRights.Employee_Role === 'admin' ? 'admin' : (checkRights.Is_Manager === 1 ? 'manager' :
            (checkRights.Is_Recruiter && checkRights.Is_Recruiter.toLowerCase() === 'yes' ? 'recruiter'  : 'employee') );
        
         // need to use promise all here
        const [centralisedRecuitment, orgDetails] = await Promise.all([
            commonLib.func.getCentralisedRecuitment(organizationDbConnection),
            organizationDbConnection('org_details').select('Field_Force').first()
        ])
        const fieldForce = orgDetails && orgDetails.Field_Force ? orgDetails.Field_Force : 0 ;
        if(employeeRole !== 'admin' && fieldForce) {
            // Service Provider admin access check                
            let spCheckRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmpId, null, '', 'UI', false, 219);
            employeeRole = spCheckRights && spCheckRights.Role_Update === 1 ?  'sp-admin' : employeeRole;
        }


        if(employeeRole.toLowerCase() !== 'admin' && (employeeRole.toLowerCase() === 'sp-admin' || (fieldForce && centralisedRecuitment.toLowerCase() === 'no'))){
            let serviceProvider = await organizationDbConnection(ehrTables.empJob).select('Service_Provider_Id')
                .where('Employee_Id', loginEmpId)
               
            serviceProvider = serviceProvider && serviceProvider.length ?  [...new Set(serviceProvider.flat().map(row => row.Service_Provider_Id))] : [];
            return {isOrgUnitFlag: true, serviceProviderids : serviceProvider};
        }
        return {isOrgUnitFlag: false, serviceProviderids : []};

    }catch(err){
        console.error("Error: Inside getIndividualsRollAccess catch block ", err)
        throw err;
    }
}
async function sendCustomEmail(emailData, ses, event, senderName = null, organizationDbConnection = null) {
    try {
      const nodemailer = require("nodemailer");
      let originalSenderName = null;
      if(senderName){
        originalSenderName = senderName
      } else {
        if(organizationDbConnection) {
            let result = await getSenderName(organizationDbConnection);
            originalSenderName = result?.senderName;
        }
      }
      const {
        emails = {},
        Template_Content = "",
        Subject_Content = "",
      } = emailData;
      const {
        toEmails = [],
        ccEmails = [],
        bccEmails = [],
        additionalEmails = [],
      } = emails;
  
      if (!Subject_Content || !Template_Content) {
        throw "PBP0115";
      }
  
      if (!toEmails.length && !ccEmails.length && !bccEmails.length && !additionalEmails.length) {
        throw "PBP0116";
      }
  
      const transporter = nodemailer.createTransport({ SES: ses });
      const maxRecipients = 50;
      const batches = [];
      let currentBatch = { to: [], cc: [], bcc: [] };
      let currentBatchCount = 0;
  
      const addRecipients = (type, recipients) => {
        recipients.forEach((email) => {
          if (currentBatchCount >= maxRecipients) {
            batches.push({ ...currentBatch });
            currentBatch = { to: [], cc: [], bcc: [] };
            currentBatchCount = 0;
          }
          currentBatch[type].push(email);
          currentBatchCount++;
        });
      };
  
      // Add recipients to batches
      addRecipients("to", toEmails);
      addRecipients("cc", ccEmails);
      addRecipients("bcc", bccEmails);
      addRecipients("cc", additionalEmails);
  
      // Add the final batch if it contains recipients
      if (currentBatchCount > 0) {
        batches.push({ ...currentBatch });
      }
      let status;
      // Send emails batch by batch
      for (const batch of batches) {
        const mailOptions = {
          from: originalSenderName ? `${originalSenderName} <${event.Source}>` : event.Source,
          subject: Subject_Content,
          html: Template_Content,
          to: batch.to.length ? batch.to : undefined,
          cc: batch.cc.length ? batch.cc : undefined,
          bcc: batch.bcc.length ? batch.bcc : undefined,
          replyTo: event.ReplyToAddresses,
        };
          const info = await transporter.sendMail(mailOptions);
          if (info) {
            status = "Invited";
          } else {
            status = "Pending";
          }
      }
      return status;
    } catch (error) {
      console.error("Error in sendCustomEmail:", error);
      throw error;
    }
  }

  async function getSenderName(organizationDbConnection) {
    try {
        let data = await organizationDbConnection(ehrTables.emailNotificationSetting)
            .select('Sender_Name as senderName')
            .first();

        if (data) {
            return data;
        }
        return false
    }
    catch (err) {
        console.log('Error in getSenderName function.', err);
        return false
    }
}
async function validateInputs(organizationDbConnection, fieldId) {
    try {
        const result = await organizationDbConnection('customization_fields')
        .where({ Field_Id: fieldId }).select('Field_Alias').first();
        return result && result.Field_Alias && result.Field_Alias.toLowerCase() === 'ifsc code';
    } catch (error) {
        console.error('Error validating input:', error);
        return false
    }
}


async function getOnboardSpecialistRollAccess(organizationDbConnection, checkRights, loginEmpId, screenType){

    try{

        let employeeRole = checkRights.Employee_Role.toLowerCase() === 'admin' ? 'admin' : 
        (checkRights.Is_Recruiter && checkRights.Is_Recruiter.toLowerCase() === 'yes') ? 'recruiter'  : 'others';

        if(employeeRole === 'admin'){
            return { isAdmin: true, candidateIds: []};
        }

        let candidateIds = [];

        if(screenType === "onboarding" && employeeRole === 'others'){

            candidateIds = await organizationDbConnection('job_post_onboard_specialist as JPOS ')
            .pluck('CRI.Candidate_Id').distinct()
            .innerJoin('candidate_recruitment_info as CRI', 'CRI.Job_Post_Id', 'JPOS.Job_Post_Id')
            .where('JPOS.Onboard_Specialist_Id', loginEmpId);
            return { isAdmin: false, candidateIds: candidateIds};
        }


        let [centralisedRecuitment, orgDetails, isCustomGroup] = await Promise.all([
            commonLib.func.getCentralisedRecuitment(organizationDbConnection),
            organizationDbConnection('org_details').select('Field_Force').first(),
            coverageIsCustomGroup(organizationDbConnection),
        ]);

        const isFieldForce = orgDetails && orgDetails.Field_Force ? orgDetails.Field_Force : 0 ;

        if(screenType === "onboard-individuals" && employeeRole === 'others'){  // others only onboard-individuals 

            if(centralisedRecuitment.toLowerCase() === 'no' && isFieldForce === 1){
                candidateIds = await organizationDbConnection(ehrTables.candidateUrl + " as CU")
                .pluck('CU.Candidate_Id').distinct()
                .innerJoin(ehrTables.empJob + " as EJ", 'EJ.Service_Provider_Id', 'CU.Service_Provider_Id')
                .leftJoin('candidate_recruitment_info as CRI', 'CRI.Candidate_Id', 'CU.Candidate_Id')
                .leftJoin('job_post_onboard_specialist as JPOS', 'JPOS.Job_Post_Id', 'CRI.Job_Post_Id')
                .modify(function (queryBuilder) {
                    queryBuilder.where(function () {
                        this.where(function () {
                            this.where('EJ.Employee_Id', loginEmpId).whereNull('CRI.Job_Post_Id');
                        }).orWhere('JPOS.Onboard_Specialist_Id', loginEmpId);
                    });
                })  

            } else {
                candidateIds = await organizationDbConnection(ehrTables.candidateUrl + " as CU")
                .pluck('CU.Candidate_Id').distinct()
                .leftJoin('candidate_recruitment_info as CRI', 'CRI.Candidate_Id', 'CU.Candidate_Id')
                .leftJoin('job_post_onboard_specialist as JPOS', 'JPOS.Job_Post_Id', 'CRI.Job_Post_Id')
                .modify(function (queryBuilder) {
                    queryBuilder.where('JPOS.Onboard_Specialist_Id', loginEmpId).orWhereNull('CRI.Job_Post_Id');
                })
                
            }

        } else if (employeeRole === 'recruiter'){

            if (isCustomGroup) {

                const customGroupList = await getCustomGroupList(organizationDbConnection, loginEmpId, formIds.jobPost);

               
                candidateIds = await organizationDbConnection(ehrTables.candidatePersonalInfo + " as CPI")
                .pluck('CPI.Candidate_Id').distinct()
                .leftJoin('candidate_recruitment_info as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
                .leftJoin('job_post as JP', 'CRI.Job_Post_Id', 'JP.Job_Post_Id')
                .leftJoin('job_post_onboard_specialist as JPOS', 'JPOS.Job_Post_Id', 'CRI.Job_Post_Id')
                .modify(function (queryBuilder) {
                    if(screenType === "onboard-individuals"){
                        queryBuilder.where(function () {
                            this.whereIn('JP.Custom_Group_Id', customGroupList)
                            .orWhere('JPOS.Onboard_Specialist_Id', loginEmpId)
                            .orWhere('JP.Added_By', loginEmpId)
                            .orWhereNull('CRI.Job_Post_Id')
                        });
                    } else {
                        queryBuilder.where(function () {
                            this.whereIn('JP.Custom_Group_Id', customGroupList)
                            .orWhere('JPOS.Onboard_Specialist_Id', loginEmpId)
                            .orWhere('JP.Added_By', loginEmpId);
                        });
                    }
                })
                    
                

            } else if (isFieldForce && centralisedRecuitment.toLowerCase() === 'no') {

                candidateIds = await organizationDbConnection(ehrTables.candidatePersonalInfo + " as CPI")
                .pluck('CPI.Candidate_Id').distinct()
                .leftJoin('candidate_recruitment_info as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
                .leftJoin('job_post as JP', 'CRI.Job_Post_Id', 'JP.Job_Post_Id')
                .leftJoin(ehrTables.empJob + " as EJ", 'EJ.Service_Provider_Id', 'JP.Service_Provider_Id')
                .leftJoin('job_post_onboard_specialist as JPOS', 'JPOS.Job_Post_Id', 'CRI.Job_Post_Id')
                .modify(function (queryBuilder) {
                    if(screenType === "onboard-individuals"){
                        queryBuilder.where(function () {
                            this.where('EJ.Employee_Id', loginEmpId).orWhere('JP.Added_By', loginEmpId)
                            .orWhere('JPOS.Onboard_Specialist_Id', loginEmpId).orWhereNull('CRI.Job_Post_Id');
                        });
                       
                    } else {
                        queryBuilder.where(function () {
                            this.where('EJ.Employee_Id', loginEmpId).orWhere('JPOS.Onboard_Specialist_Id', loginEmpId).orWhere('JP.Added_By', loginEmpId);
                        });
                    }
                })
               
            } else {
                candidateIds = await organizationDbConnection(ehrTables.candidatePersonalInfo + ' as CPI')
                .pluck('CPI.Candidate_Id').distinct();
            }
        }

        candidateIds = [...new Set(candidateIds)];

        return { isAdmin: false, candidateIds:  candidateIds};

    } catch (error) {
        console.error("Error: Inside getOnboardSpecialistRollAccess main catch block: ", error)
        throw error;
    }
    
}
async function incrementNextNumber(organizationDbConnection, empPrefixSettingId) {
    console.log(`Incrementing next number for empPrefixSettingId: ${empPrefixSettingId}`);
    try {
        // Update the next number by incrementing it by 1
        const updateResult = await organizationDbConnection(ehrTables.empPrefixSettings)
            .where('Emp_Prefix_Setting_Id', empPrefixSettingId)
            .increment('Next_Number', 1);

        return updateResult > 0;
    } catch (err) {
        console.error('Error while incrementing next number:', err);
        throw err;
    }
}

function validateDateOfJoinAndDOB(dob, doj) {
    const ageAtJoining = moment(doj).diff(moment(dob), 'years');
    return ageAtJoining >= 14;
}


module.exports = {
    getDataFromCandidateUrlAccordingToUrlHash,
    validateInputDateAndGetDesiredDateFormat,
    isValidExtension,
    getLocationDetails,
    encodeUsingBase64,
    decodeUsingBase64,
    sendEmail,
    getProbationDate,
    getOrganizationName,
    updateResendVendor,
    updateResendCandidate,
    sendBulkEmail,
    resendEmail,
    resendCandidateEmail,
    sendCommonEmailTemplate,
    getHRGroupEmployeeEmails,
    validateCandidateInputs,
    sendEmailToCandidateManagerInitiatorHrGroup,
    validateCandidateDetails,
    coverageIsCustomGroup,
    getCustomGroupList,
    getHiringTeamEmployeeEmails,
    getIndividualsRollAccess,
    sendCustomEmail,
    getSenderName,
    fetchJobRoleDetails,
    validateInputs,
    getOnboardSpecialistRollAccess,
    incrementNextNumber,
    validateDateOfJoinAndDOB
}