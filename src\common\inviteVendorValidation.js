//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

//Function to validate invite vendor data

module.exports.validateInviteVendorData = async (args, isAdd) => {
    console.log('Inside validateInviteVendorData function');
    try {
        let validationError = {};
        var invitedVendorId = 0;

        if (isAdd == 0) {
            invitedVendorId = 0;
        } else {
            invitedVendorId = args.invitedVendorId;
            //Validate the invited vendor id
            if (!(args.invitedVendorId || commonLib.commonValidation.numberValidation(args.invitedVendorId))) {
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message1;
                throw '_EC0007'
            }
        }

        //Validate the vendor name
        if (args.vendorName) {
            if (!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.vendorName)) {
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message2;
            } 
            else if (!commonLib.commonValidation.checkLength(args.vendorName, 3, 50)) {
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message3;
            }
        } else {
            validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message4;
        }

        // validate the vendor email
        if (args.vendorEmail) {
            if (!commonLib.commonValidation.emailValidation(args.vendorEmail)) {
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message5;
            }
            else if (!commonLib.commonValidation.checkLength(args.vendorEmail, 5, 50)) {
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message6;
            }
        }else{
            validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message7;
        }

        //validate the location id
        if (args.locationId) {
            if(!commonLib.commonValidation.numberValidation(args.locationId)){
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message9;
            }
            else if (!commonLib.commonValidation.checkLength(args.locationId, 1, 10)) {
                validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message9;
            }
        }
        else {
            validationError['IVE0252'] = commonLib.func.getError('', 'IVE0252').message8;
        }

        //validate the expire value
        if(args.expireValue){
            if(!commonLib.commonValidation.numberValidation(args.expireValue)){
                validationError['IVE0260'] = commonLib.func.getError('', 'IVE0260').message1;
            }
            else if (!commonLib.commonValidation.checkLength(args.expireValue, 1, 60)) {
                validationError['IVE0260'] = commonLib.func.getError('', 'IVE0260').message2;
            }
        }else{
            validationError['IVE0260'] = commonLib.func.getError('', 'IVE0260').message3;
        }

        return validationError;
    }
    catch (err) {
        console.log('Error in the validateInviteVendorData() function in the main catch block.', err);
        throw err;
    }
}
