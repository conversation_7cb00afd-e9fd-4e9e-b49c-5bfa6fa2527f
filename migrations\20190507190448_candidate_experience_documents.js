
exports.up = function(knex, Promise) {
    return knex.schema.createTable("candidate_experience_documents",function(table){
        table.increments();
        table.integer('Experience_Id').notNullable();
        table.string('File_Hash',300).notNullable();
        table.string('File_Name',50).notNullable();
  })
};

exports.down = function(knex, Promise) {
    return knex.schema.dropTable('candidate_experience_documents');
};
