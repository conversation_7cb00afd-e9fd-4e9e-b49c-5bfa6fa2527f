const resolvers = {
    Query : {
        listCourseDetails : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig("course_details")
                        .orderBy('Course_Name', 'asc')
                        .then((courseDetails)=>{
                            if(!courseDetails[0]){
                                return {
                                    courseDetails: null
                                }
                            } else{
                                return {
                                    courseDetails: courseDetails
                                }
                            }
                        })
                    }
                })
                .then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from listCourseDetails')
                return result;
            }).catch(function(err){
                console.log('Error in listCourseDetails',err);
                if (err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;