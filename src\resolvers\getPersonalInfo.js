const resolvers = {
    Query: {
        getPersonalInfo: async (root, args) => {
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');
            var dateFormat = require('dateformat');

            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_url')
                    .where('Url_Hash', args.Url_Hash)
                    .then((url) => {
                        if (!url[0]) {
                            throw new Error('REO0101')
                        } else {
                            return knexconfig.select('candidate_personal_info.*', 'candidate_personal_info.Hobbies as hobbies',
                                'candidate_drivinglicense.*',
                                'candidate_passport.Candidate_Id', 'candidate_passport.Passport_No', 'candidate_passport.Issue_Date', 'candidate_passport.Issuing_Authority as Passport_Issuing_Authority', 'candidate_passport.Issuing_Country as Passport_Issuing_Country', 'candidate_passport.Expiry_Date', 'candidate_passport.Visa',
                                'candidate_contact_details.*',
                                'candidate_drivinglicense.File_Name as License_File_Name',
                                'candidate_passport.File_Name as Passport_File_Name',
                                'gender_expression.Gender_Expression_Id',
                                'gender_expression.Gender_Expression',
                                'gender_identity.Gender_Identity_Id',
                                'gender_identity.Gender_Identity',
                                'pBarangay.Barangay_Name as pBarangay_Id_Name',
                                'cBarangay.Barangay_Name as cBarangay_Id_Name',
                                'oBarangay.Barangay_Name as oBarangay_Id_Name',
                                'pCity.City_Name as pCity_Id_Name',
                                'cCity.City_Name as cCity_Id_Name',
                                'oCity.City_Name as oCity_Id_Name'
                            )
                                .from('candidate_personal_info')
                                .leftJoin('candidate_drivinglicense', 'candidate_personal_info.Candidate_Id', 'candidate_drivinglicense.Candidate_Id')
                                .leftJoin('candidate_passport', 'candidate_personal_info.Candidate_Id', 'candidate_passport.Candidate_Id')
                                .leftJoin('candidate_contact_details', 'candidate_personal_info.Candidate_Id', 'candidate_contact_details.Candidate_Id')
                                .leftJoin('gender_expression', 'gender_expression.Gender_Expression_Id', 'candidate_personal_info.Gender_Expression_Id')
                                .leftJoin('gender_identity', 'gender_identity.Gender_Identity_Id', 'candidate_personal_info.Gender_Identity_Id')
                                .leftJoin('barangay as pBarangay', 'pBarangay.Barangay_Id', 'candidate_contact_details.pBarangay_Id')
                                .leftJoin('barangay as cBarangay', 'cBarangay.Barangay_Id', 'candidate_contact_details.cBarangay_Id')
                                .leftJoin('barangay as oBarangay', 'oBarangay.Barangay_Id', 'candidate_contact_details.oBarangay_Id')
                                .leftJoin('city as pCity', 'pCity.City_Id', 'candidate_contact_details.pCity_Id')
                                .leftJoin('city as cCity', 'cCity.City_Id', 'candidate_contact_details.cCity_Id')
                                .leftJoin('city as oCity', 'oCity.City_Id', 'candidate_contact_details.oCity_Id')
                                .where('candidate_personal_info.Candidate_Id', url[0].Candidate_Id)
                                .then((personalInfo) => {
                                    return knexconfig.select('candidate_language.*', 'languages.*')
                                        .from('candidate_language')
                                        .leftJoin('languages', 'candidate_language.Lang_Known', 'languages.Lang_Id')
                                        .where('candidate_language.Candidate_Id', url[0].Candidate_Id)
                                        .then((lang) => {
                                            personalInfo[0].Lang_Known = lang;
                                            personalInfo[0].DOB = (!personalInfo[0].DOB || isNaN(Date.parse(personalInfo[0].DOB))) ? null : dateFormat(new Date(personalInfo[0].DOB), "yyyy-mm-dd");
                                            personalInfo[0].Smokerasof = (!personalInfo[0].Smokerasof || isNaN(Date.parse(personalInfo[0].Smokerasof))) ? null : dateFormat(new Date(personalInfo[0].Smokerasof), "yyyy-mm-dd");
                                            personalInfo[0].License_Issue_Date = (!personalInfo[0].License_Issue_Date || isNaN(Date.parse(personalInfo[0].License_Issue_Date))) ? null : dateFormat(new Date(personalInfo[0].License_Issue_Date), "yyyy-mm-dd");
                                            personalInfo[0].License_Expiry_Date = (!personalInfo[0].License_Expiry_Date || isNaN(Date.parse(personalInfo[0].License_Expiry_Date))) ? null : dateFormat(new Date(personalInfo[0].License_Expiry_Date), "yyyy-mm-dd");
                                            personalInfo[0].Issue_Date = (!personalInfo[0].Issue_Date || isNaN(Date.parse(personalInfo[0].Issue_Date))) ? null : dateFormat(new Date(personalInfo[0].Issue_Date), "yyyy-mm-dd");
                                            personalInfo[0].Expiry_Date = (!personalInfo[0].Expiry_Date || isNaN(Date.parse(personalInfo[0].Expiry_Date))) ? null : dateFormat(new Date(personalInfo[0].Expiry_Date), "yyyy-mm-dd");
                                            return personalInfo[0];
                                        })
                                })
                        }
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                console.log('return successful response from getPersonalInfo');
                return result;
            }).catch(function (err) {
                console.log('Error in getPersonalInfo', err);
                if (err.message == "REO0101") {
                    console.log('URL not found')
                    throw new ApolloError("URL not found", "REO0101")
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;