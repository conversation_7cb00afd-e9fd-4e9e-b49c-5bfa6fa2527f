const resolvers = {
    Mutation: {
        updateEmployeePreviousExperience: async(root,args) => {
            console.log('Inside updateEmployeePreviousExperience() function.');

            // require apollo server errors
            const { UserInputError, ApolloError } = require('apollo-server-lambda');

            let knexconfig = '';
            try{
                let { Org_Code, Url_Hash, Previous_Experience_Month, Previous_Experience_Year } = args;
                if(!Org_Code){
                    throw new Error('Please provide org code.');
                }else if(!Url_Hash){
                    throw new Error('Please provide URL hash.');
                }else if(Previous_Experience_Month < 0 || Previous_Experience_Month > 11){
                    throw new Error('Previous experience month should be between 0 to 11.');
                }else if(Previous_Experience_Year < 0 || Previous_Experience_Year > 99){
                    throw new Error('Previous experience year should be between 0 to 99.');
                }else{
                    let dbConnection = require('./dbConnection');
                    let connection = await dbConnection.getConnection(Org_Code);
                    knexconfig = require('knex')(connection);

                    return knexconfig.transaction(function(trc)
                    {
                        return knexconfig('candidate_url')
                        .select('Candidate_Id')
                        .where('Url_Hash',Url_Hash)
                        .then((candidateUrlDetails) => {
                            if(!candidateUrlDetails[0]){
                                throw new Error('REO0101');
                            } else {
                                if(candidateUrlDetails[0].Candidate_Id > 0){
                                    // Convert year to month by multiplying year * 12. Sum this month and the input month
                                    let previousExperienceTotalMonths = (Previous_Experience_Year*12) + Previous_Experience_Month;

                                    let candidateJobUpdateParams = {
                                        Previous_Employee_Experience: previousExperienceTotalMonths
                                    };

                                    //Update the previous experience total months in the table.
                                    return knexconfig('candidate_job')
                                    .update(candidateJobUpdateParams)
                                    .where('Candidate_Id',candidateUrlDetails[0].Candidate_Id)
                                    .transacting(trc)
                                    .then(()=>{
                                        return {message:'Employee previous experience updated successfully.'};
                                    })
                                }else{
                                    console.log('Candidate id does not exist for the URL hash.',candidateUrlDetails);
                                    throw new Error('Candidate id does not exist for the URL hash.');
                                }
                            }
                        })
                        .then(trc.commit)
                        .catch(trc.rollback);
                    })
                    .then(function(result){
                        console.log('Employee previous experience updated successfully.');
                        return result;
                    }).catch(function(previousExperienceUpdateCatchError){
                        console.log('Error while updating the employee previous experience details in the updateEmployeePreviousExperience() in .catch block. ',previousExperienceUpdateCatchError);
                        switch(previousExperienceUpdateCatchError.message){
                            case 'REO0101': throw new ApolloError('URL not found.','REO0101');
                            default: throw new ApolloError('Employee previous experience is not updated. Please try after some time.');
                        }
                    }).finally(() => {
                        knexconfig.destroy();
                    })
                }
            }catch(updateEmployeePreviousExperienceMainCatchError){
                console.log('Error in the updateEmployeePreviousExperience() function in main catch block. ',updateEmployeePreviousExperienceMainCatchError);
                knexconfig ? knexconfig.destroy() : null;

                switch(updateEmployeePreviousExperienceMainCatchError.message){
                    case 'Please provide org code.':
                    case 'Please provide URL hash.':
                        throw new ApolloError("It's us ! There seems to be some technical difficulties. Please try after some time.");
                    case 'Previous experience month should be between 0 to 11.':
                    case 'Previous experience year should be between 0 to 99.':
                        throw new UserInputError(updateEmployeePreviousExperienceMainCatchError.message);
                    default: throw new ApolloError("Something went wrong while processing the request to update the employee's previous experience.");
                }
            }
        }
    }
};
exports.resolvers = resolvers;