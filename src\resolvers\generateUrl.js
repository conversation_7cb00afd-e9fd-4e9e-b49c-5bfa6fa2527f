// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex')

const { getLocationDetails, encodeUsingBase64 } = require('../common/commonFunctions')
const resolvers = {
    Mutation: {
        generateUrl: async (root, args) => {
            const SHA3 = require('sha3');
            var dateFormat = require('dateformat');
            var randomize = require('randomatic');
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            var cName = await dbConnection.getCompanyName(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var moment = require('moment-timezone');

            let locationDetails = await getLocationDetails(knexconfig, args.Location_Id);
            let countryCode;
            if (locationDetails) {
                if (locationDetails.length > 0 && locationDetails[0]['Country_Code']) {
                    countryCode = locationDetails[0]['Country_Code'];
                }
            }
            else {
                throw new Error('Error occurred while getting locationDetails');
            }
            let amconnection = await commonLib.stepFunctions.getConnection(process.env.stageName, process.env.dbPrefix, process.env.secretName, process.env.region, args.Org_Code, 1, {});
            if (Object.keys(amconnection).length === 0) {
                throw new Error('Error occurred while getting apmangagerdb connection');
            }
            let appManagerDbConnection = knex(amconnection.AppManagerDb)
            let regionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection, args.Org_Code);
            appManagerDbConnection ? appManagerDbConnection.destroy() : null;
            if (Object.keys(regionDetails).length == 0) {
                throw new Error('Region details not found.');
            }
            var Candidate_Id;
            var companyName = encodeURIComponent(cName);
            var hash = new SHA3.SHA3Hash().update(' ' + Date.now()).digest('hex');
            let baseUrl = process.env.baseAUUrlUI;

            
            // if (countryCode && countryCode.toLowerCase() === 'au') {
            //     baseUrl = process.env.baseAUUrlUI;
            // }
            // else {
            //     baseUrl = process.env.baseUrlUI;
            // }
            countryCode = countryCode.toLowerCase()
            var full_url = baseUrl + companyName + '&country=' + countryCode + '&code=' + args.Org_Code + '&identifier=' + hash + '&d_code=' + regionDetails.Data_Region + '&b_code=' + regionDetails.Bucket_Region;
            let index = full_url.indexOf('?');
            let encodeParam = (full_url.substr(index + 1));
            let leftUrl = full_url.substr(0, index);
            full_url = leftUrl + '?' + await encodeUsingBase64(encodeParam);
            var pin = randomize('?', 6, { chars: '*********' });
            var expire_time;
            let duration_type;
            
            if (args.Expire_Type == 1) {
                duration_type = "Minutes"
                expire_time = moment.utc().add(args.Expire_Value, 'minutes').format('YYYY-MM-DD HH:mm:ss');
            } else if (args.Expire_Type == 2) {
                duration_type = "Hours"
                expire_time = moment.utc().add(args.Expire_Value, 'hours').format('YYYY-MM-DD HH:mm:ss');
            } else {
                duration_type = "Days"
                expire_time = moment.utc().add(args.Expire_Value, 'days').format('YYYY-MM-DD HH:mm:ss');
            }
            
            return knexconfig.transaction(function (trc) {
                return knexconfig('candidate_personal_info')
                    .insert({
                        Emp_First_Name: null,
                        Source_Type: "EmployeeOnboard"
                    })
                    .transacting(trc)
                    .then((personalId) => {
                        Candidate_Id = personalId[0];
                        var createData = {
                            Candidate_Id: Candidate_Id
                        }
                        var urlData = {
                            Url_Hash: hash,
                            Url: full_url,
                            Pin: pin,
                            Expire_Time: expire_time,
                            Candidate_Id: Candidate_Id,
                            Designation_Id: args.Designation_Id,
                            Department_Id: args.Department_Id,
                            Location_Id: args.Location_Id,
                            Job_Code: args.Job_Code,
                            Date_Of_Join: args.Date_Of_Join,
                            Probation_Date: args.Probation_Date,
                            EmpType_Id: args.EmpType_Id,
                            Manager_Id: args.Manager_Id,
                            Work_Schedule: args.Work_Schedule,
                            Service_Provider_Id: (args.Service_Provider_Id && args.Service_Provider_Id > 0) ? args.Service_Provider_Id : null,
                            Business_Unit_Id: (args.Business_Unit_Id && args.Business_Unit_Id > 0) ? args.Business_Unit_Id : null,
                            URL_Expiry_Duration_Measure: duration_type,
                            URL_Expiry_Duration: args.Expire_Value,
                            Created_At: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                            Created_By: args.Employee_Id
                        }
                        return knexconfig('candidate_url')
                            .insert(urlData)
                            .transacting(trc)
                            .then((url) => {
                                return knexconfig('candidate_contact_details')
                                    .insert(createData)
                                    .transacting(trc)
                                    .then(() => {
                                       
                                                return knexconfig('candidate_job')
                                                    .insert({
                                                        Candidate_Id: Candidate_Id,
                                                        Designation_Id: args.Designation_Id,
                                                        Department_Id: args.Department_Id,
                                                        Location_Id: args.Location_Id,
                                                        Date_Of_Join: args.Date_Of_Join,
                                                        Job_Code: args.Job_Code,
                                                        Probation_Date: args.Probation_Date,
                                                        EmpType_Id: args.EmpType_Id,
                                                        Manager_Id: args.Manager_Id,
                                                        Work_Schedule: args.Work_Schedule,
                                                        Service_Provider_Id: (args.Service_Provider_Id && args.Service_Provider_Id > 0) ? args.Service_Provider_Id : null,
                                                        Business_Unit_Id: (args.Business_Unit_Id && args.Business_Unit_Id > 0) ? args.Business_Unit_Id : null
                                                    })
                                                    .transacting(trc)
                                                    .then(() => {
                                                        return {
                                                            message: "URL Generated"
                                                        }
                                                    })
                                            
                                    })
                            })
                    }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function (result) {
                return knexconfig('candidate_url')
                    .where('Candidate_Id', Candidate_Id)
                    .then(async (data) => {
                        return data[0];
                    })
            }).catch(function (err) {
                console.log('Error in generateUrl', err);
                throw new Error('Something went wrong');
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;