//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
let moment = require('moment-timezone');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { formName, systemLogs, formIds } = require('../../common/appConstants');
const { getHRGroupEmployeeEmails, sendEmailToCandidateManagerInitiatorHrGroup, getHiringTeamEmployeeEmails, validateCandidateInputs, sendEmail, sendCustomEmail } = require('../../common/commonFunctions');
//Require validation
const { validateCandidateDetails } = require('../../common/commonFunctions')

//function to update job details
module.exports.updateCandidateStatus = async (parent, args, context, info) => {
    console.log('Inside updateCandidateStatus function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, args.formId);
        let actualStatus;
        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Add === 1 || checkRights.Role_Update === 1)) {
            //Validate Status
            if (args.status && args.status.length) {
                if (!(['Rejected', 'Verified', 'Returned', 'Withdrawn', 'Unverified'].includes(args.status))) {
                    validationError['IVE0438'] = commonLib.func.getError('', 'IVE0438').message;
                }
                else {
                    actualStatus = args.status.toLowerCase() === "returned" ? 'Draft' : args.status;
                }
                let statusValue = await statusIsProhibited(organizationDbConnection, args);
                if (args.status === 'Returned' && statusValue) {
                    validationError['IVE0441'] = commonLib.func.getError('', 'IVE0441').message;
                }
                if (args.status === 'Withdrawn' && await statusWithdrawProhibited(organizationDbConnection, args)) {
                    validationError['IVE0507'] = commonLib.func.getError('', 'IVE0507').message;
                }
            } else {
                validationError['IVE0439'] = commonLib.func.getError('', 'IVE0439').message;
            }
            if (args.status !== 'Returned' && args.status !== 'Rejected' && args.status !== 'Withdrawn' && args.status !== 'Unverified') {
                validationError = await validateCandidateDetails(organizationDbConnection, args.candidateId, validationError)
            }
            if (args.status === 'Verified') {
                const onboardingSettingsResult = await organizationDbConnection(ehrTables.onboardingSettings)
                    .select('Status').where('Integration_Type', 'Sunfish').first();
                if (onboardingSettingsResult && onboardingSettingsResult.Status.toLowerCase() === 'active') {
                    validationError = await commonLib.func.validateSunfishManatoryField(organizationDbConnection, args.candidateId, validationError)
                }
            }

            if (Object.keys(validationError).length == 0) {
                return (
                    organizationDbConnection
                        .transaction(async function (trx) {

                            if (args.status.toLowerCase() === "returned" || args.status.toLowerCase() === "rejected" || 
                            args.status.toLowerCase() === "withdrawn") {
                                
                                // Update candidate status in candidateUrl table
                                let data = await organizationDbConnection(ehrTables.candidateUrl).transacting(trx)
                                .update("Status", actualStatus).where("Candidate_Id", args.candidateId);

                                // Update candidate status in candidate_recruitment_info table
                                let candidateInRecruitment = await organizationDbConnection("candidate_recruitment_info").select('*')
                                .where("Candidate_Id", args.candidateId);

                                let statusId = await getCandidateStatusIdToBeUpdated(organizationDbConnection, args);
                                if (!statusId) throw 'IO0140'

                                if (candidateInRecruitment && candidateInRecruitment.length) {
                                    let updatedResult = await organizationDbConnection("candidate_recruitment_info").transacting(trx)
                                        .update({
                                            Candidate_Status: statusId,
                                        }).where("Candidate_Id", args.candidateId)
                                    if (!updatedResult) {
                                        throw "IO0137";
                                    }
                                }

                                await organizationDbConnection(ehrTables.candidatePersonalInfo).transacting(trx)
                                    .update('Candidate_Status', args.status).where('Candidate_Id', args.candidateId);
                                  
                                if (args.status.toLowerCase() === "returned") {
                                    // Retrieve candidate URL details
                                    let newData = await getCandidateUrlDetails(
                                        organizationDbConnection,
                                        args.candidateId,
                                        trx
                                    );

                                    // Calculate expire time based on URL expiry duration
                                    let expire_time;
                                    if (newData[0].URL_Expiry_Duration_Measure == "Minutes") {
                                        expire_time = moment.utc().add(newData[0].URL_Expiry_Duration, "minutes").format("YYYY-MM-DD HH:mm:ss");
                                    } else if (newData[0].URL_Expiry_Duration_Measure == "Hours") {
                                        expire_time = moment.utc().add(newData[0].URL_Expiry_Duration, "hours").format("YYYY-MM-DD HH:mm:ss");
                                    } else {
                                        expire_time = moment.utc().add(newData[0].URL_Expiry_Duration, "days").format("YYYY-MM-DD HH:mm:ss");
                                    }

                                    // Update URL expiration details in candidate_url table
                                    let updatedCandidateUrlResult = await organizationDbConnection("candidate_url").transacting(trx)
                                        .update({
                                            Expire_Time: expire_time,
                                            Updated_At: moment.utc().format("YYYY-MM-DD HH:mm:ss"),
                                            Updated_By: loginEmployeeId,
                                            Status: args.status
                                        })
                                        .where("Candidate_Id", args.candidateId);

                                    if (!updatedCandidateUrlResult) {
                                        throw "IO0138";
                                    }
                                }

                                if (args.templateId) {
                                    let eventVal = {
                                        Source: process.env.emailFrom,
                                        ReplyToAddresses: [process.env.emailReplyTo],
                                        bucketName: process.env.documentsBucket,
                                        region: process.env.region
                                    }
                                    const { emailResult } =
                                        await commonLib.func.listEmailTemplatePlaceHolderValues(
                                            { templateId: args.templateId, candidateId: args.candidateId },
                                            organizationDbConnection,
                                            context,
                                            eventVal,
                                            []
                                        );
                                    if (emailResult) {
                                        await sendSmsBasedOnTemplate(organizationDbConnection, emailResult, args.templateId, args.candidateId, context)
                                    }
                                }

                                // Return the initial update result
                                return data;

                            }
                            else {
                                let OldStatus = await organizationDbConnection(ehrTables.candidatePersonalInfo)
                                .select('Candidate_Status').where('Candidate_Id', args.candidateId);
                                
                                return (
                                    organizationDbConnection(ehrTables.candidatePersonalInfo)
                                        .update('Candidate_Status', args.status)
                                        .where('Candidate_Id', args.candidateId)
                                        .transacting(trx)
                                        .then(async (data) => {
                                            //Send email to the candidate and managers
                                            if (!OldStatus[0] || OldStatus[0].Candidate_Status.toLowerCase() !== "withdrawn") {
                                                let categoryId;
                                                if (args.status.toLowerCase() === "withdrawn") {
                                                    categoryId = 11
                                                }
                                                else if (args.status.toLowerCase() === "returned") {
                                                    categoryId = 12
                                                }
                                                else {
                                                    categoryId = 15
                                                }
                                                const defaultTemplate = await commonLib.func.getDefaultTemplate(
                                                    {
                                                        categoryId: categoryId,
                                                        formId: 178
                                                    },
                                                    organizationDbConnection
                                                )
                                                if (defaultTemplate && Object.keys(defaultTemplate).length) {
                                                    const templateId = defaultTemplate.Template_Id;
                                                    let eventVal = {
                                                        Source: process.env.emailFrom,
                                                        ReplyToAddresses: [process.env.emailReplyTo],
                                                        bucketName: process.env.documentsBucket,
                                                        region: process.env.region
                                                    }
                                                    const { emailResult, ses, event } =
                                                        await commonLib.func.listEmailTemplatePlaceHolderValues(
                                                            { templateId, candidateId: args.candidateId },
                                                            organizationDbConnection,
                                                            context,
                                                            eventVal,
                                                            []
                                                        );
                                                    if (emailResult && templateId) {
                                                        await sendSmsBasedOnTemplate(organizationDbConnection, emailResult, templateId, args.candidateId, context)
                                                    }

                                                    let inviteStatus = await sendCustomEmail(emailResult, ses, event, null, organizationDbConnection);
                                                    if (inviteStatus && inviteStatus.toLowerCase() === "invited") {
                                                        return data;
                                                    } else {
                                                        throw 'IO0134'
                                                    }
                                                }

                                                let [orgDetails, candidateDetails, hrGroupEmail, initiatorEmail, hiringTeamEmail] = await Promise.all([
                                                    commonLib.func.getOrgDetails(context.Org_Code, organizationDbConnection, 1),
                                                    organizationDbConnection(ehrTables.candidateJob + " as CJ")
                                                        .select(organizationDbConnection.raw("CONCAT_WS(' ', CP.Emp_First_Name, CP.Emp_Middle_Name, CP.Emp_Last_Name) as candidateName"), 'EJ.Emp_Email as managerEmail', 'CP.Personal_Email as candidateEmail')
                                                        .leftJoin(ehrTables.candidatePersonalInfo + " as CP", "CJ.Candidate_Id", "CP.Candidate_Id")
                                                        .leftJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "CJ.Manager_Id")
                                                        .where('CP.Candidate_Id', args.candidateId),
                                                    getHRGroupEmployeeEmails(organizationDbConnection),
                                                    organizationDbConnection(ehrTables.empJob).select('Emp_Email').where('Employee_Id', loginEmployeeId),
                                                    getHiringTeamEmployeeEmails(organizationDbConnection, args.candidateId)
                                                ])
                                                let emailData = {
                                                    'status': args.status,
                                                    'candidateName': candidateDetails[0].candidateName,
                                                    'candidateEmail': candidateDetails[0].candidateEmail,
                                                    'orgLogo': orgDetails.logoPath ? orgDetails.logoPath : '',
                                                    'orgCode': context.Org_Code
                                                }
                                                let emails = {
                                                    hrGroupEmail: hrGroupEmail,
                                                    managerEmail: candidateDetails[0].managerEmail ? candidateDetails[0].managerEmail : '',
                                                    initiatorEmail: initiatorEmail[0].Emp_Email ? initiatorEmail[0].Emp_Email : '',
                                                    hiringTeamEmail: hiringTeamEmail
                                                }

                                                await sendEmailToCandidateManagerInitiatorHrGroup(emails, 'statusUpdate', emailData)
                                            }
                                            return data
                                        })
                                        .catch((catchError) => {
                                            console.error('Error in updateCandidateStatus .catch() block', catchError);
                                            let errResult = commonLib.func.getError(catchError, 'IO0134');
                                            //Destroy DB connection
                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                            //Return error response
                                            throw new ApolloError(errResult.message, errResult.code);
                                        })
                                )
                            }

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: args.formId,
                                    organizationDbConnection: organizationDbConnection,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    uniqueId: args.candidateId,
                                    message:  `The Status has been changed to ${args.status}.` 
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);
                                organizationDbConnection ? organizationDbConnection.destroy() : null;

                                return { errorCode: "", message: "Candidate job details was updated successfully." };
                            } else {
                                throw 'IO0134'
                            }
                        })
                        .catch((catchError) => {
                            console.error('Error in updateCandidateStatus .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0134');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add/update the candidate status');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.error('Error in updateCandidateStatus function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.error('Validation error in updateCandidateStatus function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0032');
            // return response
            throw new ApolloError(errResult.message, errResult.code,);
        }
    }
    async function statusIsProhibited(organizationDbConnection, args) {
        let info = await organizationDbConnection(ehrTables.candidatePersonalInfo)
            .select('Candidate_Status')
            .where('Candidate_Id', args.candidateId)
            .whereIn('Candidate_Status', ['Migrated', 'Rejected', 'Verified']);

        if (info && info.length) {
            return true;
        }
        else {
            return false;
        }
    }
}
async function statusWithdrawProhibited(organizationDbConnection, args) {
    let info = await organizationDbConnection(ehrTables.candidatePersonalInfo)
        .select('Candidate_Status')
        .where('Candidate_Id', args.candidateId)
        .where('Candidate_Status', 'Migrated');

    if (info && info.length) {
        return true;
    }
    else {
        return false;
    }
}
async function getCandidateUrlDetails(organizationDbConnection, candidateId, trx) {
    try {
        let val = await organizationDbConnection('candidate_url').select('*').where('Candidate_Id', candidateId).transacting(trx)
        if (!val || !val.length || !val[0]) {
            throw 'IO0139'
        }
        return val
    } catch (err) {
        throw err;
    }
}

async function getCandidateStatusIdToBeUpdated(organizationDbConnection, args) {
    let newStatusValue = (args.status.toLowerCase() === "returned")
        ? 'Onboarding Inprogress'
        : (args.status.toLowerCase() === "rejected")
            ? 'Onboarding Rejected'
            : 'Onboarding Withdrawn';

    try {
        let info = await organizationDbConnection(ehrTables.atsStatusTable)
            .select('Id')
            .where('Status', newStatusValue);

        if (info.length > 0) {
            return info[0].Id;
        } else {
            return null;
        }
    } catch (err) {
        console.error("Error fetching candidate status ID:", err);
        throw err;
    }
}

async function sendSmsBasedOnTemplate(organizationDbConnection, emailResult, templateId, candidateId, context) {
    try {
        const enableSms = await organizationDbConnection(ehrTables.onboardingSettings)
            .select('Enable_SMS').first();

        if (enableSms && enableSms.Enable_SMS) {
            let mobileNumbers = await commonLib.func.getMobileNumbersForTemplate(organizationDbConnection, templateId, candidateId, context)
            if (mobileNumbers && mobileNumbers.length > 0) {
                //Convert the html to text
                let text = emailResult.Template_Content.replace(/<[^>]*>/g, '').trim();
                mobileNumbers = mobileNumbers.map((number) => {
                    return {
                        mobileNumber: number,
                        message: text
                    }
                })
                await commonLib.func.sendBulkSMS(mobileNumbers)
            }
        }
        return true
    }
    catch (err) {
        console.log('Error in sendSmsBasedOnTemplate', err);
        throw err;
    }
}