const resolvers = {
    Query : {
        getEducation : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig.transaction(function(trc){
                return knexconfig('candidate_url')
                .where('Url_Hash',args.Url_Hash)
                .then((url)=>{
                    if(!url[0]){
                        throw new Error('REO0101')
                    } else {
                        return knexconfig.select('candidate_education.*',
                            'candidate_education_documents.File_Name',
                            'candidate_education_documents.Document_Name',
                            'candidate_education_documents.Sub_Type_Id',
                            'document_sub_type.Document_Sub_Type',
                            'course_details.Course_Name as Education_Type_Name',
                            'ES.Specialization as Specialization_Name','ES.Specialization_Code','EI.Institution_Code','EI.Institution as Institution_Name'
                        )
                        .from('candidate_education')
                        .leftJoin('course_details','candidate_education.Education_Type','course_details.Course_Id')
                        .leftJoin('edu_specialization' + " as ES", "ES.Specialization_Id", "candidate_education.Specialization_Id")
                .leftJoin('edu_institution' + " as EI", "EI.Institution_Id", "candidate_education.Institution_Id")
                        .leftJoin('candidate_education_documents','candidate_education.Education_Id','candidate_education_documents.Education_Id')
                        .leftJoin('document_sub_type','candidate_education_documents.Sub_Type_Id','document_sub_type.Document_Sub_Type_Id')
                        .where('candidate_education.Candidate_Id',url[0].Candidate_Id)
                        .orderBy('candidate_education.Education_Id')
                        .then((education)=>{
                            if(!education[0]){
                                return {
                                    education: null
                                }
                            } else {
                                return {
                                    education: education
                                }
                            }
                        })
                    }
                }).then(trc.commit)
                    .catch(trc.rollback);
            }).then(function(result){
                console.log('return successful response from getEducation');
                return result;
            }).catch(function(err){
                console.log('Error in getEducation',err);
                if(err.message == "REO0101"){
                    console.log('URL not found')
                    throw new ApolloError("URL not found","REO0101" )
                } else {
                    throw new Error('Something went wrong')
                }
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;