//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');

//fuction to list candidate document
let organizationDbConnection;
module.exports.getTimezoneList = async (parent, args, context, info) => {
    console.log('Inside getTimezoneList function', args);
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
            organizationDbConnection.select('*')
            .from('timezone')
            .orderBy('TimeZone_Id', 'asc')
            .then((data) => {
                    if (data && data.length > 0) {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "timezone list has been fetched successfully.", timeZoneDetails: data };
                    }
                    else {
                        throw 'CCH0018';
                    }
                })
                .catch((catchError) => {
                    console.log('Error in getTimezoneList .catch() block', catchError);
                   throw catchError;
                })
        )
       
    }
    
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getTimezoneList function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0019');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
