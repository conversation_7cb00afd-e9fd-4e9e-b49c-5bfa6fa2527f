//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
let moment = require('moment-timezone');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');

//function to add/update vendor dyncamic forms
module.exports.addUpdateVendorDynamicForm = async (parent, args, context, info) => {
    console.log('Inside addUpdateVendorDynamicForm function');
    let organizationDbConnection;
    try {
        let loginEmployeeId = context.Employee_Id ? context.Employee_Id : null;
        let responseId = args.responseId
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights;
        if (args.checkAccess) {
            checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        }
        if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1))) {
            return (
                organizationDbConnection
                    .transaction(function (trx) {
                        if (responseId) {
                            if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1)) {
                                return (
                                    organizationDbConnection(ehrTables.dynamicFormResponse)
                                        .update({
                                            'Response': args.response,
                                            'Status': args.status,
                                            'Task_Id': `Vendor - ${args.vendorId}`,
                                            "Updated_On": moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                            "Updated_By": loginEmployeeId ? loginEmployeeId : 0
                                        })
                                        .where('Response_Id', responseId)
                                        .andWhere('Vendor_Id', args.vendorId)
                                        .transacting(trx)
                                        .then((updateVendorDynamicForm) => {
                                            if (updateVendorDynamicForm) {
                                                return 'success';
                                            } else {
                                                console.log('Error while updating the vendor dynamic form', updateVendorDynamicForm)
                                                throw 'VO0130';
                                            }
                                        })
                                )
                            } else {
                                console.log('No rights to update vendor dynamic form')
                                throw '_DB0102'
                            }
                        } else {
                            if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1)) {
                                return (
                                    organizationDbConnection(ehrTables.dynamicFormResponse)
                                        .insert({
                                            'Response': args.response,
                                            'Status': args.status,
                                            'Task_Id': `Vendor - ${args.vendorId}`,
                                            'Vendor_Id': args.vendorId,
                                            "Added_On": moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                            "Added_By": loginEmployeeId ? loginEmployeeId : 0
                                        })
                                        .transacting(trx)
                                        .then((insertDynamicData) => {
                                            if (insertDynamicData) {
                                                return 'success'
                                            } else {
                                                console.log('Error while inserting the vendor dynamic form', insertDynamicData)
                                                throw 'VO0130';
                                            }
                                        })
                                )
                            } else {
                                console.log('No rights to add vendor dynamic form')
                                throw '_DB0101'
                            }
                        }
                    })
                    .then((response) => {
                        if (response) {
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Vendor dynamic form has been submitted successfully" };
                        } else {
                            throw 'VO0114'
                        }
                    })
                    .catch((catchError) => {
                        console.log('Error in addUpdateVendorDynamicForm .catch() block', catchError);
                        let errResult = commonLib.func.getError(catchError, 'VO0137');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return error response
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )

        } else {
            console.log('No rights to add / update the vendor dynamic form');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateVendorDynamicForm function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'VO0026');
        // return response
        throw new ApolloError(errResult.message, errResult.code);

    }
}

