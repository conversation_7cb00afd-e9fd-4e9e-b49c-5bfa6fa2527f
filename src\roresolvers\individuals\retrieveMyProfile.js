// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appConstants');

module.exports.retrieveMyProfile = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveMyProfile function.");
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
            organizationDbConnection(ehrTables.candidateJob + " as EJ")
                .select(organizationDbConnection.raw('CONCAT_WS(" ",EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as employeeName'), "EJ.Emp_Email as empEmail", "EJ.User_Defined_EmpId as userDefinedEmpId", "EJ.Candidate_Id as candidateId", "EJ.Designation_Id as designationId",
                    organizationDbConnection.raw("CASE WHEN DES.Designation_Code IS NOT NULL THEN CONCAT(DES.Designation_Code,' - ',DES.Designation_Name) ELSE DES.Designation_Name END AS designationName"),
                    "EJ.Department_Id as departmentId",
                    organizationDbConnection.raw(`
                        CASE 
                            WHEN DEP.Department_Code IS NOT NULL 
                            THEN CONCAT(DEP.Department_Code, ' - ', DEP.Department_Name) 
                            ELSE DEP.Department_Name 
                        END AS departmentName
                    `),
                    "EJ.Emp_Status as empStatus", "CD.Mobile_No as mobileNo", "CD.Mobile_No_Country_Code as mobileNoCountryCode",
                    "CD.oStreet_Name as street", "CD.oApartment_Name as apartmentName", "CD.oCity as city", "CD.oState as state",
                    "C.Country_Name as country", "CD.oPinCode as pinCode", "CD.Use_Location_Address as useLocationAddress", "EP.Photo_Path as photoPath", "LO.Street1 as locationStreet1", "LO.Street2 as locationStreet2", "CI.City_Name as locationCity", "ST.State_Name as locationState", "LO.Pincode as locationPinCode", "CO.Country_Name as locationCountry")
                .leftJoin(ehrTables.candidatePersonalInfo + " as EP", "EP.Candidate_Id", "EJ.Candidate_Id")
                .leftJoin(ehrTables.designation + " as DES", "DES.Designation_Id", "EJ.Designation_Id")
                .leftJoin(ehrTables.department + " as DEP", "DEP.Department_Id", "EJ.Department_Id")
                .leftJoin(ehrTables.candidateContact + " as CD", "CD.Candidate_Id", "EJ.Candidate_Id")
                .leftJoin(ehrTables.country + " as C", "C.Country_Code", "CD.oCountry")
                .leftJoin(ehrTables.location + " as LO", "LO.Location_Id", "EJ.Location_Id")
                .leftJoin(ehrTables.city + " as CI", "CI.City_Id", "LO.City_Id")
                .leftJoin(ehrTables.state + " as ST", "ST.State_Id", "LO.State_Id")
                .leftJoin(ehrTables.country + " as CO", "CO.Country_Code", "LO.Country_Code")
                .where('EJ.Candidate_Id', args.candidateId)
                .then((profileDetails) => {
                    if (profileDetails) {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Candidate profile details retrieved successfully", employeeProfile: profileDetails[0] }
                    } else {
                        throw 'IO0101'
                    }
                })
                .catch((e) => {
                    // Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    console.log('Error in retrieveMyProfile function main catch block.', e);
                    const errResult = commonLib.func.getError(e, 'IO0101');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveMyProfile function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'IO0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
};
