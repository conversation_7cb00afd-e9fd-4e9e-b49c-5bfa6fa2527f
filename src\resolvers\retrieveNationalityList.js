const resolvers = {
    Query : {
        retrieveNationalityList : async (root,args) =>{
            var dbConnection = require('./dbConnection');
            var connection = await dbConnection.getConnection(args.Org_Code);
            const knexconfig = require('knex')(connection);
            var { ApolloError } = require('apollo-server-lambda');

            return knexconfig("nationality")
            .select('Nationality_Id as nationalityId', 'Nationality_Code as nationalityCode', 'Nationality as nationality')
            .orderBy('Nationality', 'asc')
            .then((nationality)=>{
                if(!nationality[0]){
                    return {
                        nationalityData: null
                    }
                } else{
                    return {
                        nationalityData: nationality
                    }
                }
            })
            .catch(function(err){
                console.log('Error in retrieveNationalityList',err);
                throw new Error('Something went wrong')
            }).finally(() => {
                knexconfig.destroy();
            })
        }
    },
}
exports.resolvers = resolvers;