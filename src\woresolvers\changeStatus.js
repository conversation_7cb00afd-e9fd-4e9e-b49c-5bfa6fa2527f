//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName, formIds } = require('../common/appConstants');
let moment = require('moment-timezone');

//function to change the status and complete the form
module.exports.changeStatus = async (parent, args, context, info) => {
    console.log('Inside changeStatus function');
    let organizationDbConnection;
    let loginEmployeeId = context.Employee_Id;

    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let orgCode = context.Org_Code;
        let loginEmployeeCurrentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        //Check if vendor exists
        let onboardingVendorId = (args.vendorId) ? args.vendorId : 0;
        // if it is update we will send the vendorId
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            if (onboardingVendorId > 0) {
                let status = args.status;
                let updateData = {
                    Status: status,
                    Updated_On: loginEmployeeCurrentDateTime,
                    Updated_By: loginEmployeeId
                }
                if (status == 'Verified') {
                    updateData.Vendor_Status = 'Active'
                }
                return (
                    organizationDbConnection(ehrTables.serviceProvider)
                        .update(updateData)
                        .where('Service_Provider_Id', onboardingVendorId)
                        .then(async (updateResult) => {
                            if (updateResult) {
                                updateResult = updateResult[0];
                                if (status == "Verified") {
                                    return (
                                        organizationDbConnection(ehrTables.candidateUrl)
                                            .update({
                                                Status: "Yet to be invited",
                                                Updated_By: loginEmployeeId,
                                                Updated_At: loginEmployeeCurrentDateTime
                                            })
                                            .where('Service_Provider_Id', onboardingVendorId)
                                            .andWhere('Status', 'Supplier to be verified')
                                            .then(async () => {
                                                let [vendorData, adminEmployeeIds, orgDetails] = await Promise.all([
                                                    organizationDbConnection(ehrTables.serviceProvider).select().where('Service_Provider_Id', onboardingVendorId),
                                                    commonLib.func.getAdminEmployeeIds(organizationDbConnection, 0, 1, '', [formIds.admin]),
                                                    commonLib.func.getOrgDetails(orgCode, organizationDbConnection, 1)
                                                ]);

                                                if(vendorData && !vendorData.length){
                                                    throw 'VO0103'
                                                }

                                                // Fetching admin employee emails after getting employee IDs
                                                let adminEmployeeEmails = await organizationDbConnection(ehrTables.empJob).pluck("Emp_Email").whereIn("Employee_Id", adminEmployeeIds).andWhereNot('Emp_Email', '').andWhereNot('Emp_Email', null);

                                                let defaulTemplateData = {
                                                    "emailSubject": `Vendor Verified | ${vendorData[0].Service_Provider_Name}`,
                                                    "orgLogo": orgDetails && orgDetails.logoPath ? orgDetails.logoPath: '',
                                                    "title1": '',
                                                    "title2": '',
                                                    "centerImage": '',
                                                    "subTitle": `I am pleased to inform you that after undergoing a comprehensive verification process, ${vendorData[0].Service_Provider_Name} has been successfully verified.`,
                                                    "redirectionUrl": '',
                                                    "buttonText": '',
                                                    "footer": true,
                                                    "supportEmail": orgDetails && orgDetails.hrAdminEmailAddress && orgDetails.hrAdminEmailAddress.length ? orgDetails.hrAdminEmailAddress : '',
                                                }
                                                //Send Notification to the Vendor
                                                let notificationParams = {
                                                    "Template": "CommonTemplate",
                                                    "Source": process.env.emailFrom,
                                                    "Destinations": [{
                                                        "Destination": {
                                                            "ToAddresses": [vendorData[0].Email_Id],
                                                            "CcAddresses": adminEmployeeEmails
                                                        }
                                                    }],
                                                    "DefaultTemplateData": JSON.stringify(defaulTemplateData)
                                                }
                                                await commonLib.func.sendBulkEmailNotifications(notificationParams, process.env.sesTemplatesRegion);
                                                //Destroy DB connection
                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                return { errorCode: "", message: "Vendor Status has been updated successfully." };
                                            })
                                    )
                                } else {
                                    return { errorCode: "", message: "Vendor Status has been updated successfully." };
                                }
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in changeFinishStatus .catch() block', catchError);
                            errResult = commonLib.func.getError(catchError, 'VO0119');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            }
            else {
                throw 'VO0103';
            }
        } else {
            console.log('No rights to update vendors');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in changeStatus function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'VO0012');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}
