//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');

//fuction to list vendor employees
let organizationDbConnection;
module.exports.getVendorBankDetails = async (parent, args, context, info) => {
    console.log('Inside getVendorBankDetails function', args);
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id ? context.Employee_Id : null;
        let checkRights;
        if (args.checkAccess) {
            checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        }
        if ((!args.checkAccess) || (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1 )) {
            return (
                organizationDbConnection(ehrTables.vendorBankDetails)
                    .select("*")
                    .where('Vendor_Id', args.vendorId)
                    .then((data) => {
                        data = data.length ? data[0]: null
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Vendor's bank details has been fetched successfully.", vendorBankDetails: data };
                    })
                    .catch((catchError) => {
                        console.log('Error in getVendorBankDetails .catch() block', catchError);
                        let errResult = commonLib.func.getError(catchError, 'VO0141');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        } else {
            console.log('No rights to view the vendor documents');
            throw '_DB0100';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getVendorBankDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'VO0027');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
