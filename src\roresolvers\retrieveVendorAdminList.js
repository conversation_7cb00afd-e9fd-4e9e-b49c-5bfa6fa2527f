//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');

//function to retrieve vendor admins
let organizationDbConnection;
module.exports.retrieveVendorAdmins = async (parent, args, context, info) => {
    console.log('Inside retrieveVendorAdmins function',);
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
        return (
            organizationDbConnection(ehrTables.vendorAdminList + " as VAL")
            .select("CP.Candidate_Id", organizationDbConnection.raw('CONCAT_WS(" ",CP.Emp_First_Name, CP.Emp_Middle_Name, CP.Emp_Last_Name) as Candidate_Name'),
            "CP.Personal_Email","CP.Candidate_Status", "CC.Mobile_No", "CC.Mobile_No_Country_Code", "CU.Status"
            )
            .leftJoin(ehrTables.candidatePersonalInfo + " as CP", "CP.Candidate_Id", "VAL.Candidate_Id")
            .leftJoin(ehrTables.candidateContact + " as CC", "CC.Candidate_Id", "VAL.Candidate_Id")
            .leftJoin(ehrTables.candidateUrl + " as CU", "CU.Candidate_Id", "VAL.Candidate_Id")
            .where('VAL.Vendor_Id', args.vendorId)
                .then((data) => {
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Vendor's admin data has been fetched successfully.", vendorAdmin: data };
                })
                .catch((catchError) => {
                    console.log('Error in retrieveVendorAdmins .catch() block', catchError);
                    errResult = commonLib.func.getError(catchError, 'VO0143');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
            }else{
                console.log('No rights to view vendor admin');
                throw '_DB0100';
            }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveVendorAdmins function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'VO0029');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
