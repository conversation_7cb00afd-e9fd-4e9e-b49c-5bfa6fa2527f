//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');


//function to change the status and complete the form
module.exports.changeFinishStatus = async (parent, args, context, info) => {
    console.log('Inside changeFinishStatus function');
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        //Check if vendor exists
        let onboardingVendorId = (args.vendorId) ? args.vendorId : 0;
        // if it is update we will send the vendorId
        if (onboardingVendorId > 0) {
            // return (
            return (organizationDbConnection
                .transaction(async (trx) => {
                    return(
                    organizationDbConnection(ehrTables.serviceProvider)
                        .select("Section1_Progress", "Section2_Progress", "Section3_Progress", "Section4_Progress")
                        .where('Service_Provider_Id', onboardingVendorId)
                        .transacting(trx)
                        .then(async (updateResult) => {
                            
                            if (updateResult) {
                                updateResult = updateResult[0];
                                
                                //updateResult.Section3_Progress == 1 && updateResult.Section4_Progress == 1 also need to be added
                                if (updateResult.Section1_Progress == 1) {
                                    return (
                                        organizationDbConnection(ehrTables.serviceProvider)
                                            .update({
                                                Status: "Unverified"
                                            })
                                            .where('Service_Provider_Id', onboardingVendorId)
                                            .transacting(trx)
                                            .then(async (updateResult) => {
                                                if (updateResult) {
                                                    return (
                                                        organizationDbConnection(ehrTables.invitedVendors)
                                                            .update({
                                                                Status: "Completed",
                                                            })
                                                            .where('Vendor_Id', onboardingVendorId)
                                                            .transacting(trx)
                                                            .then(async (updateResult) => {
                                                                if (updateResult) {
                                                                    //Destroy DB connection
                                                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                                    return { errorCode: "", message: "Vendor has been updated successfully." };
                                                                }

                                                            })
                                                    )
                                                }
                                                else {
                                                    throw 'VO0119';
                                                }
                                            })
                                    )
                                } else {
                                    throw 'VO0120';
                                }
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in changeFinishStatus .catch() block', catchError);
                            errResult = commonLib.func.getError(catchError, 'VO0119');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                    )
                })
            )
        }
        else {
            throw 'VO0103';
        }
    } catch (mainCatchError) {
        console.log('Error in changeFinishStatus function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'VO0011');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}