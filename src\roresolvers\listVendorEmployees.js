//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const { formName } = require('../common/appConstants');

//fuction to list vendor employees
let organizationDbConnection;
module.exports.listVendorEmployees = async (parent, args, context, info) => {
    console.log('Inside listVendorEmployees function', args);
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.vendorOnboarding, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.serviceProvider)
                    .select("EJ.Employee_Id as employeeId", "EJ.User_Defined_EmpId as userDefinedEmpId", "EJ.Emp_Email as email", "EJ.Emp_Status as status", "EJ.Date_Of_Join as dateOfJoin",
                        organizationDbConnection.raw("CASE WHEN DES.Designation_Code IS NOT NULL THEN CONCAT(DES.Designation_Code,' - ',DES.Designation_Name) ELSE DES.Designation_Name END AS designationName"),
                        organizationDbConnection.raw(`
                            CASE 
                                WHEN DEP.Department_Code IS NOT NULL 
                                THEN CONCAT(DEP.Department_Code, ' - ', DEP.Department_Name) 
                                ELSE DEP.Department_Name 
                            END AS departmentName
                        `),
                        organizationDbConnection.raw(`
                            CASE 
                                WHEN LOC.Location_Code IS NOT NULL 
                                THEN CONCAT(LOC.Location_Code, ' - ', LOC.Location_Name) 
                                ELSE LOC.Location_Name 
                            END AS locationName
                        `),
                        organizationDbConnection.raw(`
                            CASE 
                                WHEN ET.Employee_Type_Code IS NOT NULL 
                                THEN CONCAT(ET.Employee_Type_Code, ' - ', ET.Employee_Type) 
                                ELSE ET.Employee_Type 
                            END AS employeeType
                        `),
                        organizationDbConnection.raw("CONCAT(MAN.Emp_First_Name, ' ', MAN.Emp_Last_Name) as managerName"),
                        organizationDbConnection.raw("CONCAT(EPI.Emp_First_Name, ' ', EPI.Emp_Last_Name) as employeeName"))
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "EJ.Employee_Id")
                    .leftJoin(ehrTables.empPersonalInfo + " as MAN", "MAN.Employee_Id", "EJ.Manager_Id")
                    .leftJoin(ehrTables.designation + " as DES", "DES.Designation_Id", "EJ.Designation_Id")
                    .leftJoin(ehrTables.department + " as DEP", "DEP.Department_Id", "EJ.Department_Id")
                    .leftJoin(ehrTables.location + " as LOC", "LOC.Location_Id", "EJ.Location_Id")
                    .leftJoin(ehrTables.employeeType + " as ET", "ET.EmpType_Id", "EJ.EmpType_Id")
                    .from(ehrTables.empJob + " as EJ")
                    .where('EJ.Service_Provider_Id', args.vendorId)
                    .then((data) => {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Vendor's Employee data has been fetched successfully.", vendorEmployees: data };
                    })
                    .catch((catchError) => {
                        console.log('Error in listVendorEmployees .catch() block', catchError);
                        errResult = commonLib.func.getError(catchError, 'VO0127');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        } else {
            console.log('No rights to view Onboarded Vendors Employees');
            throw '_DB0100';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listVendorEmployees function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'VO0016');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
