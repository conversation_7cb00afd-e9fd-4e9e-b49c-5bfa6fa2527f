# defining custom data type
scalar Date

type Query {
  retrieveAccreditationAndType(
    urlHash: String!
  ): retrieveAccreditationAndTypeResponse!
  retrieveAccreditationDetails(
    urlHash: String!
  ): retrieveAccreditationDetailsResponse!
  retrieveSuperannuationDetails(
    urlHash: String!
  ): retrieveSuperannuationDetailsResponse!
  listInvitedVendors: listInvitedVendorsResponse
  listCandidateDocumentDetails(urlHash: String!):listCandidateDocumentDetailsResponse
  getCityListWithState:getCityListWithStateResponse
  getTimezoneList:getTimezoneListResponse
  listOnboardedVendors: listOnboardedVendorsResponse
  getVendorDetails(urlHash: String!): getVendorDetailsResponse
  checkUrlStatus(urlHash: String!): checkUrlStatusResponse
  authenticatePin(urlHash: String!, pin: Int!): commonResponse
  getVendorInsuranceDetails(vendorId: Int): getVendorInsuranceDetailsResponse
  listVendorEmployees(vendorId: Int!): listVendorEmployeesResponse
  checkVendorStatus(candidateId: Int!): commonResponse
  checkMemberStatus(vendorId: Int!): commonResponse
  retrieveDesignationAccreditationCategoryDetails(
    formId: Int
  ): designationMappingDetailsResponse
  retrieveProjectAccreditationCategoryDetails(
    formName: String
  ): projectMappingDetailsResponse
  triggerResendInviteVendor(vendorIds: [Int]!): commonResponse!
  triggerInviteCandidate(candidates:[Int]!
  formId: Int!): commonResponse
  listVendorDocuments(
    vendorId: Int!
    checkAccess: Int
  ): listVendorDocumentsResponse
  getDynamicFormDetails(
    vendorBased: Int
    templateId: Int
    vendorId: Int!
  ): getDynamicFormDetailsResponse
  getVendorBankDetails(vendorId: Int): getVendorBankDetailsResponse
  retrieveVendorAdmins(vendorId: Int!):retrieveVendorAdminsResponse
  listInvitedIndividuals(offset: Int, limit: Int): listInvitedIndividualsResponse
  retrievePersonalInfo(candidateId: Int!): retrievePersonalInfoResponse
  retrieveJobInfo(candidateId: Int!): retrieveJobInfoResponse
  retrieveContactInfo(candidateId: Int!): retrieveContactInfoResponse
  retrieveCareerInfo(candidateId: Int!): retrieveCareerInfoResponse
  retrieveDocumentInfo(candidateId: Int!): retrieveDocumentInfoResponse
  retrieveOtherInfo(candidateId: Int!): retrieveOtherInfoResponse
  retrieveMyProfile(candidateId: Int!): retrieveMyProfileResponse
  listCandidateDetails: listCandidateDetailsResponse
  getFormFeildsByFormIdAndTab(form_Id: Int!): getFormFeildsByFormIdAndTabResponse
  listCandidateInvitedIndividuals(offset: Int, limit: Int): listInvitedIndividualsResponse
  retrieveListEduInstitutionAndSpecialization : listEduInstitutionAndSpecializationResponse
  retrieveOnboardingSettings(integrationType: String): OnboardingSettingsResponse
  listTimekeepingCareerDetail : listTimekeepingCareerResponse
  listdocumentEnforcementGroup: listdocumentEnforcementGroupResponse
  getNewHireEmployeeDetails(candidateId:Int!):getNewHireEmployeeDetailsResponse
  listVendorDocumentDetails(vendorId: Int!):listVendorDocumentDetailsResponse
  listCandidateAccreditationDetails(urlHash: String!):listCandidateAccreditationDetailsResponse
  getBarangayListWithCity(searchString: String,cityId:Int):getBarangayListWithCityResponse
}

type listCandidateAccreditationDetailsResponse{
  errorCode: String
  message: String
  accreditationDetails: [accreditationCategoryType]
}

type accreditationCategoryType{
  Accreditation_Category_And_Type_Id: Int
  Accreditation_Category: String
  Accreditation_Type: String
  Mandatory: String
  Instruction: String
  File_Name: String
  Dependent_Id: Int
  Dependent_First_Name: String
  Dependent_Last_Name: String
  Gender: String
  Relationship: String
  Dependent_DOB: String
}

type retrieveMyProfileResponse{
  errorCode: String
  message: String
  employeeProfile: employeeProfile
}

type employeeProfile {
  employeeName: String
  empEmail: String
  userDefinedEmpId: String
  candidateId: Int
  designationId: Int
  designationName: String
  departmentId: Int
  departmentName: String
  empStatus: String
  mobileNo: String
  mobileNoCountryCode: String
  street: String
  apartmentName: String
  state: String
  city: String
  pinCode: String
  country: String
  photoPath: String
  useLocationAddress: Int
  invitationStatus: String
  locationStreet1: String
  locationStreet2: String
  locationCity: String
  locationState: String
  locationPinCode: String
  locationCountry: String
}

type retrieveOtherInfoResponse{
  errorCode: String
  message: String
  bankDetails: String
  insuranceDetails: String
}

type retrieveDocumentInfoResponse{
  errorCode: String
  message: String
  educationalInfoDetails: String
  certificateInfoDetails: String
  trainingInfoDetails: String
  documentDetails: String
  accreditationDetails: String
  drivingLicenseDetails: String
  passportDetails: String
}

type retrieveCareerInfoResponse{
  errorCode: String
  message: String
  awardDetails: String
  skillDetails: String
}

type retrieveContactInfoResponse{
  errorCode: String
  message: String
  contactDetails: String
}

type retrieveJobInfoResponse {
  errorCode: String
  message: String
  jobInfoDetails: String
  experienceDetails: String
  assetDetails: String
}

type retrievePersonalInfoResponse {
  errorCode: String
  message: String
  personalInfoDetails: String
  dependentDetails: String
}

type listInvitedIndividualsResponse {
  errorCode: String
  message: String
  listIndividuals: String
}

type retrieveVendorAdminsResponse{
  errorCode: String
  message: String
  vendorAdmin: [vendorAdmin]
}

type vendorAdmin{
  Candidate_Id: Int,
  Candidate_Name: String,
  Personal_Email: String,
  Candidate_Status: String,
  Mobile_No: String,
  Mobile_No_Country_Code: String,
  Status: String
}

type getVendorBankDetailsResponse {
  errorCode: String
  message: String
  vendorBankDetails: vendorBankDetails
}

type vendorBankDetails {
  Vendor_Id: Int
  Vendor_Bank_Id: Int
  Bank_Account_Number: String
  Bank_Name: String
  Bank_Id: Int
  Branch_Name: String
  IFSC_Code: String
  Street: String
  City: String
  State: String
  Zip: String
  Account_Type_Id: Int
  Credit_Account: String
  Beneficiary_Id: Int
  Status: String
  BSB_Code: String
}

type getDynamicFormDetailsResponse {
  errorCode: String
  message: String
  dynamicFormDetails: [dynamicFormDetails]
}

type dynamicFormDetails {
  Response_Id: Int
  Form_Template: String
  Template_Id: Int
  Template_Name: String
  Required: Int
}

type listVendorDocumentsResponse {
  errorCode: String
  message: String
  vendorDocuments: [vendorDocuments]
}

type vendorDocuments {
  Document_Id: Int
  Vendor_Id: String
  Category_Id: Int
  Category_Name: String
  Document_Type_Id: Int
  Document_Type: String
  Document_Sub_Type_Id: Int
  Document_Sub_Type: String
  Document_Name: String
  Effective_Date: String
  Mandatory: String
  Instruction: String
  End_Date: String
  File_Name: String
  Added_On: String
  Added_By: String
  Updated_On: String
  Updated_By: String
}

type listVendorEmployeesResponse {
  errorCode: String
  message: String
  vendorEmployees: [vendorEmployees]
}

type vendorEmployees {
  employeeId: Int
  userDefinedEmpId: String
  email: String
  status: String
  dateOfJoin: String
  designationName: String
  departmentName: String
  locationName: String
  employeeType: String
  managerName: String
  employeeName: String
}

type checkUrlStatusResponse {
  errorCode: String
  message: String
  vendorId: String
}

type retrieveAccreditationAndTypeResponse {
  errorCode: String
  message: String
  accreditationAndType: [accreditationAndType]
}

type accreditationAndType {
  accreditationCategoryAndTypeId: Int!
  accreditationCategory: String!
  accreditationType: String!
  Mandatory: String
  Instruction: String
}

type retrieveAccreditationDetailsResponse {
  errorCode: String
  message: String
  accreditationDetails: [accreditationDetails]
}

type accreditationDetails {
  accreditationDetailId: Int!
  accreditationCategoryAndTypeId: Int!
  accreditationCategory: String!
  accreditationType: String!
  Mandatory: String!
  Instruction:String
  fileName: String!
  receivedDate: String
  expiryDate: String
  identifier: String
  examRating: Int
  examDateYear: Int
  examDateMonth: String
  dependentId: Int
  dependentFirstName: String
  dependentLastName: String
  gender: String
  relationship: String
  dependentDob: String
}

type retrieveSuperannuationDetailsResponse {
  errorCode: String
  message: String
  superannuationDetails: [superannuationDetails]
}

type superannuationDetails {
  taxFileNumber: String!
  superannuationType: String!
  fundABN: String
  fundName: String
  fundAddress: String
  suburbOrTown: String
  stateOrTerritory: String
  postCode: String
  fundPhone: String
  fundElectronicServiceAddress: String
  uniqueSuperannuationIdentifier: String
  accountName: String
  memberNumber: String
}

type listInvitedVendorsResponse {
  errorCode: String
  message: String
  invitedVendors: [invitedVendors]
}
type listCandidateDocumentDetailsResponse {
 errorCode: String
  message: String
  documentDetails: [documentDetails]
}
type getCityListWithStateResponse {
errorCode: String
  message: String
  cityDetails: [cityDetails]
}
type getTimezoneListResponse{
errorCode: String
  message: String
  timeZoneDetails:[timeZoneDetails]
}
type timeZoneDetails{
Zone_Id: Int
Country_Code:String
Country_Name:String
TimeZone_Id:String
Offset_Time:String
Summer_Time:String
Start_Time:String
End_Time: String
Dst_Flag: Int
Dst_Id:Int
}
type cityDetails {
City_Id: Int
Country_Code: String
Locale: String
City_Name: String
Country_Name: String
State_Name:String
State_Id:Int
cityStateDetails: String
}
type documentDetails {
Document_Sub_Type_Id: Int,
Document_Sub_Type: String
Mandatory: String,
Instruction: String,
Document_Type_Id: Int
Document_Type: String
Category_Id: Int
Category_Fields: String
File_Name: String
Is_Default: Boolean
}

type invitedVendors {
  vendorId: Int
  vendorName: String
  vendorEmailId: String
  status: String
  url: String
  urlHash: String
  expireTime: String
  addedOn: String
  addedBy: String
  addedByName: String
  updatedOn: String
  updatedBy: String
  updatedByName: String
  managerId: Int
  managerName: String
  departmentId: Int
  departmentName: String
  typeOfIndustry: String
  servicePriority: String
  documentEnforcementGroups: String
}

type listOnboardedVendorsResponse {
  errorCode: String
  message: String
  onboardedVendors: [onboardedVendors]
}

type onboardedVendors {
  vendorId: Int
  vendorType: String
  vendorName: String
  businessNumber: String
  managerId: Int
  managerName: String
  departmentId: Int
  departmentName: String
  entityTrust: String
  trusteeName: String
  gstRegistered: String
  gstNumber: String
  serviceOffered: String
  typeOfIndustry: String
  servicePriority: String
  street: String
  city: String
  stateRegion: String
  zipCode: String
  country: String
  postalStreet: String
  postalCity: String
  postalStateRegion: String
  postalZipCode: String
  postalCountry: String
  telephone: String
  mobileNo: String
  mobileNoCountryCode: String
  vendorEmail: String
  noOfEmployeesWithinOrganization: Int
  noOfSubcontractors: Int
  noOfEmployeesToBeAllocatedToContract: Int
  trusteeAuthorizationDocuments: String
  contactNameOfVendor: String
  status: String
  onboardingFormId: Int
  vendorStatus: String
  performanceRatings: String
  statusLevel: String
  addedOn: String
  addedBy: String
  addedByName: String
  updatedOn: String
  updatedBy: String
}

type getVendorDetailsResponse {
  errorCode: String
  message: String
  vendorDetails: vendorDetails
}

type vendorDetails {
  vendorId: Int
  vendorType: String
  vendorName: String
  businessNumber: String
  entityTrust: String
  trusteeName: String
  gstRegistered: String
  gstNumber: String
  serviceOffered: String
  typeOfIndustry: String
  servicePriority: String
  street: String
  city: String
  stateRegion: String
  zipCode: String
  country: String
  postalStreet: String
  postalCity: String
  postalStateRegion: String
  postalZipCode: String
  postalCountry: String
  telephone: String
  mobileNo: String
  mobileNoCountryCode: String
  vendorEmail: String
  noOfEmployeesWithinOrganization: Int
  noOfSubcontractors: Int
  onboardingFormId: Int
  noOfEmployeesToBeAllocatedToContract: Int
  trusteeAuthorizationDocuments: String
  status: String
  addedOn: String
  addedBy: String
  updatedOn: String
  updatedBy: String
  urlHash: String
  contactNameOfVendor: String
  insuranceDetails: [insuranceDetails]
}

type insuranceDetails {
  vendorId: Int
  vendorName: String
  vendorType: String
  insuranceId: Int
  typeOfInsurance: String
  nameOfInsurance: String
  policyNumber: String
  nameOfInsurer: String
  nameOfInsured: String
  sumInsured: Int
  documentFile: String
  expiryDate: String
  description: String
  verification: String
  verifiedDate: String
  lastReminderDate: String
}

type designationMappingDetails {
  Designation_Id: Int
  Designation_Name: String
  Accreditation_Category_And_Type_Id: Int
  Accreditation_Category: String
  Accreditation_Type: String
  Status: String
}

type designationMappingDetailsResponse {
  errorCode: String
  message: String
  designationMappingDetails: [designationMappingDetails]
}

type projectMappingDetails {
  Project_Id: Int
  Project_Name: String
  Accreditation_Category_And_Type_Id: Int
  Accreditation_Category: String
  Accreditation_Type: String
  Status: String
}

type projectMappingDetailsResponse {
  errorCode: String
  message: String
  projectMappingDetails: [projectMappingDetails]
}
type getFormFeildsByFormIdAndTabResponse {
  errorCode: String
  message: String
  formFields:[formFields]
}
type formFields{
  Field_Id: Int,
  Form_Id: Int,
  Field_Name: String,
  Field_Alias: String,
  Field_Visiblity: String,
  Mandatory_Field: String,
  Predefined : String
  Tab_Name: String
  Min_Validation: Int
  Max_Validation: Int
  Validation_Id: Int
  Validation_Type: String
  validationDetails: validationDetails
}

type validationDetails {
  Validation_Id: Int
  Validation_Name: String
  Regular_Expression: String
  Description: String
}

type commonResponse {
  errorCode: String
  message: String
}

type getVendorInsuranceDetailsResponse {
  errorCode: String
  message: String
  insuranceDetails: [insuranceDetails]
}

type listCandidateDetailsResponse {
  errorCode: String
  message: String
  listCandidates: String
  adminEmail: [String]
}

type listEduInstitutionAndSpecializationResponse {
  errorCode: String
  message: String
  institution: [institution]
  specialization: [specialization]
}


type institution {
  Institution_Id: Int
  Institution_Code: String
  Institution: String
}

type specialization {
  Specialization_Id: Int
  Specialization_Code: String
  Specialization: String
}

type OnboardingSettingsResponse {
  errorCode: String
  message: String
  status: String
  Allow_Deployment_Notification_OnFailure: String
  Integration_Type: String
  Enable_Location_Auto_Prefill: String
}

type listTimekeepingCareerResponse {
  errorCode: String
  message: String
  timeKeeping: [timeKeepingData]
  career: [careerData]
  taxDetail: [taxDetailData]
}

type timeKeepingData {
  Timekeeping_Id: Int
  Timekeeping_Name: String
}

type careerData {
  Career_Id: Int
  Career_Name: String
}

type taxDetailData {
  Tax_Code: String
  Tax_Description: String
  Exemption_Amount: Int
}

type listdocumentEnforcementGroupResponse{
  errorCode: String
  message: String
  groupIds: [groupIdDetails]
}
type getNewHireEmployeeDetailsResponse{
  errorCode: String
  message: String
  hiringManagerDetails: String
}

type groupIdDetails {
  Group_Id: Int
  Group_Name:String
}

type listVendorDocumentDetailsResponse{
  errorCode: String
  message: String
  documentDetails: [documentDetails]
}

type getBarangayListWithCityResponse {
  errorCode: String
  message: String
  barangayDetails: [barangayDetails]
}

type barangayDetails {
  Barangay_Id: Int
  Barangay_Name: String
  Barangay_Code: String
  City_Id: Int
  City_Name: String
  State_Id: Int
  State_Name: String
  Region_Id: Int
  Region_Name: String
  Region_Code: String
  Country_Name: String
  Country_Code: String
  barangayDetails: String
}

schema {
  query: Query
}
