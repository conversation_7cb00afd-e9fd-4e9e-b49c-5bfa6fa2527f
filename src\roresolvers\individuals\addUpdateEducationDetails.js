//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tableAlias');
const { formName, systemLogs, formIds } = require('../../common/appConstants');
//Require validation
const { validateCandidateInputs } = require('../../common/commonFunctions')

//function to add / update education details
module.exports.addUpdateEducationDetails = async (parent, args, context, info) => {
    console.log('Inside addUpdateEducationDetails function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.individuals);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1 || checkRights.Role_Add === 1)) {
            const fieldValidations = {
                specialisation: "IVE0365",
                instituteName: "IVE0366",
                university: "IVE0367",
                percentage: "IVE0369",
                grade: "IVE0370",
            }
            if(args.yearOfPassing && args.yearOfPassing.length){
                fieldValidations.yearOfPassing = "IVE0368";
            }
            validationError = validateCandidateInputs(args, fieldValidations);
            if (Object.keys(validationError).length == 0) {
                let educationData = {
                    Candidate_Id: args.candidateId,
                    Education_Type: args.educationType,
                    Specialisation: args.specialisation,
                    Institute_Name: args.instituteName,
                    Specialization_Id:args.specializationId,
                    Institution_Id:args.institutionId,
                    University: args.university,
                    Year_Of_Start: args.yearOfStart,
                    Year_Of_Passing: args.yearOfPassing,
                    Percentage: args.percentage,
                    Grade: args.grade,
                    Start_Date: args.startDate,
                    End_Date: args.endDate,
                    City: args.city,
                    State: args.state,
                    Country: args.country
                }
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (args.educationId) {
                                return (
                                    organizationDbConnection(ehrTables.candidateEducation)
                                        .update(educationData)
                                        .transacting(trx)
                                        .where('Education_Id', args.educationId)
                                        .then(async (updateData) => {
                                            if (updateData) {

                                                if(args.fileName && args.documentSubTypeId) {
                                                    let documentExist = await organizationDbConnection(ehrTables.candidateEducationDocuments)
                                                    .transacting(trx)
                                                    .where('Education_Id',args.educationId)
                                                    
                                                    if(!documentExist[0]){
                                                        await organizationDbConnection(ehrTables.candidateEducationDocuments)
                                                        .insert({
                                                            Education_Id: args.educationId,
                                                            File_Name: args.fileName,
                                                            Document_Name: args.fileName,
                                                            Sub_Type_Id: args.documentSubTypeId
                                                        })
                                                        .transacting(trx)
                                                    } else{
                                                        await organizationDbConnection(ehrTables.candidateEducationDocuments)
                                                        .transacting(trx)
                                                        .update({'File_Name': args.fileName, 'Sub_Type_Id': args.documentSubTypeId, 'Document_Name': args.fileName})
                                                        .where('Education_Id', args.educationId)
                                                    }
                                                }else{
                                                   await organizationDbConnection(ehrTables.candidateEducationDocuments)
                                                        .where('Education_Id',args.educationId).del()
                                                        .transacting(trx);
                                                }
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the education details', educationData)
                                                throw 'IO0117'
                                            }
                                        })
                                )
                            } else {
                                return (
                                    organizationDbConnection(ehrTables.candidateEducation)
                                        .insert(educationData)
                                        .transacting(trx)
                                        .then(async(insertData) => {
                                            if (insertData) {
                                                if(args.fileName && args.documentSubTypeId){
                                                    await organizationDbConnection(ehrTables.candidateEducationDocuments)
                                                    .insert({'Education_Id': insertData, 'File_Name': args.fileName, 'Sub_Type_Id': args.documentSubTypeId, 'Document_Name': args.fileName})
                                                }
                                                return 'success'
                                            } else {
                                                console.log('Error while updating the education details', educationData)
                                                throw 'IO0117'
                                            }
                                        })
                                )
                            }

                        })
                        .then(async (response) => {
                            if (response) {
                                let systemLogParam = {
                                    action: systemLogs.onboard,
                                    userIp: context.User_Ip,
                                    employeeId: loginEmployeeId,
                                    formId: formIds.individuals,
                                    isEmployeeTimeZone: 0,
                                    changedData: args,
                                    organizationDbConnection: organizationDbConnection,
                                    uniqueId: args.candidateId,
                                    message: `The candidate education details has been ${args.educationId ? 'updated' : 'added'}.`
                                };

                                // Call the function to add the system log
                                await commonLib.func.createSystemLogActivities(systemLogParam);

                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Candidate Education details has been added/updated successfully." };
                            } else {
                                throw 'IO0117'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateEducationDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'IO0117');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )

            } else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add / update the education details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateEducationDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateEducationDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'IO0016');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}