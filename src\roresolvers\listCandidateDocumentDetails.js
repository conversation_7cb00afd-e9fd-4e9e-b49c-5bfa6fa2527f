//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');

//fuction to list candidate document
let organizationDbConnection;
module.exports.listCandidateDocumentDetails = async (parent, args, context, info) => {
    console.log('Inside listCandidateDocumentDetails function', args);
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
            organizationDbConnection('candidate_url')
            .select("Candidate_Id").where('Url_Hash', args.urlHash)
            .then(async (url) => {
                if(!url[0]){
                    throw 'REO0101';
                } else {

                    let documentSubTypeList = await organizationDbConnection(ehrTables.candidateDocumentEnforcementGroups + ' as CDEG')
                    .select('DSEG.Document_Sub_Type_Id') .where('CDEG.Candidate_Id', url[0].Candidate_Id)
                    .innerJoin(ehrTables.documentSubTypeEnforcementGroups + ' as DSEG', 'DSEG.Group_Id', 'CDEG.Group_Id');
                    documentSubTypeList = documentSubTypeList && documentSubTypeList.length ? documentSubTypeList.map(subType=> subType.Document_Sub_Type_Id) : []
                    return (
                        organizationDbConnection(ehrTables.documentSubType+ " as DST")
                        .where('DST.Mandatory','Yes')
                        .leftJoin(ehrTables.documentType + " as DT", "DT.Document_Type_Id", "DST.Document_Type_Id")
                        .leftJoin(ehrTables.documentCategory + " as DC", "DC.Category_Id", "DT.Category_Id")  
                        .where(qb => {    
                            if(documentSubTypeList && documentSubTypeList.length)
                                qb.whereIn('DST.Document_Sub_Type_Id', documentSubTypeList)
                        })
                        
                        .then((data) => {
                            if (data && data.length > 0) {
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "candidate document data has been fetched successfully.", documentDetails: data };
                            }
                            else {
                                return { errorCode: "", message: "Does not have candidate document details.", documentDetails: [] };
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in listCandidateDocumentDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'CCH0010');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                    )
                }
            }) .catch((catchError) => {
                console.log('Error in UrlHash .catch() block', catchError);
                let errResult = commonLib.func.getError(catchError, 'CCH0010');
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                throw new ApolloError(errResult.message, errResult.code);
            })
        )
       
    }
    
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listCandidateDocumentDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0011');
        throw new ApolloError(errResult.message, errResult.code)
    }
}