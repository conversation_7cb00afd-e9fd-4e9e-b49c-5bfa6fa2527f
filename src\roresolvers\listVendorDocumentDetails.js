//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../common/tableAlias');

//function to list vendor document
let organizationDbConnection;
module.exports.listVendorDocumentDetails = async (parent, args, context, info) => {
    console.log('Inside listVendorDocumentDetails function', args);
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        //Step 1: Get the Sub Document Type Ids 
        let documentSubTypeList = await organizationDbConnection(ehrTables.vendorDocumentEnforcementGroups + ' as VDEG')
            .select('DSEG.Document_Sub_Type_Id').where('VDEG.Vendor_Id', args.vendorId)
            .innerJoin(ehrTables.documentSubTypeEnforcementGroups + ' as DSEG', 'DSEG.Group_Id', 'VDEG.Group_Id');

        if (documentSubTypeList && documentSubTypeList.length) {
            documentSubTypeList = documentSubTypeList.map(subType => subType.Document_Sub_Type_Id)
        }

        //Step 2: Get the document details based on Vendor_Based and Mandatory
        let mandatoryDocumentDetails = await organizationDbConnection(ehrTables.documentSubType + " as DST")
            .leftJoin(ehrTables.documentType + " as DT", "DT.Document_Type_Id", "DST.Document_Type_Id")
            .leftJoin(ehrTables.documentCategory + " as DC", "DC.Category_Id", "DT.Category_Id")
            .where('DC.Vendor_Based', 1)
            .where('DST.Mandatory', 'Yes')
            .where(qb => {
                if (documentSubTypeList && documentSubTypeList.length)
                    qb.whereIn('DST.Document_Sub_Type_Id', documentSubTypeList)
            })

        //Step 3: Return the document details
        return { errorCode: "", message: "Vendor document data has been fetched successfully.", documentDetails: mandatoryDocumentDetails };

    }
    catch (e) {
        console.log('Error in listVendorDocumentDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'VO0136');
        throw new ApolloError(errResult.message, errResult.code)
    }
    finally {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}