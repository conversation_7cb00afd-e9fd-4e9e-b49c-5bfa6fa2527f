const resolvers = {
  Query: {
    sendUrlToCandidate: async (root, args) => {
      var dbConnection = require('./dbConnection');
      var connection = await dbConnection.getConnection(args.Org_Code);
      var cName = await dbConnection.getCompanyName(args.Org_Code);
      const knexconfig = require('knex')(connection);
      var { ApolloError } = require('apollo-server-lambda');
      // require retrieves3FileUrl to get report logo
      const getS3Path = require('./formS3ObjectUrl');
      // Get organization report logo from org_details
      var reportLogoFileUrl = await getS3Path.getReportLogo(args.Org_Code, knexconfig);
      return knexconfig.transaction(function (trc) {
        return knexconfig('candidate_url')
          .where('Url', args.Url_Hash)
          .then(async (url) => {
            if (!url[0]) {
              throw new Error('REO0101')
            } else {
              const AWS = require("aws-sdk");
              AWS.config.update({
                region: process.env.sesTemplatesRegion
              });
              const ses = new AWS.SES({
                apiVersion: "2010-12-01"
              });
              let buttonColors=await commonLib.func.getButtonColor(organizationDbConnection);
              const bucketName = process.env.documentsBucket
              const region=process.env.region;
              let fileNameString=await commonLib.func.getDocumentSubType(organizationDbConnection,args.Org_Code,bucketName,region);
              const templateData = {
                employeeName: args.Name,
                redirectionUrl: args.Url_Hash,
                passCode: url[0].Pin,
                name: args.Name,
                orgLogo: reportLogoFileUrl,
                topCardImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + '/Email-Template-Images/topCard.png',
                domainName: process.env.domainName.toUpperCase(),
                domainContentInSubject: "Hello",
                registrationImage: 'https://s3.' + process.env.region + '.amazonaws.com/' + process.env.logoBucket + '/Email-Template-Images/RegistrationImage.png',
                notificationSubject: "Invitation to join " + cName,
                isEmailClient: 0,
                isVendorBased: 0,
                isCandidate: 1,
                orgCode: cName?cName:'',
                bgColors:buttonColors?buttonColors:'',
                docContent:fileNameString?'Below is the link to the list of pre-employment requirements and the hiring forms you need to accomplish.':'',
                docLink:fileNameString?fileNameString:'',
              }

              const params = {
                "Source": process.env.emailFrom,
                "Template": "welcomeEmailToClient",
                "Destination": {
                  "ToAddresses": [args.MailTo]
                },
                "ReplyToAddresses": [process.env.emailReplyTo],
                "TemplateData": JSON.stringify(templateData)
              }

              try {
                let response = await ses.sendTemplatedEmail(params).promise()
                console.log('response', response)

                if (response.ResponseMetadata) {
                  console.log("Mail has sent");
                  return knexconfig('candidate_sent_mails')
                    .insert({
                      Url_Hash: args.Url_Hash,
                      Name: args.Name,
                      Email: args.MailTo
                    }).then(() => {
                      return {
                        message: "Mail has sent"
                      }
                    })

                } else {
                  console.log("Mail hasn't sent");
                  throw new Error("Mail hasn't sent, Please try again later")
                }
              }
              catch (err) {
                console.log("err", err);
                throw new Error("Mail hasn't sent, Please try again later")
              }
            }
          }).then(trc.commit)
          .catch(trc.rollback);
      }).then(function (result) {
        console.log('return successful response from sendUrlToCandidate');
        return result;
      }).catch(function (err) {
        console.log('Error in sendUrlToCandidate', err);
        if (err.message == "REO0101") {
          console.log('URL not found')
          throw new ApolloError("URL not found", "REO0101")
        } else if (err.message == "Mail hasn't sent, Please try again later") {
          throw new Error(err.message)
        } else {
          throw new Error('Something went wrong')
        }
      }).finally(() => {
        knexconfig.destroy();
      })
    }
  },
}
exports.resolvers = resolvers;