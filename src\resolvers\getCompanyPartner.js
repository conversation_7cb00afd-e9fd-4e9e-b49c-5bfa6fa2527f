const resolvers = {
    Query : {
        getCompanyPartner : async (root,args) =>{
            let appManagerDbConnection;
            try {
                const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
                let amconnection = await commonLib.stepFunctions.getConnection(process.env.stageName, process.env.dbPrefix, process.env.secretName, process.env.region, args.Org_Code, 1, {});
                if (Object.keys(amconnection).length === 0) {
                    throw new Error('Error occurred while getting apmangagerdb connection');
                }
                let appManagerDbConnection = require('knex')(amconnection.AppManagerDb)
                var partnerId = await commonLib.func.getPartnerIntegration(appManagerDbConnection, args.Org_Code);
                appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                return { errorCode: "", message:"Successfully retrieved partner details.", partnerIntegration: partnerId}
            } catch(err){
                console.error('Error in getCompanyPartner() main catch block', err);
                appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                throw new Error('Something went wrong');
            }
        }
    },
}
exports.resolvers = resolvers;