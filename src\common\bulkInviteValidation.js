//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

//Function to validate form details

module.exports.bulkInviteValidation = async (args) => {
    console.log('Inside bulkInviteValidation function');
    try {
        let validationError = {};
        //Validate the vendor id
        if (!(args.vendorId || commonLib.commonValidation.numberValidation(args.vendorId))) {
            validationError['IVE0000'] = commonLib.func.getError('', 'IVE0000').message;
            throw '_EC0007'
            }

        //validating the employee data
        if(args.employeeData){
            for(let i=0; i<args.employeeData.length; i++){
            if(!commonLib.commonValidation.numberValidation(args.employeeData[i].Designation_Id)){
                validationError['IVE0249'] = commonLib.func.getError('', 'IVE0249').message1;
            }
            else if(!commonLib.commonValidation.numberValidation(args.employeeData[i].Department_Id)){
                validationError['IVE0249'] = commonLib.func.getError('', 'IVE0249').message2;
            }
            else if(!commonLib.commonValidation.numberValidation(args.employeeData[i].Location_Id)){
                validationError['IVE0249'] = commonLib.func.getError('', 'IVE0249').message3;
            }
            else if(!commonLib.commonValidation.numberValidation(args.employeeData[i].EmpType_Id)){
                validationError['IVE0249'] = commonLib.func.getError('', 'IVE0249').message4;
            }
            else if(!commonLib.commonValidation.numberValidation(args.employeeData[i].Manager_Id)){
                validationError['IVE0249'] = commonLib.func.getError('', 'IVE0249').message5;
            }
            else if(!commonLib.commonValidation.numberValidation(args.employeeData[i].Work_Schedule)){
                validationError['IVE0249'] = commonLib.func.getError('', 'IVE0249').message6;
            }
        }
        }else{
            validationError['IVE0001'] = commonLib.func.getError('', 'IVE0001').message1;
        }

        return validationError;
    }
    catch (err) {
        console.log('Error in the bulkInviteValidation() function in the main catch block.', err);
        throw err;
    }
}
