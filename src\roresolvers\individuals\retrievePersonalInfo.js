// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files

module.exports.retrievePersonalInfo = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrievePersonalInfo function.");
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const data = await getPersonalInfo(organizationDbConnection, args);
        if (data) {
            const { personalInfoWithLanguages, dependentDetails } = data;
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return {
                errorCode: "",
                message: "Candidate personal info details retrieved successfully.",
                personalInfoDetails: JSON.stringify(personalInfoWithLanguages),
                dependentDetails: JSON.stringify(dependentDetails),
            };
        } else {
            throw 'IO0102'
        }
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrievePersonalInfo function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'IO0002');
        throw new ApolloError(errResult.message, errResult.code);
    }
};

async function getPersonalInfo(organizationDbConnection, args) {
    try {
        const [personalInfoDetails, languageDetails, dependentDetails] = await Promise.all([
            organizationDbConnection(ehrTables.candidatePersonalInfo + " as EP")
                .select("EP.*", "EJ.User_Defined_EmpId", 'g.Gender as Gender_Name_Table', "EJ.External_EmpId", "EJ.Pf_PolicyNo", "MS.Marital_Status as maritalStatusName", "GE.Gender_Expression", "GI.Gender_Identity")
                .leftJoin(ehrTables.maritalStatus + " as MS", "MS.Marital_Status_Id", "EP.Marital_Status")
                .leftJoin(ehrTables.candidateJob + " as EJ", "EJ.Candidate_Id", "EP.Candidate_Id")
                .leftJoin('gender' + " as g", "g.Gender_Id", "EP.Gender_Id")
                .leftJoin(ehrTables.genderExpression + " as GE", "GE.Gender_Expression_Id", "EP.Gender_Expression_Id")
                .leftJoin(ehrTables.genderIdentity + " as GI", "GI.Gender_Identity_Id", "EP.Gender_Identity_Id")
                .where('EP.Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidateLanguage + " as EL")
                .select("L.Lang_Id", "L.Language_Name", "EL.*")
                .leftJoin(ehrTables.languages + " as L", "L.Lang_Id", "EL.Lang_Known")
                .where('EL.Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidateDependent + ' as CD')
                .select("CD.*")
                .where('CD.Candidate_Id', args.candidateId),

            organizationDbConnection(ehrTables.candidatePassport + " as EP")
                .select("EP.*", "C.Country_Name")
                .leftJoin(ehrTables.country + " as C", "C.Country_Code", "EP.Issuing_Country")
                .where('Candidate_Id', args.candidateId)
        ]);
        const personalInfoWithLanguages = {
            ...personalInfoDetails[0],
            Languages: languageDetails
        };

        return {
            personalInfoWithLanguages,
            dependentDetails,
        };
    } catch (err) {
        console.log('Error in getPersonalInfo function', err);
        throw err
    }
}
